<template>
  <div class="data-import-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>基础数据导入</span>
        </div>
      </template>
      
      <!-- 导入设置 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">导入设置</div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="导入类型:">
            <el-select v-model="importForm.importType" placeholder="请选择导入类型" style="width: 100%">
              <el-option
                v-for="type in importTypeOptions"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据来源:">
            <el-radio-group v-model="importForm.dataSource">
              <el-radio label="local">本地文件</el-radio>
              <el-radio label="network">网络地址</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="文件格式:">
            <el-select v-model="importForm.fileFormat" placeholder="请选择文件格式" style="width: 100%">
              <el-option label="Excel文件" value="excel" />
              <el-option label="CSV文件" value="csv" />
              <el-option label="JSON文件" value="json" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 文件选择 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">文件选择</div>
        </el-col>
        <el-col :span="24">
          <el-form-item label="选择文件:">
            <el-upload
              class="upload-demo"
              action=""
              :auto-upload="false"
              :on-change="handleFileChange"
              :show-file-list="false"
            >
              <el-button type="primary">选择文件</el-button>
            </el-upload>
            <div class="file-info" v-if="fileName">
              <p>文件名: {{ fileName }}</p>
              <p>文件大小: {{ fileSize }}</p>
              <p>文件格式: {{ fileFormat }}</p>
            </div>
            <div v-else class="no-file">
              <p>未选择文件</p>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="模板下载:">
            <el-button type="primary" link @click="downloadTemplate">下载导入模板</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 导入模板说明 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">导入模板说明</div>
        </el-col>
        <el-col :span="24">
          <el-card class="info-card">
            <div class="info-title">{{ importForm.importType === 'material' ? '物料基础库' : '其他' }}导入模板说明:</div>
            <div class="info-content">
              <p v-if="importForm.importType === 'material'">1. 必填字段: 公司物料编码、物料名称、物料分类、单位</p>
              <p v-if="importForm.importType === 'material'">2. 可选字段: 型号、规格、甲方编码</p>
              <p v-if="importForm.importType === 'material'">3. 物料分类: 甲料、乙料、商品、辅料</p>
              <p v-if="importForm.importType === 'material'">4. 一个公司物料编码可对应多个甲方编码(用逗号分隔)</p>
              <p v-if="importForm.importType === 'material'">5. 支持格式: .xls、.xlsx</p>
              <p v-if="importForm.importType === 'material'">&nbsp;</p>
              <p v-if="importForm.importType === 'material'">字段示例:</p>
              <p v-if="importForm.importType === 'material'">公司物料编码  甲方编码    物料分类  物料名称  型号  规格  单位</p>
              <p v-if="importForm.importType === 'material'">WL001        JD001,JD002  甲料      电缆线    YJV   3*4mm² 米</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 数据预览 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">数据预览</div>
        </el-col>
        <el-col :span="24">
          <el-table :data="previewData" border class="preview-table" height="300">
            <el-table-column prop="rowNumber" label="行号" width="60" />
            <el-table-column prop="materialCode" label="公司物料编码" min-width="120" />
            <el-table-column prop="partyCode" label="甲方编码" min-width="120" />
            <el-table-column prop="materialCategory" label="物料分类" min-width="100" />
            <el-table-column prop="materialName" label="物料名称" min-width="120" />
            <el-table-column prop="model" label="型号" min-width="100" />
            <el-table-column prop="specification" label="规格" min-width="100" />
            <el-table-column prop="unit" label="单位" min-width="80" />
            <el-table-column prop="status" label="状态" min-width="80">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'valid' ? 'success' : 'danger'">
                  {{ scope.row.status === 'valid' ? '有效' : '无效' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="errorMessage" label="错误信息" min-width="120" />
          </el-table>
        </el-col>
      </el-row>
      
      <!-- 导入统计 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">导入统计</div>
        </el-col>
        <el-col :span="24">
          <el-card class="statistics-card">
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="statistic-item">
                  <span class="label">总记录数:</span>
                  <span>{{ importStatistics.totalRecords }}条</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="statistic-item">
                  <span class="label">有效记录:</span>
                  <span>{{ importStatistics.validRecords }}条</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="statistic-item">
                  <span class="label">无效记录:</span>
                  <span>{{ importStatistics.invalidRecords }}条</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="statistic-item">
                  <span class="label">重复记录:</span>
                  <span>{{ importStatistics.duplicateRecords }}条 (将被跳过)</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 导入选项 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">导入选项</div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="导入模式:">
            <el-select v-model="importForm.importMode" placeholder="请选择导入模式" style="width: 100%">
              <el-option label="增量导入" value="incremental" />
              <el-option label="全量导入" value="full" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="import-mode-description">
            <p v-if="importForm.importMode === 'incremental'">增量导入: 只导入新数据，跳过重复数据</p>
            <p v-else>全量导入: 清空原数据，导入新数据(谨慎操作)</p>
          </div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重复处理:">
            <el-radio-group v-model="importForm.duplicateHandling">
              <el-radio label="skip">跳过重复项</el-radio>
              <el-radio label="overwrite">覆盖重复项</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="冲突处理:">
            <el-radio-group v-model="importForm.conflictHandling">
              <el-radio label="stop">停止导入</el-radio>
              <el-radio label="continue">继续导入</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button type="primary" @click="previewDataFunc">预览数据</el-button>
        <el-button type="success" @click="startImport">开始导入</el-button>
        <el-button @click="saveImportResult">保存导入结果</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
      
      <!-- 导入进度 -->
      <el-row :gutter="20" class="form-section" v-if="showProgress">
        <el-col :span="24">
          <div class="section-title">导入进度</div>
        </el-col>
        <el-col :span="24">
          <el-card class="progress-card">
            <el-progress 
              :percentage="importProgress" 
              :stroke-width="20" 
              striped 
              striped-flow 
              :duration="importProgress < 100 ? 5 : 0"
            />
            <div class="progress-info">
              <p>已导入: {{ importedCount }}条/{{ importStatistics.validRecords }}条</p>
              <p>预计剩余时间: {{ remainingTime }}</p>
              <p>当前状态: {{ importStatus }}</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 导入结果 -->
      <el-row :gutter="20" class="form-section" v-if="showResult">
        <el-col :span="24">
          <div class="section-title">导入结果</div>
        </el-col>
        <el-col :span="24">
          <el-card class="result-card">
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="result-item">
                  <span class="label">导入完成时间:</span>
                  <span>{{ importCompletionTime }}</span>
                </div>
              </el-col>
              <el-col :span="24">
                <div class="result-item">
                  <span class="label">成功导入:</span>
                  <span>{{ importResult.successCount }}条</span>
                </div>
              </el-col>
              <el-col :span="24">
                <div class="result-item">
                  <span class="label">导入失败:</span>
                  <span>{{ importResult.failureCount }}条</span>
                </div>
              </el-col>
              <el-col :span="24">
                <div class="result-item">
                  <span class="label">重复跳过:</span>
                  <span>{{ importResult.skipCount }}条</span>
                </div>
              </el-col>
            </el-row>
            
            <el-divider v-if="importResult.failureCount > 0" />
            
            <div v-if="importResult.failureCount > 0" class="failure-details">
              <div class="info-title">失败记录详情:</div>
              <div 
                v-for="(error, index) in importResult.failureDetails" 
                :key="index" 
                class="failure-item"
              >
                {{ error }}
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="24" v-if="importResult.failureCount > 0">
          <div class="form-actions">
            <el-button @click="downloadFailedRecords">下载失败记录</el-button>
            <el-button @click="reimportFailedRecords">重新导入失败记录</el-button>
            <el-button type="primary" @click="finishImport">完成</el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

// 导入表单
const importForm = reactive({
  importType: 'material',
  dataSource: 'local',
  fileFormat: 'excel',
  importMode: 'incremental',
  duplicateHandling: 'skip',
  conflictHandling: 'stop'
})

// 导入类型选项
const importTypeOptions = [
  { value: 'material', label: '物料基础库' },
  { value: 'employee', label: '员工信息' },
  { value: 'workType', label: '工种设置' },
  { value: 'supplier', label: '供应商信息' },
  { value: 'customer', label: '客户信息' },
  { value: 'system', label: '系统参数' }
]

// 文件信息
const fileName = ref('')
const fileSize = ref('')
const fileFormat = ref('')

// 数据预览
const previewData = ref([
  {
    rowNumber: 1,
    materialCode: 'WL001',
    partyCode: 'JD001,JD002',
    materialCategory: '甲料',
    materialName: '电缆线',
    model: 'YJV-3*4',
    specification: '3*4mm²',
    unit: '米',
    status: 'valid',
    errorMessage: '无'
  },
  {
    rowNumber: 2,
    materialCode: 'WL002',
    partyCode: 'JD003',
    materialCategory: '商品',
    materialName: '开关面板',
    model: 'KP-86',
    specification: '86型',
    unit: '个',
    status: 'valid',
    errorMessage: '无'
  },
  {
    rowNumber: 3,
    materialCode: 'WL003',
    partyCode: '',
    materialCategory: '乙料',
    materialName: '电线',
    model: 'BV-2.5',
    specification: '2.5mm²',
    unit: '米',
    status: 'valid',
    errorMessage: '无'
  },
  {
    rowNumber: 4,
    materialCode: 'WL004',
    partyCode: 'JD004',
    materialCategory: '辅料',
    materialName: '灯具',
    model: 'LED-12W',
    specification: '12W',
    unit: '个',
    status: 'valid',
    errorMessage: '无'
  },
  {
    rowNumber: 5,
    materialCode: 'WL005',
    partyCode: 'JD005',
    materialCategory: '其他',
    materialName: '插座',
    model: 'ZP-86',
    specification: '86型',
    unit: '个',
    status: 'invalid',
    errorMessage: '物料分类错误'
  }
])

// 导入统计
const importStatistics = reactive({
  totalRecords: 100,
  validRecords: 95,
  invalidRecords: 5,
  duplicateRecords: 2
})

// 导入进度
const showProgress = ref(false)
const importProgress = ref(0)
const importedCount = ref(0)
const remainingTime = ref('00:02:30')
const importStatus = ref('正在导入...')

// 导入结果
const showResult = ref(false)
const importCompletionTime = ref('2024-01-15 15:30:25')
const importResult = reactive({
  successCount: 95,
  failureCount: 5,
  skipCount: 2,
  failureDetails: [
    '1. 行4: 物料编码不能为空',
    '2. 行5: 物料分类错误',
    '3. 行15: 单位字段超长',
    '4. 行28: 物料名称包含非法字符',
    '5. 行67: 规格字段格式错误'
  ]
})

// 文件变更处理
const handleFileChange = (file: any) => {
  fileName.value = file.name
  fileSize.value = formatFileSize(file.size)
  fileFormat.value = getFileFormat(file.name)
  ElMessage.success('文件选择成功')
}

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  }
}

// 获取文件格式
const getFileFormat = (filename: string) => {
  const ext = filename.split('.').pop()?.toLowerCase()
  const formatMap: any = {
    'xls': 'Excel文件',
    'xlsx': 'Excel文件',
    'csv': 'CSV文件',
    'json': 'JSON文件'
  }
  return formatMap[ext] || '未知'
}

// 下载模板
const downloadTemplate = () => {
  ElMessage.success('下载导入模板')
}

// 预览数据
const previewDataFunc = () => {
  ElMessage.success('预览数据')
  console.log('预览数据:', importForm)
}

// 开始导入
const startImport = () => {
  ElMessage.success('开始导入')
  showProgress.value = true
  
  // 模拟导入进度
  const timer = setInterval(() => {
    importProgress.value += 5
    importedCount.value = Math.floor(importProgress.value * importStatistics.validRecords / 100)
    
    if (importProgress.value >= 100) {
      clearInterval(timer)
      importStatus.value = '导入完成'
      setTimeout(() => {
        showProgress.value = false
        showResult.value = true
      }, 1000)
    }
  }, 300)
}

// 保存导入结果
const saveImportResult = () => {
  ElMessage.success('保存导入结果')
}

// 取消
const handleCancel = () => {
  ElMessage.info('已取消')
}

// 下载失败记录
const downloadFailedRecords = () => {
  ElMessage.success('下载失败记录')
}

// 重新导入失败记录
const reimportFailedRecords = () => {
  ElMessage.success('重新导入失败记录')
}

// 完成导入
const finishImport = () => {
  ElMessage.success('导入完成')
  showResult.value = false
}

// 监听导入类型变化
watch(() => importForm.importType, (newType) => {
  ElMessage.success(`切换到${importTypeOptions.find(item => item.value === newType)?.label}导入`)
})
</script>

<style lang="scss" scoped>
.data-import-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
    
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #606266;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
    
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  
  .file-info {
    margin-top: 10px;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
    
    p {
      margin: 5px 0;
      color: #606266;
    }
  }
  
  .no-file {
    margin-top: 10px;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
    color: #909399;
  }
  
  .preview-table {
    margin-top: 10px;
  }
  
  .info-card {
    background-color: #f0f9ff;
    border-color: #b3e0ff;
    
    .info-title {
      font-weight: bold;
      color: #409eff;
      margin-bottom: 10px;
    }
    
    .info-content {
      p {
        margin: 5px 0;
        color: #606266;
      }
    }
  }
  
  .statistics-card {
    background-color: #f0f9ff;
    border-color: #b3e0ff;
    
    .statistic-item {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      
      .label {
        font-weight: bold;
        color: #409eff;
      }
    }
  }
  
  .import-mode-description {
    margin-top: 10px;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
    color: #606266;
  }
  
  .progress-card {
    background-color: #f0f9ff;
    border-color: #b3e0ff;
    
    .progress-info {
      margin-top: 15px;
      
      p {
        margin: 5px 0;
        color: #606266;
      }
    }
  }
  
  .result-card {
    background-color: #f0f9ff;
    border-color: #b3e0ff;
    
    .result-item {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      
      .label {
        font-weight: bold;
        color: #409eff;
      }
    }
    
    .failure-details {
      margin-top: 15px;
      
      .info-title {
        font-weight: bold;
        color: #409eff;
        margin-bottom: 10px;
      }
      
      .failure-item {
        margin-bottom: 5px;
        color: #f56c6c;
      }
    }
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
    }
  }
}
</style>