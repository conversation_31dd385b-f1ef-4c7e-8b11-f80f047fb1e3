<template>
  <div class="material-inbound">
    <el-card class="main-card">
      <!-- 入库信息 -->
      <div class="form-section">
        <el-form :model="formData" label-width="100px">
          <el-form-item label="单号：" required>
            <el-input v-model="formData.documentNumber" placeholder="请输入单号" style="width: 300px;" />
          </el-form-item>
        </el-form>
      </div>

      <!-- 已选物料列表 -->
      <el-button type="primary" @click="openMaterialDialog" style="margin-bottom: 16px;">选择物料</el-button>
      <el-button @click="clearSelectedMaterials" style="margin-left: 8px; margin-bottom: 16px;">清空</el-button>
      <el-table :data="selectedMaterials" border style="width: 100%; margin-bottom: 16px;">
        <el-table-column prop="companyCode" label="物料编码" width="200" />
        <el-table-column prop="name" label="物料名称" min-width="150" />
        <el-table-column prop="clientCodes" label="甲料编码" width="150" />
        <el-table-column prop="specification" label="规格" min-width="120" />
        <el-table-column prop="stockQuantity" label="库存" width="100" />
        <el-table-column prop="unit" label="单位" width="90" />
        <el-table-column label="入库数量" width="150">
          <template #default="scope">
            <el-input-number v-model="scope.row.quantity" :min="1" style="width: 100%;" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template #default="scope">
            <el-button type="danger" link @click="removeMaterial(scope.$index)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-form-item label="备注：" style="margin-bottom: 16px;">
        <el-input v-model="globalRemark" type="textarea" :rows="2" placeholder="请输入备注，所有物料共用" />
      </el-form-item>
      <div class="form-actions">
        <el-button type="primary" @click="handleSubmit" :disabled="selectedMaterials.length === 0">提交</el-button>
        <el-button @click="handleReset">重置</el-button>
      </div>
    </el-card>

    <!-- 物料选择组件 -->
    <MaterialSelector
      v-model="materialDialogVisible"
      :title="`选择物料 (已选: ${selectedMaterials.length})`"
      category="甲料"
      :selected-ids="selectedMaterialIds"
      :disable-category-switch="true"
      @selection-change="handleMaterialSelectionChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import request from '@/utils/request'
import { ElMessage } from 'element-plus'
import MaterialSelector from '@/components/MaterialSelector.vue'

// 表单数据
const formData = reactive({
  documentNumber: ''
})

// 物料弹窗控制
const materialDialogVisible = ref(false)
const selectedMaterials = ref([])
const globalRemark = ref('')

// 计算已选物料的ID列表
const selectedMaterialIds = computed(() => {
  const ids = selectedMaterials.value.map(m => m.id)
  console.log('=== 计算selectedMaterialIds ===')
  console.log('selectedMaterials.value.length:', selectedMaterials.value.length)
  console.log('selectedMaterials.value:', selectedMaterials.value)
  console.log('计算出的IDs:', ids)
  return ids
})

const SELECTED_MATERIALS_KEY = 'material_inbound_selected_materials'

// 页面加载时恢复已选物料
onMounted(() => {
  const saved = localStorage.getItem(SELECTED_MATERIALS_KEY)
  if (saved) {
    try {
      selectedMaterials.value = JSON.parse(saved)
    } catch {}
  }
})
// 监听selectedMaterials变化，自动保存
watch(selectedMaterials, (val) => {
  localStorage.setItem(SELECTED_MATERIALS_KEY, JSON.stringify(val))
}, { deep: true })

const openMaterialDialog = () => {
  console.log('打开物料选择器，当前已选物料数量:', selectedMaterials.value.length)
  console.log('当前已选物料ID:', selectedMaterialIds.value)
  console.log('当前已选物料详情:', selectedMaterials.value)
  materialDialogVisible.value = true
}



// 处理物料选择变化（实时同步到入库列表）
const handleMaterialSelectionChange = (materials: any[]) => {
  console.log('=== handleMaterialSelectionChange 开始 ===')
  console.log('接收到的materials数量:', materials?.length || 0)
  console.log('接收到的materials:', materials)
  console.log('当前selectedMaterials.value:', selectedMaterials.value)
  
  // 实时同步选择的物料到入库列表
  const newSelectedMaterials = materials.map(m => ({
    ...m,
    quantity: selectedMaterials.value.find(existing => existing.id === m.id)?.quantity || 1,
    remarks: selectedMaterials.value.find(existing => existing.id === m.id)?.remarks || ''
  }))
  
  selectedMaterials.value = newSelectedMaterials
  
  console.log('同步后的selectedMaterials.value:', selectedMaterials.value)
  console.log('=== handleMaterialSelectionChange 结束 ===')
}
const removeMaterial = (idx: number) => {
  const removedMaterial = selectedMaterials.value[idx]
  selectedMaterials.value.splice(idx, 1)
  console.log('从入库列表移除物料:', removedMaterial?.name)
  
  // 注意：这里移除后，selectedMaterialIds会自动更新，
  // 下次打开物料选择器时会正确反映当前状态
}

const handleSubmit = async () => {
  if (!formData.documentNumber.trim()) {
    ElMessage.error('请输入单号')
    return
  }
  
  if (selectedMaterials.value.length === 0) return
  // 校验数量
  for (const m of selectedMaterials.value) {
    if (!m.quantity || m.quantity <= 0) {
      ElMessage.error('请填写所有物料的入库数量')
      return
    }
  }
  // 构造请求数据
  const items = selectedMaterials.value.map(m => ({
    id: m.id,
    quantity: m.quantity
  }))
  try {
    const res = await request.post('/api/materials/inbound', {
      items,
      documentNumber: formData.documentNumber,
      remark: globalRemark.value
    })
    if (res && res.success) {
      ElMessage.success('入库成功')
      selectedMaterials.value = []
      globalRemark.value = ''
      formData.documentNumber = ''
      localStorage.removeItem(SELECTED_MATERIALS_KEY)
    } else {
      ElMessage.error(res.message || '入库失败')
    }
  } catch (e) {
    ElMessage.error('入库失败')
  }
}
const handleReset = () => {
  selectedMaterials.value = []
  globalRemark.value = ''
  formData.documentNumber = ''
  localStorage.removeItem(SELECTED_MATERIALS_KEY)
}

const clearSelectedMaterials = () => {
  const count = selectedMaterials.value.length
  selectedMaterials.value = []
  localStorage.removeItem(SELECTED_MATERIALS_KEY)
  console.log(`清空了 ${count} 个物料`)
  ElMessage.success('已清空所有物料')
}
</script>

<style scoped lang="scss">
.material-inbound {
  padding: 0;
  margin: 0;
  
  .breadcrumb {
    margin-bottom: 20px;
  }
  
  .main-card {
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #606266;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
    }
  }
}
</style>
