# SQLite修复总结

## 问题描述

在Excel导入过程中遇到两个主要错误：

1. **字段不存在错误**：`SQLITE_ERROR: table loose_orders has no column named address`
2. **函数不支持错误**：`SQLITE_ERROR: no such function: NOW`

## 修复内容

### 1. 删除不存在的数据库字段

从所有SQL语句中删除了以下不存在的字段：
- `address`
- `appeal_description` 
- `total_amount`
- `remarks`

#### 修改的文件和函数：

**`looseOrdersController.js`**

1. **`importExcel`函数**：
   - 删除SQL INSERT语句中的不存在的字段
   - 删除参数数组中的对应参数
   - 删除变量定义中的相关变量

2. **`createLooseOrder`函数**：
   - 删除SQL INSERT语句中的不存在的字段
   - 删除参数数组中的对应参数
   - 删除请求体解构中的相关字段

3. **`updateLooseOrder`函数**：
   - 删除SQL UPDATE语句中的不存在的字段
   - 删除参数数组中的对应参数
   - 删除请求体解构中的相关字段

### 2. 修复时间戳处理

将数据库函数`CURRENT_TIMESTAMP`替换为服务器端生成的时间戳：

### 3. 实现批量插入功能

将逐行插入改为批量插入，提高导入性能：

#### 修改位置：

1. **`importExcel`函数**：
   ```javascript
   // 修改前
   VALUES (..., CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
   
   // 修改后  
   const now = new Date().toISOString();
   VALUES (..., ?, ?)
   // 参数: [..., now, now]
   ```

2. **`createLooseOrder`函数**：
   ```javascript
   // 修改前
   VALUES (..., 'pending', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
   
   // 修改后
   const now = new Date().toISOString();
   VALUES (..., ?, ?, ?)
   // 参数: [..., 'pending', now, now]
   ```

3. **`updateLooseOrder`函数**：
   ```javascript
   // 修改前
   updated_at = CURRENT_TIMESTAMP
   
   // 修改后
   const now = new Date().toISOString();
   updated_at = ?
   // 参数: [..., now, id]
   ```

4. **`assignOrder`函数**：
   ```javascript
   // 修改前
   updated_at = CURRENT_TIMESTAMP
   
   // 修改后
   const now = new Date().toISOString();
   updated_at = ?
   // 参数: [..., now, id]
   ```

#### 批量插入实现：

**`importExcel`函数**：
```javascript
// 修改前：逐行插入
for (let i = 0; i < rows.length; i++) {
    // 处理单行数据
    await execute(sql, insertParams);
}

// 修改后：批量插入
const batchInsertData = []; // 收集数据数组

// 第一轮循环：收集数据
for (let i = 0; i < rows.length; i++) {
    // 处理单行数据
    batchInsertData.push([...]); // 添加到数组
}

// 第二轮：批量插入
await execute('BEGIN TRANSACTION');
for (let i = 0; i < batchInsertData.length; i++) {
    await execute(sql, batchInsertData[i]);
}
await execute('COMMIT');
```

**优势**：
- 减少数据库连接开销
- 提高大量数据导入性能
- 使用事务确保数据一致性
- 支持错误回滚

## 当前数据库字段结构

### loose_orders表实际字段：

```sql
CREATE TABLE loose_orders (
    id VARCHAR(36) PRIMARY KEY,                    -- 订单ID，UUID格式
    order_no VARCHAR(50) UNIQUE NOT NULL,          -- 订单编号，唯一标识
    customer_name VARCHAR(50) NOT NULL,            -- 客户姓名
    customer_code VARCHAR(50),                     -- 客户编码
    community_name VARCHAR(100),                   -- 小区名称
    building VARCHAR(50),                          -- 楼栋号
    room_number VARCHAR(50),                       -- 房间号
    phone VARCHAR(20),                             -- 联系电话
    contact_person VARCHAR(50),                    -- 联系人
    order_type VARCHAR(50),                        -- 订单类型
    project_name VARCHAR(100),                     -- 项目名称
    batch_id VARCHAR(36),                          -- 批次ID
    party_address TEXT,                            -- 甲单地址
    party_appeal_description TEXT,                 -- 甲单诉求描述
    party_total_amount DECIMAL(10,2) DEFAULT 0.00, -- 甲单费用合计金额
    party_remarks TEXT,                            -- 甲单备注
    status VARCHAR(20) DEFAULT 'pending',          -- 订单状态
    installation_date DATE,                        -- 安装日期
    assigned_worker_id VARCHAR(36),                -- 指派工人ID
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);
```

## Excel导入字段映射

### 支持的Excel字段：
- 工单号 → `order_no`
- 用户编号 → `customer_code`
- 用户姓名 → `customer_name`
- 移动电话 → `phone`
- 地址 → `party_address` (甲单地址)
- 诉求描述 → `party_appeal_description` (甲单诉求描述)
- 备注 → `party_remarks` (甲单备注)
- 费用合计金额 → `party_total_amount` (甲单费用合计金额)

### 系统自动填充字段：
- `id`: UUID自动生成
- `community_name`: ''
- `building`: ''
- `room_number`: ''
- `contact_person`: ''
- `order_type`: '普通工单'
- `project_name`: ''
- `batch_id`: 导入时生成的UUID
- `status`: 'pending'
- `created_at`: 服务器当前时间 (new Date().toISOString())
- `updated_at`: 服务器当前时间 (new Date().toISOString())

## 测试验证

创建了`test-sqlite-fix.js`测试脚本来验证：
1. INSERT语句执行
2. 数据查询
3. UPDATE语句执行
4. 数据清理

创建了`test-batch-insert.js`测试脚本来验证：
1. 批量数据生成
2. 批量插入执行
3. 事务处理
4. 数据验证
5. 错误回滚

## 修复结果

- ✅ 删除了所有不存在的数据库字段引用
- ✅ 修复了时间戳处理，使用服务器端生成时间
- ✅ 实现了批量插入功能，提高导入性能
- ✅ 保持了Excel导入功能的完整性
- ✅ 确保了甲单字段的正确映射

现在Excel导入功能应该可以正常工作，不会再出现字段不存在和函数不支持的错误。时间戳由服务器端统一管理，确保数据一致性。批量插入功能大大提高了大量数据的导入性能。 