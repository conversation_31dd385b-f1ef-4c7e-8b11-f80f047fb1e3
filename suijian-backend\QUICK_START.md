# 工程管理系统后端 - 快速开始指南

## 🚀 快速启动

### 1. 环境要求

- Node.js 18.x 或更高版本
- npm 8.x 或更高版本

### 2. 克隆项目

```bash
git clone <repository-url>
cd suijian-backend
```

### 3. 安装依赖

```bash
npm install
```

### 4. 环境配置

复制环境配置文件：

```bash
cp env.example .env
```

根据实际情况修改 `.env` 文件中的配置项。

### 5. 启动服务

```bash
# 开发模式（支持热重载）
npm run dev

# 生产模式
npm start
```

服务启动后，访问 `http://localhost:3000/health` 检查服务状态。

## 📝 环境配置

### 开发环境配置

复制 `env.example` 为 `.env` 并根据需要修改：

```bash
# 应用配置
NODE_ENV=development
PORT=3000

# JWT配置
JWT_SECRET=your_jwt_secret_key_development
JWT_EXPIRES_IN=24h
```

### 生产环境配置

```bash
# 应用配置
NODE_ENV=production
PORT=3000

# JWT配置
JWT_SECRET=your_jwt_secret_key_production
JWT_EXPIRES_IN=8h
```

## 🧪 测试

```bash
# 运行所有测试
npm test

# 运行测试并监听文件变化
npm run test:watch

# 生成测试覆盖率报告
npm run test:coverage
```

## 📊 API接口测试

启动服务后，可以测试以下接口：

### 健康检查
```bash
curl http://localhost:3000/health
```

### API信息
```bash
curl http://localhost:3000/api
```

### 用户登录
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password"}'
```

### 仪表板数据
```bash
# 获取token后，使用token访问仪表板接口
curl http://localhost:3000/api/dashboard/materials/unused \
  -H "Authorization: Bearer <your-token>"
```

## 🐳 Docker部署

```bash
# 构建镜像
docker build -t suijian-backend .

# 运行容器
docker run -p 3000:3000 suijian-backend

# 使用Docker Compose
docker-compose up -d
```

## 📝 开发命令

```bash
# 代码检查
npm run lint

# 代码格式化
npm run format

# 修复代码问题
npm run lint:fix
```

## 🔧 开发工具

### 推荐VS Code插件

- ESLint
- Prettier
- Node.js Extension Pack
- REST Client

### 调试配置

在VS Code中创建 `.vscode/launch.json`：

```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Program",
            "type": "node",
            "request": "launch",
            "program": "${workspaceFolder}/src/app.js",
            "envFile": "${workspaceFolder}/.env",
            "console": "integratedTerminal"
        }
    ]
}
```

## 📚 默认用户

系统预置了以下默认用户用于测试：

| 用户名 | 密码 | 角色 | 权限 |
|--------|------|------|------|
| admin | password | 管理员 | 所有权限 |
| manager | password | 经理 | 管理权限 |
| worker | password | 员工 | 基础权限 |

## 🚨 常见问题

### 1. 端口被占用

如果3000端口被占用，可以修改 `.env` 文件中的 `PORT` 配置。

### 2. 依赖安装失败

```bash
# 清除npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

### 3. 权限问题

```bash
# 给启动脚本执行权限
chmod +x scripts/start.sh
```

### 4. 环境变量问题

确保 `.env` 文件存在且配置正确：

```bash
# 检查环境变量
node -e "console.log(process.env.NODE_ENV)"
```

## 📞 技术支持

如果遇到问题，请：

1. 查看日志文件 `logs/combined.log`
2. 检查环境配置
3. 运行测试确保代码正常
4. 提交Issue到项目仓库

## �� 许可证

MIT License 