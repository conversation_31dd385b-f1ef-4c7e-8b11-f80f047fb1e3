<template>
  <div class="project-progress-statistics">

    <!-- 工程状态统计 -->
    <el-card class="status-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>📊 工程状态统计</span>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="status-summary">
        <div class="status-item">
          <span class="status-icon">🔵</span>
          <span class="status-label">未开始:</span>
          <span class="status-count">{{ notStartedCount }}个</span>
        </div>
        <div class="status-item">
          <span class="status-icon">🟡</span>
          <span class="status-label">在建:</span>
          <span class="status-count">{{ inProgressCount }}个</span>
        </div>
        <div class="status-item">
          <span class="status-icon">🔴</span>
          <span class="status-label">暂停:</span>
          <span class="status-count">{{ pausedCount }}个</span>
        </div>
        <div class="status-item">
          <span class="status-icon">✅</span>
          <span class="status-label">完成:</span>
          <span class="status-count">{{ completedCount }}个</span>
        </div>
      </div>

      <div class="progress-summary">
        <div class="progress-item">
          <span class="label">📈 总工程数:</span>
          <span class="value">{{ totalProjects }}个</span>
        </div>
        <div class="progress-item">
          <span class="label">完成率:</span>
          <span class="value completion-rate">{{ completionRate }}%</span>
        </div>
      </div>
    </el-card>

    <!-- 工程列表 -->
    <el-card class="project-list-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>🏗️ 工程列表 (按最新进度排序)</span>
        </div>
      </template>

      <el-table :data="paginatedProjectList" style="width: 100%" border>
        <el-table-column prop="icon" label="" width="50" align="center">
          <template #default="{ row }">
            <span class="project-icon">{{ row.icon }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="工程名称" width="180" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusIcon(row.status) }} {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastUpdateTime" label="最新进度时间" width="150" align="center" />
        <el-table-column prop="progress" label="进度" width="100" align="center">
          <template #default="{ row }">
            <el-progress :percentage="row.progress" :stroke-width="8" />
          </template>
        </el-table-column>
        <el-table-column prop="currentProgress" label="当前进度" width="150" />
        <el-table-column prop="issues" label="存在问题" width="200">
          <template #default="{ row }">
            <span v-if="row.issues" class="issues-text">{{ row.issues }}</span>
            <span v-else class="no-issues">无</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewProjectDetails(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="projectList.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button type="primary" size="large" @click="() => viewProjectDetails()">
        📊 工程详情
      </el-button>
      <el-button type="success" size="large" @click="viewProgressReport">
        📈 进度报表
      </el-button>
      <el-button type="default" size="large" @click="refreshData">
        🔄 刷新
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'

// 数据定义
interface Project {
  id: number
  name: string
  icon: string
  status: string
  lastUpdateTime: string
  progress: number
  startDate: string
  estimatedEndDate: string
  currentProgress: string
  issues?: string
}

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10
})

// 响应式数据
const projectList = ref<Project[]>([
  {
    id: 1,
    name: '阳光小区A栋',
    icon: '🏢',
    status: '在建',
    lastUpdateTime: '2024-01-15 14:30',
    progress: 65,
    startDate: '2024-01-01',
    estimatedEndDate: '2024-03-01',
    currentProgress: '基础施工完成，正在进行主体结构',
    issues: '材料供应延迟，预计影响工期3天'
  },
  {
    id: 2,
    name: '花园广场项目',
    icon: '🏬',
    status: '在建',
    lastUpdateTime: '2024-01-15 10:20',
    progress: 45,
    startDate: '2024-01-05',
    estimatedEndDate: '2024-04-01',
    currentProgress: '地基开挖完成，准备浇筑混凝土',
    issues: ''
  },
  {
    id: 3,
    name: '商业中心B区',
    icon: '🏪',
    status: '暂停',
    lastUpdateTime: '2024-01-14 16:45',
    progress: 30,
    startDate: '2023-12-15',
    estimatedEndDate: '2024-05-01',
    currentProgress: '主体结构30%，因资金问题暂停',
    issues: '资金链紧张，等待甲方追加投资'
  },
  {
    id: 4,
    name: '住宅楼C座',
    icon: '🏠',
    status: '完成',
    lastUpdateTime: '2024-01-14 09:15',
    progress: 100,
    startDate: '2023-11-01',
    estimatedEndDate: '2024-01-10',
    currentProgress: '工程已完工，正在进行验收',
    issues: ''
  },
  {
    id: 5,
    name: '绿城花园D区',
    icon: '🏘️',
    status: '在建',
    lastUpdateTime: '2024-01-14 08:30',
    progress: 75,
    startDate: '2023-12-01',
    estimatedEndDate: '2024-02-15',
    currentProgress: '装修阶段，预计本月底完工',
    issues: '部分装修材料质量不达标，需要更换'
  },
  {
    id: 6,
    name: '工业园E栋',
    icon: '🏭',
    status: '未开始',
    lastUpdateTime: '2024-01-13 17:00',
    progress: 0,
    startDate: '2024-02-01',
    estimatedEndDate: '2024-06-01',
    currentProgress: '等待开工许可证',
    issues: '环评手续尚未完成'
  }
])

// 计算属性
const totalProjects = computed(() => projectList.value.length)
const notStartedCount = computed(() => projectList.value.filter(p => p.status === '未开始').length)
const inProgressCount = computed(() => projectList.value.filter(p => p.status === '在建').length)
const pausedCount = computed(() => projectList.value.filter(p => p.status === '暂停').length)
const completedCount = computed(() => projectList.value.filter(p => p.status === '完成').length)
const completionRate = computed(() => {
  if (totalProjects.value === 0) return 0
  return Math.round((completedCount.value / totalProjects.value) * 100 * 10) / 10
})

// 分页计算属性
const paginatedProjectList = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return projectList.value.slice(start, end)
})

// 方法
const getStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '未开始': 'info',
    '在建': 'warning',
    '暂停': 'danger',
    '完成': 'success'
  }
  return statusMap[status] || 'info'
}

const getStatusIcon = (status: string): string => {
  const iconMap: Record<string, string> = {
    '未开始': '🔵',
    '在建': '🟡',
    '暂停': '🔴',
    '完成': '✅'
  }
  return iconMap[status] || '🔵'
}

const refreshData = () => {
  ElMessage.success('数据已刷新')
  // 这里可以调用API刷新数据
}

const viewProjectDetails = (project?: Project) => {
  if (project) {
    ElMessage.info(`查看 ${project.name} 的详细信息`)
  } else {
    ElMessage.info('查看所有工程详情')
  }
  // 这里可以打开详情弹窗或跳转到详情页面
}

const viewProgressReport = () => {
  ElMessage.info('生成工程进度报表')
  // 这里可以跳转到进度报表页面
}

// 分页处理方法
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
}

// 生命周期
onMounted(() => {
  // 页面加载时获取数据
})
</script>

<style scoped>
.project-progress-statistics {
  padding: 20px;
}

.breadcrumb {
  margin-bottom: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 8px 0;
  color: #303133;
}

.page-description {
  color: #606266;
  margin: 0;
}

.status-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.status-summary {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  font-size: 16px;
}

.status-label {
  color: #606266;
}

.status-count {
  font-weight: bold;
  color: #409EFF;
}

.progress-summary {
  display: flex;
  gap: 30px;
  padding: 10px;
  background-color: #ecf5ff;
  border-radius: 4px;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.label {
  color: #606266;
}

.value {
  font-weight: bold;
  color: #409EFF;
}

.completion-rate {
  color: #67C23A;
}

.project-list-card {
  margin-bottom: 20px;
}

.project-icon {
  font-size: 18px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}

.issues-text {
  color: #f56c6c;
  font-weight: 500;
}

.no-issues {
  color: #67c23a;
  font-style: italic;
}
</style>
