/**
 * 统一响应格式工具类
 */
class Response {
    /**
     * 成功响应
     * @param {Object} res - Express响应对象
     * @param {*} data - 响应数据
     * @param {string} message - 响应消息
     * @param {number} status - HTTP状态码
     */
    static success(res, data = null, message = '操作成功', status = 200) {
        res.status(status).json({
            success: true,
            message,
            data,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 错误响应
     * @param {Object} res - Express响应对象
     * @param {string} message - 错误消息
     * @param {number} status - HTTP状态码
     * @param {*} errors - 错误详情
     */
    static error(res, message = '操作失败', status = 400, errors = null) {
        const response = {
            success: false,
            message,
            timestamp: new Date().toISOString()
        };

        if (errors) {
            response.errors = errors;
        }

        res.status(status).json(response);
    }

    /**
     * 服务器错误响应
     * @param {Object} res - Express响应对象
     * @param {string} message - 错误消息
     */
    static serverError(res, message = '服务器内部错误') {
        this.error(res, message, 500);
    }

    /**
     * 未授权响应
     * @param {Object} res - Express响应对象
     * @param {string} message - 错误消息
     */
    static unauthorized(res, message = '未授权访问') {
        this.error(res, message, 401);
    }

    /**
     * 禁止访问响应
     * @param {Object} res - Express响应对象
     * @param {string} message - 错误消息
     */
    static forbidden(res, message = '禁止访问') {
        this.error(res, message, 403);
    }

    /**
     * 资源不存在响应
     * @param {Object} res - Express响应对象
     * @param {string} message - 错误消息
     */
    static notFound(res, message = '资源不存在') {
        this.error(res, message, 404);
    }

    /**
     * 请求参数错误响应
     * @param {Object} res - Express响应对象
     * @param {string} message - 错误消息
     */
    static badRequest(res, message = '请求参数错误') {
        this.error(res, message, 400);
    }

    /**
     * 分页响应
     * @param {Object} res - Express响应对象
     * @param {Array} data - 数据列表
     * @param {number} total - 总数量
     * @param {number} page - 当前页码
     * @param {number} pageSize - 每页数量
     * @param {string} message - 响应消息
     */
    static pagination(res, data, total, page, pageSize, message = '获取成功') {
        res.json({
            success: true,
            message,
            data: {
                list: data,
                pagination: {
                    total,
                    page: parseInt(page),
                    pageSize: parseInt(pageSize),
                    totalPages: Math.ceil(total / pageSize)
                }
            },
            timestamp: new Date().toISOString()
        });
    }
}

module.exports = Response;