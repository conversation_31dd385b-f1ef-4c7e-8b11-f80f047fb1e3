const express = require('express');
const router = express.Router();
const employeesController = require('../controllers/employeesController');
const { authMiddleware } = require('../middleware/auth');

// 获取员工列表
router.get('/list', [authMiddleware], employeesController.getEmployees);

// 新增员工
router.post('/', [authMiddleware], employeesController.createEmployee);

// 更新员工
router.put('/:id', [authMiddleware], employeesController.updateEmployee);

module.exports = router;