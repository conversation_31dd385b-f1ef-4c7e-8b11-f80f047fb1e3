# 散户订单 - 月度平账功能需求文档（更新版）

## 1. 项目概述

### 1.1 功能描述
月度平账功能用于对散户订单进行月度结算和统计分析，包括工程费用汇总、材料使用统计、管件统计、工程决算等多个维度的数据展示和分析。

### 1.2 更新内容
- 重新设计了所有表格的表头结构
- 为所有表格添加了合计行功能
- 删除了"管道燃气户内二次安装工程决算"选项卡
- 优化了户主信息的展示方式
- 增强了材料领用表的功能

## 2. 功能模块

### 2.1 平账信息管理
- **平账月份选择**：支持选择具体的平账月份
- **平账时间记录**：自动记录平账操作时间
- **操作员信息**：记录执行平账操作的用户
- **平账状态管理**：支持未平账、已平账等状态

### 2.2 工程费用汇总模块

#### 2.2.1 户内安装工程费用汇总（带合计行）
**表头结构：**
- 序号
- 单项工程名称
- 工程安装人工费(元)
  - 表前安装人工费
  - 户内安装人工费
  - 小计(元)
- 安装户数
- 甲供材料费用(元)
  - 表前甲供材料费
  - 户内甲供材料费
  - 小计(元)
- 超领甲供材料金额(元)
- 应付施工单位金额

**功能特点：**
- 支持多级表头展示
- 自动计算各项费用小计
- 合计行显示所有数值列的总和
- 金额列使用货币格式显示

#### 2.2.2 户内表前甲供材清单表（带合计行）
**表头结构：**
- 序号
- 单项工程名称
- 甲供材料-表前（多级表头）
  - 灶前阀：领用数、耗用数、含税单价
  - 涂覆钢管：领用数、耗用数、含税单价
  - 波纹管：领用数、耗用数、含税单价
  - 输送波纹管防护钢板(直板)：领用数、耗用数、含税单价
  - 输送波纹管防护钢板(外、侧弯)：领用数、耗用数、含税单价

#### 2.2.3 户内甲供材料清单表（带合计行）
**表头结构：**
- 序号
- 单项工程名称
- 甲供材料-户内（多级表头）
  - 各种材料的领用数、耗用数、含税单价

#### 2.2.4 户内管件甲供材料费用表（带合计行）
**表头结构：**
- 序号
- 单项工程名称
- 甲供材料-户内（多级表头）
  - 波纹管快速外螺纹接头：领用数、耗用数、含税单价
  - 镀锌90°弯头：领用数、耗用数、含税单价
  - 镀锌六角外丝：领用数、耗用数、含税单价
  - 镀锌内丝：领用数、耗用数、含税单价
  - 镀锌内外丝弯头：领用数、耗用数、含税单价

#### 2.2.5 户内结算做销售处理超领材料费用表（带合计行）
**表头结构：**
- 序号
- 材料名称
- 规格
- 单位
- 超领量(损耗率以内)
- 含税材料单价(元)(损耗率以内)
- 超领量(损耗率以外)
- 含税材料单价(元)(损耗率以外)
- 合价(元)

### 2.3 管件统计模块

#### 2.3.1 户内挂表安装工程管件统计（带合计行）
**表头结构：**
- 序号
- 户主信息（多级表头）
  - 姓名
  - 用户编号
  - 住址
    - 小区名称
    - 楼橦
    - 房号
- 派单时间
- 安装时间
- 各种管件统计列（17种管件类型）
- 实际耗用甲供材料金额-户内（元）

#### 2.3.2 户内零星安装工程管件统计（带合计行）
**表头结构：**
- 序号
- 户主信息（多级表头）
  - 姓名
  - 编号
  - 详细地址
    - 详细地址汇总
    - 小区名称
    - 楼橦
    - 房号
- 派单日期
- 施工日期
- 各种管件统计列
- 实际耗用甲供材料金额-户内（元）

#### 2.3.3 户内二次安装工程管件统计（带合计行）
**表头结构：**
- 序号
- 户主信息（多级表头）
  - 姓名
  - 编号
  - 详细地址
    - 小区名称
    - 楼橦
    - 房号
- 派单日期
- 施工日期
- 管件统计列（4种二次安装相关管件）
- 实际耗用甲供材料金额-户内（元）

#### 2.3.4 户内安装管件统计表（带合计行）
**表头结构：**
- 序号
- 单项工程名称
- 各种管件统计列（17种管件类型，每种显示数量）
- 实际耗用甲供材料金额-户内（元）

### 2.4 工程决算模块

#### 2.4.1 户内挂表安装工程决算（半月板）（带合计行）
**表头结构：**
- 序号
- 户主信息（多级表头）
  - 姓名
  - 用户编号
  - 住址
    - 小区名称
    - 楼橦
    - 房号
- 派单时间
- 安装时间
- 煤气表信息
  - 有/无 表箱
  - 表前阀
- 包干价（元）
  - 表前
  - 户内
- 灶前阀数量（个）
- 表前管道耗材（米）
  - 钢管
  - 涂覆钢管
  - 波纹管
- 户内管道耗材（米）
  - 钢管
  - 涂覆钢管
  - 波纹管
- 机械表
- 表前阀
  - 低低压调压器（个）
  - 波纹连接管（1.2m,1m）
  - 波纹连接管（0.8m,0.5m）
  - 预制短管12cm
  - 预制短管18cm
- 表前安装成本（元）
- 户内安装成本（元）

#### 2.4.2 户内挂表安装工程决算（未半月板）（带合计行）
**表头结构：**
- 与半月板表头结构基本相同
- 户主信息部分只包含"住址汇总"子表头
- 所有数值字段默认为0

#### 2.4.3 管道燃气户内零星安装工程决算（半月板）（带合计行）
**表头结构：**
- 户主信息（多级表头）
  - 姓名
  - 编号
  - 详细地址
    - 小区名称
    - 楼橦
    - 房号
- 派单日期
- 施工日期
- 火点数量（个）
- 灶前阀数量（个）
- 安装项目
  - 燃气表箱(个)
  - 表前阀(个)
  - 低低压调压器
  - 燃气表（个）
- 包干价（元）
- 管道材料耗用统计（米）
  - DN15钢管
  - DN15涂覆钢管
  - 波纹管
- 波纹管连接（灶）
- 波纹管连接（热）
- 预制短管12cm
- 预制短管18cm
- 单户户内零星安装工程造价（元）

#### 2.4.4 管道燃气户内零星安装工程决算（未半月板）（带合计行）
**表头结构：**
- 序号
- 与半月板表头结构相同

#### 2.4.5 管道燃气户内零星安装工程决算（不可用燃气表）（带合计行）
**表头结构：**
- 序号
- 与半月板表头结构相同

### 2.5 材料管理模块

#### 2.5.1 甲供材料领用表（带合计行）
**表头结构：**
- 领料日期
- 物料编码
- 材料名称
- 型号规格
- 单位
- 领料数
- 不含税单价
- 含税单价
- 不含税金额
- 含税金额
- 总应扣材料金额
- 备注

## 3. 技术要求

### 3.1 表格功能要求
- **合计行功能**：所有表格必须支持合计行，自动计算数值和金额列的总和
- **多级表头**：支持复杂的多级表头结构展示
- **数据格式化**：金额列统一使用货币格式显示（¥符号）
- **响应式设计**：表格支持横向滚动，适应不同屏幕尺寸

### 3.2 数据处理要求
- **智能合计**：
  - 序号列显示"合计"
  - 文本列在合计行显示为空
  - 数量列显示数值合计
  - 金额列显示货币格式合计
- **数据验证**：确保数据的准确性和完整性
- **实时计算**：支持数据变更时的实时重新计算

### 3.3 用户交互要求
- **选项卡切换**：支持在不同统计表之间快速切换
- **数据导出**：支持将统计数据导出为Excel格式
- **打印功能**：支持打印当前显示的统计表
- **平账确认**：提供平账确认弹窗，显示平账汇总信息

## 4. 业务规则

### 4.1 平账流程
1. 选择平账月份
2. 系统自动加载该月份的所有相关数据
3. 用户可以查看各个统计表的详细数据
4. 确认数据无误后执行平账操作
5. 平账完成后数据锁定，不可修改

### 4.2 数据计算规则
- **工程费用**：人工费 + 材料费 + 管理费
- **材料成本**：实际耗用数量 × 含税单价
- **超领费用**：(领用数量 - 标准用量) × 单价
- **利润计算**：总收入 - 总成本

### 4.3 权限控制
- **查看权限**：所有用户可查看平账数据
- **操作权限**：只有管理员可执行平账操作
- **修改权限**：平账前可修改，平账后锁定

## 5. 界面设计要求

### 5.1 布局要求
- 采用选项卡式布局，便于在不同统计表间切换
- 表格采用固定表头设计，支持大量数据展示
- 合计行采用不同背景色突出显示

### 5.2 交互要求
- 表格支持排序功能
- 支持按条件筛选数据
- 提供数据刷新功能
- 操作按钮布局合理，易于使用

## 6. 性能要求

### 6.1 响应时间
- 页面加载时间不超过3秒
- 表格数据切换响应时间不超过1秒
- 合计计算响应时间不超过0.5秒

### 6.2 数据处理能力
- 支持单月最多10000条工程记录
- 支持同时展示多个大型统计表
- 确保大数据量下的流畅操作

## 7. 测试要求

### 7.1 功能测试
- 验证所有表格的合计功能正确性
- 测试多级表头的显示效果
- 验证数据导出和打印功能

### 7.2 性能测试
- 大数据量下的表格渲染性能
- 合计计算的准确性和效率
- 并发用户访问的稳定性

### 7.3 兼容性测试
- 主流浏览器兼容性测试
- 不同分辨率下的显示效果测试
- 移动端适配测试
