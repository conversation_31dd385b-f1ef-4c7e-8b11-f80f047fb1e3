# 简化领料申请流程说明

## 概述

领料申请流程已简化为直接使用 `material_records` 和 `material_record_items` 表，不再使用专门的申请表。这样可以减少数据冗余，简化代码逻辑。

## 数据流程

### 1. 创建申请
```
用户提交申请 → 直接保存到物料记录表(type='apply')
```

### 2. 批准申请
```
申请被批准 → 创建出库记录(type='out') → 更新库存
```

### 3. 删除申请
```
删除申请 → 直接删除物料记录
```

## 表结构

### 物料记录表
```sql
-- 物料记录主表
material_records (
    id, type, operator_id, order_id, purpose, remarks, created_at
)

-- 物料记录明细表
material_record_items (
    id, record_id, material_id, quantity, current_quantity, created_at
)
```

## 记录类型

### 1. 申请记录 (type='apply')
- **用途**：记录物料申请信息
- **操作员**：申请人
- **关联订单**：申请编号
- **库存影响**：无（申请时不影响库存）

### 2. 出库记录 (type='out')
- **用途**：记录申请批准后的出库信息
- **操作员**：申请人
- **关联订单**：申请编号
- **库存影响**：减少库存

## API接口

### 1. 创建申请
```http
POST /api/material-applications
```

**请求参数**：
```json
{
  "applyNo": "申请编号",
  "applyDate": "申请日期",
  "applicantId": "申请人ID",
  "purpose": "用途",
  "orderNo": "订单号（可选）",
  "remarks": "备注",
  "materials": [
    {
      "materialId": "物料ID",
      "quantity": "数量",
      "remarks": "备注"
    }
  ]
}
```

**响应**：
```json
{
  "success": true,
  "data": {
    "id": "记录ID"
  },
  "message": "领料申请创建成功"
}
```

### 2. 获取申请列表
```http
GET /api/material-applications?page=1&pageSize=20&applyNo=&applicantId=&purpose=&startDate=&endDate=
```

**响应**：
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "id": "记录ID",
        "apply_no": "申请编号",
        "apply_date": "申请日期",
        "applicant_id": "申请人ID",
        "applicant_name": "申请人姓名",
        "purpose": "用途",
        "remarks": "备注",
        "created_at": "创建时间",
        "materials": [
          {
            "material_id": "物料ID",
            "quantity": "数量",
            "current_quantity": "当前库存",
            "material_name": "物料名称",
            "company_code": "公司编码",
            "client_code": "客户编码",
            "specification": "规格",
            "unit": "单位"
          }
        ]
      }
    ],
    "total": "总数"
  }
}
```

### 3. 获取申请详情
```http
GET /api/material-applications/:id
```

### 4. 批准申请
```http
PUT /api/material-applications/:id/approve
```

**请求参数**：
```json
{
  "remarks": "批准备注"
}
```

**响应**：
```json
{
  "success": true,
  "message": "申请批准成功，物料已出库"
}
```

### 5. 删除申请
```http
DELETE /api/material-applications/:id
```

### 6. 获取申请记录
```http
GET /api/material-applications/records/list?type=apply&page=1&pageSize=20
```

## 前端修改

### 1. 移除接收人字段
- 前端不再需要选择接收人
- 申请人和操作员为同一人

### 2. 简化表单验证
- 移除接收人相关验证
- 保持其他验证逻辑不变

### 3. API调用修改
- 移除 `receiverId` 参数
- 其他参数保持不变

## 数据一致性

### 1. 事务处理
- 所有操作都使用数据库事务
- 确保数据一致性

### 2. 库存检查
- 申请批准时检查库存是否足够
- 库存不足时抛出错误并回滚

### 3. 记录完整性
- 申请记录和出库记录通过 `order_id` 关联
- 删除申请时直接删除相关记录

## 查询示例

### 1. 查询所有申请记录
```sql
SELECT 
    mr.*,
    e.name as operator_name
FROM material_records mr
LEFT JOIN employees e ON mr.operator_id = e.id
WHERE mr.type = 'apply'
ORDER BY mr.created_at DESC;
```

### 2. 查询申请记录及其物料明细
```sql
SELECT 
    mr.id,
    mr.type,
    mr.created_at,
    mr.order_id as apply_no,
    mri.material_id,
    mri.quantity,
    mri.current_quantity,
    m.name as material_name
FROM material_records mr
JOIN material_record_items mri ON mr.id = mri.record_id
JOIN materials m ON mri.material_id = m.id
WHERE mr.type = 'apply'
ORDER BY mr.created_at DESC;
```

### 3. 查询出库记录
```sql
SELECT 
    mr.*,
    e.name as operator_name
FROM material_records mr
LEFT JOIN employees e ON mr.operator_id = e.id
WHERE mr.type = 'out'
ORDER BY mr.created_at DESC;
```

## 优势

1. **简化架构**：减少表数量，降低复杂度
2. **统一管理**：所有物料记录都在同一套表中
3. **减少冗余**：避免数据重复存储
4. **便于查询**：统一的查询接口
5. **易于维护**：代码逻辑更简单

## 注意事项

1. **数据迁移**：如果之前有数据，需要迁移到新结构
2. **前端适配**：前端需要移除接收人相关功能
3. **权限控制**：确保只有有权限的用户可以批准申请
4. **审计日志**：所有操作都有完整的记录 