const { query, execute } = require('./src/config/database');

async function testDatabaseConnection() {
    try {
        console.log('测试数据库连接...');

        // 测试基本查询
        const result = await query('SELECT 1 as test');
        console.log('数据库连接正常:', result);

        // 检查loose_orders表
        const tableExists = await query("SELECT name FROM sqlite_master WHERE type='table' AND name='loose_orders'");
        console.log('loose_orders表存在:', tableExists.length > 0);

        if (tableExists.length > 0) {
            // 获取数据总数
            const countResult = await query('SELECT COUNT(*) as total FROM loose_orders');
            console.log('loose_orders表数据总数:', countResult[0].total);

            // 获取一条示例数据
            const sampleData = await query('SELECT * FROM loose_orders LIMIT 1');
            if (sampleData.length > 0) {
                console.log('示例数据:', sampleData[0]);
            } else {
                console.log('表中没有数据');
            }
        }

    } catch (error) {
        console.error('数据库连接测试失败:', error);
    }
}

testDatabaseConnection();