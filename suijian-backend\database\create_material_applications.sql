-- 领料申请表
CREATE TABLE IF NOT EXISTS material_applications (
    id VARCHAR(36) PRIMARY KEY,
    apply_no VARCHAR(50) UNIQUE NOT NULL,
    apply_date DATE NOT NULL,
    applicant_id VARCHAR(36) NOT NULL,
    receiver_id VARCHAR(36) NOT NULL,
    purpose VARCHAR(50) NOT NULL,
    order_no VARCHAR(50),
    status VARCHAR(20) DEFAULT 'pending',
    remarks TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 领料申请明细表
CREATE TABLE IF NOT EXISTS material_application_items (
    id VARCHAR(36) PRIMARY KEY,
    application_id VARCHAR(36) NOT NULL,
    material_id VARCHAR(36) NOT NULL,
    quantity INTEGER NOT NULL,
    remarks TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 领料申请表索引
CREATE INDEX IF NOT EXISTS idx_material_applications_apply_no ON material_applications(apply_no);
CREATE INDEX IF NOT EXISTS idx_material_applications_status ON material_applications(status);
CREATE INDEX IF NOT EXISTS idx_material_applications_applicant ON material_applications(applicant_id);
CREATE INDEX IF NOT EXISTS idx_material_applications_receiver ON material_applications(receiver_id);
CREATE INDEX IF NOT EXISTS idx_material_applications_date ON material_applications(apply_date);

-- 领料申请明细表索引
CREATE INDEX IF NOT EXISTS idx_material_application_items_application ON material_application_items(application_id);
CREATE INDEX IF NOT EXISTS idx_material_application_items_material ON material_application_items(material_id); 