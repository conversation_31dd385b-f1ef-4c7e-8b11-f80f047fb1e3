<template>
  <div class="project-list-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>工程订单列表</span>
        </div>
      </template>
      
      <!-- 搜索栏 -->
      <el-row :gutter="20" class="search-section">
        <el-col :span="6">
          <el-input v-model="searchForm.partyOrderNo" placeholder="甲方订单号" clearable />
        </el-col>
        <el-col :span="6">
          <el-input v-model="searchForm.projectName" placeholder="工程名称" clearable />
        </el-col>
        <el-col :span="6">
          <el-input v-model="searchForm.projectAddress" placeholder="工程地址" clearable />
        </el-col>
        <el-col :span="6">
          <el-select v-model="searchForm.projectStatus" placeholder="工程状态" clearable style="width: 100%">
            <el-option label="未开始" value="notStarted" />
            <el-option label="在建" value="building" />
            <el-option label="暂停" value="paused" />
            <el-option label="完成" value="completed" />
          </el-select>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" class="search-section" style="margin-top: 15px;">
        <el-col :span="6">
          <el-input v-model="searchForm.materialCode" placeholder="物料编码" clearable />
        </el-col>
        <el-col :span="12">
          <div class="search-buttons">
            <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
            <el-button icon="Refresh" @click="handleReset">重置</el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 订单列表 -->
      <el-table :data="projectList" border class="project-table" style="width: 100%; margin-top: 20px;">
        <el-table-column prop="partyOrderNo" label="甲方订单号" min-width="120" />
        <el-table-column prop="projectName" label="工程名称" min-width="120" />
        <el-table-column prop="projectAddress" label="工程地址" min-width="150" />
        <el-table-column prop="projectStatus" label="工程状态" min-width="80">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.projectStatus)">
              {{ getStatusText(scope.row.projectStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="estimatedStartTime" label="预估开始时间" min-width="120" />
        <el-table-column prop="estimatedEndTime" label="预估结束时间" min-width="120" />
        <el-table-column prop="progress" label="进度" min-width="80" />
        <el-table-column label="操作" min-width="120" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="viewDetail(scope.row)">详情</el-button>
            <el-button type="primary" link @click="handleProgress(scope.row)">推进</el-button>
            <el-button type="success" link @click="goToSettlement(scope.row)">工程结算</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="pagination"
      />
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <!-- <el-button type="primary" icon="Plus" @click="addProject">新增订单</el-button> -->
        <!-- <el-button icon="Download" @click="exportExcel">导出Excel</el-button> -->
        <!-- <el-button icon="PieChart" @click="showChart">统计图表</el-button> -->
      </div>
      
      <!-- 工程详情弹窗 -->
      <el-dialog v-model="detailDialogVisible" title="工程详情" width="600">
        <el-form :model="currentProject" label-width="100px">
          <el-form-item label="甲方订单号:">
            <span>{{ currentProject.partyOrderNo }}</span>
          </el-form-item>
          <el-form-item label="工程名称:">
            <span>{{ currentProject.projectName }}</span>
          </el-form-item>
          <el-form-item label="工程地址:">
            <span>{{ currentProject.projectAddress }}</span>
          </el-form-item>
          <el-form-item label="工程状态:">
            <el-tag :type="getStatusType(currentProject.projectStatus)">
              {{ getStatusText(currentProject.projectStatus) }}
            </el-tag>
          </el-form-item>
          <el-form-item label="预估开始时间:">
            <span>{{ currentProject.estimatedStartTime }}</span>
          </el-form-item>
          <el-form-item label="预估结束时间:">
            <span>{{ currentProject.estimatedEndTime }}</span>
          </el-form-item>
          <el-form-item label="实际开始时间:">
            <span>{{ currentProject.actualStartTime }}</span>
          </el-form-item>
          <el-form-item label="当前进度:">
            <span>{{ currentProject.progress }}</span>
          </el-form-item>
          
          <el-divider />
          
          <el-form-item label="人员列表:">
            <div v-for="(staff, index) in currentProject.staffList" :key="index">
              - {{ staff.workType }}: {{ staff.name }}({{ staff.dailyWage }}元/天)
            </div>
          </el-form-item>
          
          <el-form-item label="物料清单:">
            <div v-for="(material, index) in currentProject.materialList" :key="index">
              - {{ material.name }} {{ material.quantity }}{{ material.unit }}
            </div>
          </el-form-item>
          
          <el-form-item label="备注:">
            <span>{{ currentProject.remarks }}</span>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
const router = useRouter()

// 搜索表单
const searchForm = reactive({
  partyOrderNo: '',
  projectName: '',
  projectAddress: '',
  projectStatus: '',
  materialCode: ''
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 工程列表数据
const projectList = ref([
  {
    id: 1,
    partyOrderNo: 'GC202401001',
    projectName: '阳光小区A栋',
    projectAddress: '阳光小区A栋',
    projectStatus: 'building',
    estimatedStartTime: '2024-01-10',
    estimatedEndTime: '2024-03-15',
    progress: '45%',
    actualStartTime: '2024-01-12',
    staffList: [
      { workType: '电工', name: '李师傅', dailyWage: 300 },
      { workType: '水工', name: '王师傅', dailyWage: 280 },
      { workType: '安装工', name: '张师傅', dailyWage: 320 }
    ],
    materialList: [
      { name: '电缆线', quantity: 200, unit: '米' },
      { name: '开关面板', quantity: 50, unit: '个' },
      { name: '插座', quantity: 100, unit: '个' }
    ],
    remarks: '需要注意地下管线布局'
  },
  {
    id: 2,
    partyOrderNo: 'GC202401002',
    projectName: '花园广场项目',
    projectAddress: '花园广场B区',
    projectStatus: 'notStarted',
    estimatedStartTime: '2024-02-01',
    estimatedEndTime: '2024-04-30',
    progress: '0%',
    actualStartTime: '',
    staffList: [],
    materialList: [],
    remarks: ''
  },
  {
    id: 3,
    partyOrderNo: 'GC202401003',
    projectName: '商业中心B区',
    projectAddress: '商业街88号',
    projectStatus: 'paused',
    estimatedStartTime: '2023-12-01',
    estimatedEndTime: '2024-02-28',
    progress: '65%',
    actualStartTime: '2023-12-01',
    staffList: [],
    materialList: [],
    remarks: ''
  },
  {
    id: 4,
    partyOrderNo: 'GC202401004',
    projectName: '住宅楼C座',
    projectAddress: '住宅区C栋',
    projectStatus: 'completed',
    estimatedStartTime: '2023-10-01',
    estimatedEndTime: '2023-12-31',
    progress: '100%',
    actualStartTime: '2023-10-01',
    staffList: [],
    materialList: [],
    remarks: ''
  }
])

// 当前选中的工程
const currentProject = ref({
  id: 0,
  partyOrderNo: '',
  projectName: '',
  projectAddress: '',
  projectStatus: '',
  estimatedStartTime: '',
  estimatedEndTime: '',
  progress: '',
  actualStartTime: '',
  staffList: [] as any[],
  materialList: [] as any[],
  remarks: ''
})

// 弹窗控制
const detailDialogVisible = ref(false)

// 获取状态类型
const getStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  switch (status) {
    case 'notStarted':
      return 'info'
    case 'building':
      return 'primary'
    case 'paused':
      return 'warning'
    case 'completed':
      return 'success'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'notStarted':
      return '未开始'
    case 'building':
      return '在建'
    case 'paused':
      return '暂停'
    case 'completed':
      return '完成'
    default:
      return ''
  }
}

// 搜索
const handleSearch = () => {
  ElMessage.success('搜索成功')
  console.log('搜索条件:', searchForm)
}

// 重置
const handleReset = () => {
  searchForm.partyOrderNo = ''
  searchForm.projectName = ''
  searchForm.projectAddress = ''
  searchForm.projectStatus = ''
  searchForm.materialCode = ''
}

// 分页变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  console.log('每页条数:', val)
}

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  console.log('当前页:', val)
}

// 查看详情
const viewDetail = (row: any) => {
  currentProject.value = { ...row }
  detailDialogVisible.value = true
}

// 处理推进
const handleProgress = (row: any) => {
  ElMessage.success('跳转到工程推进页面')
  console.log('推进工程:', row)
}

const goToSettlement = (row: any) => {
  router.push({ path: '/projects/project-finish', query: { id: row.id } })
}
</script>

<style lang="scss" scoped>
.project-list-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
    
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .search-section {
    margin-bottom: 15px;
    
    .search-buttons {
      display: flex;
      
      .el-button {
        margin-right: 10px;
      }
    }
  }
  
  .project-table {
    margin-top: 20px;
  }
  
  .pagination {
    margin-top: 20px;
    text-align: right;
  }
  
  .form-actions {
    margin-top: 20px;
    text-align: left;
    
    .el-button {
      margin-right: 10px;
    }
  }
}
</style>