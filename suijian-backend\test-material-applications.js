const request = require('supertest');
const app = require('./src/app');

// 测试领料申请API
async function testMaterialApplications() {
    console.log('开始测试领料申请API...');

    try {
        // 1. 测试获取申请列表（需要认证）
        console.log('\n1. 测试获取申请列表...');
        const listResponse = await request(app)
            .get('/api/material-applications')
            .set('Authorization', 'Bearer test-token');

        console.log('状态码:', listResponse.status);
        console.log('响应:', listResponse.body);

        // 2. 测试创建申请（需要认证）
        console.log('\n2. 测试创建申请...');
        const createResponse = await request(app)
            .post('/api/material-applications')
            .set('Authorization', 'Bearer test-token')
            .send({
                applyNo: 'LY20250108001',
                applyDate: '2025-01-08',
                applicantId: 'emp-001',
                receiverId: 'emp-002',
                purpose: '工程',
                orderNo: 'DD001',
                remarks: '测试申请',
                materials: [{
                    materialId: 'material-fe45e5d128a34ae19030fd63c16c0687',
                    quantity: 1,
                    remarks: '测试物料'
                }]
            });

        console.log('状态码:', createResponse.status);
        console.log('响应:', createResponse.body);

    } catch (error) {
        console.error('测试失败:', error.message);
    }
}

// 运行测试
testMaterialApplications();