# 甲单字段更新说明

## 概述
为 `loose_orders` 表添加了4个甲单相关字段，用于区分甲方和乙方的信息。

## 新增字段

### 1. 甲单地址 (party_address)
- **类型**: TEXT
- **说明**: 甲单地址信息
- **默认值**: NULL
- **索引**: 已创建 (idx_loose_orders_party_address)

### 2. 甲单诉求描述 (party_appeal_description)
- **类型**: TEXT
- **说明**: 甲单诉求描述
- **默认值**: NULL

### 3. 甲单费用合计金额 (party_total_amount)
- **类型**: DECIMAL(10,2)
- **说明**: 甲单费用合计金额
- **默认值**: 0.00
- **索引**: 已创建 (idx_loose_orders_party_total_amount)

### 4. 甲单备注 (party_remarks)
- **类型**: TEXT
- **说明**: 甲单备注信息
- **默认值**: NULL

## 数据库更新

### 1. 主建表语句更新
- 文件: `数据库建表语句.sql`
- 在 `loose_orders` 表中添加了4个新字段
- 添加了相应的索引

### 2. 数据库更新脚本
- 文件: `suijian-backend/database/update_loose_orders_party_fields.sql`
- 用于在现有数据库中添加新字段
- 包含索引创建语句

## API更新

### 1. Excel导入功能
- 支持导入甲单字段
- 字段映射支持多种名称变体
- 自动识别甲单相关列

### 2. 创建订单API
- `POST /api/loose-orders`
- 新增甲单字段参数支持

### 3. 更新订单API
- `PUT /api/loose-orders/:id`
- 支持更新甲单字段

## Excel导入支持

### 支持的字段名称
- **甲单地址**: 甲单地址、甲方地址、Party Address
- **甲单诉求描述**: 甲单诉求描述、甲方诉求、Party Description
- **甲单费用合计金额**: 甲单费用合计金额、甲方费用、Party Amount
- **甲单备注**: 甲单备注、甲方备注、Party Remarks

### Excel模板示例
| 工单号 | 用户姓名 | 地址 | 诉求描述 | 费用合计金额 | 甲单地址 | 甲单诉求描述 | 甲单费用合计金额 | 甲单备注 |
|--------|----------|------|----------|--------------|----------|--------------|------------------|----------|
| WO001  | 张三     | 北京市朝阳区XX小区 | 燃气管道维修 | 150.00 | 北京市朝阳区XX小区 | 甲方要求维修 | 180.00 | 甲方备注 |

## 部署步骤

### 1. 数据库更新
```sql
-- 执行更新脚本
source suijian-backend/database/update_loose_orders_party_fields.sql
```

### 2. 重启服务
```bash
# 重启后端服务
pm2 restart suijian-backend
```

### 3. 验证功能
- 测试Excel导入功能
- 测试创建订单API
- 测试更新订单API

## 注意事项

1. **向后兼容**: 现有数据不受影响，新字段为可选
2. **数据迁移**: 可选择将现有数据复制到甲单字段
3. **索引优化**: 已为常用查询字段创建索引
4. **字段验证**: 费用金额字段支持数字格式验证

## 测试建议

1. **单元测试**: 测试新增字段的CRUD操作
2. **集成测试**: 测试Excel导入功能
3. **性能测试**: 验证索引效果
4. **兼容性测试**: 确保现有功能正常 