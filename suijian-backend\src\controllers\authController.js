const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const Response = require('../utils/response');
const { logger } = require('../utils/logger');

// 模拟用户数据（实际项目中应该从数据库获取）
const users = [{
        id: 1,
        username: 'admin',
        password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
        realName: '系统管理员',
        phone: '13800138000',
        email: '<EMAIL>',
        role: 'admin',
        status: 1
    },
    {
        id: 2,
        username: 'manager',
        password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
        realName: '项目经理',
        phone: '13800138001',
        email: '<EMAIL>',
        role: 'manager',
        status: 1
    },
    {
        id: 3,
        username: 'worker',
        password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
        realName: '普通员工',
        phone: '13800138002',
        email: '<EMAIL>',
        role: 'worker',
        status: 1
    }
];

/**
 * 用户登录
 */
const login = async(req, res) => {
    try {
        const { username, password } = req.body;

        // 查找用户
        const user = users.find(u => u.username === username && u.status === 1);
        if (!user) {
            return Response.error(res, '用户名或密码错误', 401);
        }

        // 验证密码
        const isValidPassword = await bcrypt.compare(password, user.password);
        if (!isValidPassword) {
            return Response.error(res, '用户名或密码错误', 401);
        }

        // 生成JWT token
        const token = jwt.sign({
                id: user.id,
                username: user.username,
                realName: user.realName,
                role: user.role
            },
            process.env.JWT_SECRET, { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
        );

        // 记录登录日志
        logger.info('用户登录', {
            userId: user.id,
            username: user.username,
            ip: req.ip
        });

        Response.success(res, {
            token,
            user: {
                id: user.id,
                username: user.username,
                realName: user.realName,
                phone: user.phone,
                email: user.email,
                role: user.role
            }
        }, '登录成功');

    } catch (error) {
        logger.error('登录失败', error);
        Response.serverError(res, '登录失败');
    }
};

/**
 * 用户注册
 */
const register = async(req, res) => {
    try {
        const { username, password, realName, phone, email, role = 'worker' } = req.body;

        // 检查用户名是否已存在
        const existingUser = users.find(u => u.username === username);
        if (existingUser) {
            return Response.error(res, '用户名已存在', 400);
        }

        // 加密密码
        const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 10;
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        // 创建新用户
        const newUser = {
            id: users.length + 1,
            username,
            password: hashedPassword,
            realName,
            phone,
            email,
            role,
            status: 1
        };

        users.push(newUser);

        // 记录注册日志
        logger.info('用户注册', {
            userId: newUser.id,
            username: newUser.username,
            ip: req.ip
        });

        Response.success(res, {
            id: newUser.id,
            username: newUser.username,
            realName: newUser.realName,
            phone: newUser.phone,
            email: newUser.email,
            role: newUser.role
        }, '注册成功');

    } catch (error) {
        logger.error('注册失败', error);
        Response.serverError(res, '注册失败');
    }
};

/**
 * 用户登出
 */
const logout = async(req, res) => {
    try {
        // 记录登出日志
        logger.info('用户登出', {
            userId: req.user.id,
            username: req.user.username,
            ip: req.ip
        });

        Response.success(res, null, '登出成功');

    } catch (error) {
        logger.error('登出失败', error);
        Response.serverError(res, '登出失败');
    }
};

/**
 * 获取用户信息
 */
const getProfile = async(req, res) => {
    try {
        const user = users.find(u => u.id === req.user.id);
        if (!user) {
            return Response.notFound(res, '用户不存在');
        }

        Response.success(res, {
            id: user.id,
            username: user.username,
            realName: user.realName,
            phone: user.phone,
            email: user.email,
            role: user.role,
            status: user.status
        }, '获取用户信息成功');

    } catch (error) {
        logger.error('获取用户信息失败', error);
        Response.serverError(res, '获取用户信息失败');
    }
};

/**
 * 修改用户信息
 */
const updateProfile = async(req, res) => {
    try {
        const { realName, phone, email } = req.body;

        const userIndex = users.findIndex(u => u.id === req.user.id);
        if (userIndex === -1) {
            return Response.notFound(res, '用户不存在');
        }

        // 更新用户信息
        users[userIndex] = {
            ...users[userIndex],
            realName: realName || users[userIndex].realName,
            phone: phone || users[userIndex].phone,
            email: email || users[userIndex].email
        };

        // 记录修改日志
        logger.info('用户修改信息', {
            userId: req.user.id,
            username: req.user.username,
            ip: req.ip
        });

        Response.success(res, {
            id: users[userIndex].id,
            username: users[userIndex].username,
            realName: users[userIndex].realName,
            phone: users[userIndex].phone,
            email: users[userIndex].email,
            role: users[userIndex].role
        }, '修改用户信息成功');

    } catch (error) {
        logger.error('修改用户信息失败', error);
        Response.serverError(res, '修改用户信息失败');
    }
};

/**
 * 修改密码
 */
const changePassword = async(req, res) => {
    try {
        const { oldPassword, newPassword } = req.body;

        const user = users.find(u => u.id === req.user.id);
        if (!user) {
            return Response.notFound(res, '用户不存在');
        }

        // 验证旧密码
        const isValidOldPassword = await bcrypt.compare(oldPassword, user.password);
        if (!isValidOldPassword) {
            return Response.error(res, '原密码错误', 400);
        }

        // 加密新密码
        const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 10;
        const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

        // 更新密码
        user.password = hashedNewPassword;

        // 记录修改日志
        logger.info('用户修改密码', {
            userId: req.user.id,
            username: req.user.username,
            ip: req.ip
        });

        Response.success(res, null, '修改密码成功');

    } catch (error) {
        logger.error('修改密码失败', error);
        Response.serverError(res, '修改密码失败');
    }
};

module.exports = {
    login,
    register,
    logout,
    getProfile,
    updateProfile,
    changePassword
};