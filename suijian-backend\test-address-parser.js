const addressParser = require('./src/utils/addressParser');
const { execute } = require('./src/config/database');
const fs = require('fs');
const path = require('path');

/**
 * 测试地址解析功能
 */
async function testAddressParser() {
    console.log('=== 开始测试地址解析功能 ===');

    try {
        // 1. 首先执行数据库更新脚本
        console.log('1. 执行数据库更新脚本...');
        const sqlScript = fs.readFileSync(path.join(__dirname, 'database/create_communities_table.sql'), 'utf8');
        const statements = sqlScript.split(';').filter(stmt => stmt.trim());

        for (const statement of statements) {
            if (statement.trim()) {
                await execute(statement.trim());
            }
        }
        console.log('✅ 数据库更新脚本执行完成');

        // 2. 测试地址解析
        console.log('\n2. 测试地址解析功能...');

        const testAddresses = [
            '广东省清远市清城区阳光花园1栋101室',
            '广东省清远市清城区碧桂园A2栋302号',
            '广东省清远市清新区恒大城3号楼501房',
            '广东省清远市清城区保利城B1栋2楼201室',
            '广东省清远市清新区万科城5号楼3层301号',
            '广东省清远市清城区某小区2栋101',
            '广东省清远市清城区某花园A栋302',
            '广东省清远市清新区某城1号楼501',
            '广东省清远市清城区某苑B栋2楼201',
            '广东省清远市清新区某园3号楼3层301'
        ];

        console.log('测试地址列表:');
        testAddresses.forEach((address, index) => {
            console.log(`${index + 1}. ${address}`);
        });

        console.log('\n解析结果:');
        for (let i = 0; i < testAddresses.length; i++) {
            const address = testAddresses[i];
            const result = await addressParser.parseAddress(address);

            console.log(`\n${i + 1}. 原始地址: ${address}`);
            console.log(`   小区名称: ${result.communityName || '未识别'}`);
            console.log(`   楼栋号: ${result.building || '未识别'}`);
            console.log(`   房号: ${result.roomNumber || '未识别'}`);
        }

        // 3. 测试批量解析
        console.log('\n3. 测试批量地址解析...');
        const batchResults = await addressParser.parseAddresses(testAddresses);

        console.log('批量解析结果统计:');
        const stats = {
            total: batchResults.length,
            withCommunity: batchResults.filter(r => r.communityName).length,
            withBuilding: batchResults.filter(r => r.building).length,
            withRoomNumber: batchResults.filter(r => r.roomNumber).length,
            complete: batchResults.filter(r => r.communityName && r.building && r.roomNumber).length
        };

        console.log(`总地址数: ${stats.total}`);
        console.log(`识别出小区名称: ${stats.withCommunity}`);
        console.log(`识别出楼栋号: ${stats.withBuilding}`);
        console.log(`识别出房号: ${stats.withRoomNumber}`);
        console.log(`完整解析: ${stats.complete}`);

        // 4. 测试小区管理功能
        console.log('\n4. 测试小区管理功能...');

        // 获取所有小区
        const communities = await addressParser.getAllCommunities();
        console.log(`数据库中共有 ${communities.length} 个小区`);

        communities.forEach((community, index) => {
            console.log(`${index + 1}. ${community.name} (${community.alias || '无别名'})`);
        });

        // 5. 测试添加小区
        console.log('\n5. 测试添加小区功能...');
        const newCommunity = {
            name: '测试小区',
            alias: '测试小区,测试花园,测试苑',
            district: '清城区',
            city: '清远市',
            province: '广东省',
            address: '广东省清远市清城区测试小区'
        };

        const addResult = await addressParser.addCommunity(newCommunity);
        console.log(`添加小区结果: ${addResult ? '成功' : '失败'}`);

        // 6. 测试缓存功能
        console.log('\n6. 测试缓存功能...');
        const startTime = Date.now();
        await addressParser.parseAddress('广东省清远市清城区阳光花园1栋101室');
        const endTime = Date.now();
        console.log(`地址解析耗时: ${endTime - startTime}ms`);

        console.log('\n=== 地址解析功能测试完成 ===');

    } catch (error) {
        console.error('测试过程中发生错误:', error);
    }
}

/**
 * 测试特定地址格式
 */
async function testSpecificAddressFormats() {
    console.log('\n=== 测试特定地址格式 ===');

    const testCases = [{
            name: '标准格式',
            addresses: [
                '广东省清远市清城区阳光花园1栋101室',
                '广东省清远市清城区碧桂园A2栋302号',
                '广东省清远市清新区恒大城3号楼501房'
            ]
        },
        {
            name: '简化格式',
            addresses: [
                '阳光花园1栋101',
                '碧桂园A2栋302',
                '恒大城3号楼501'
            ]
        },
        {
            name: '复杂格式',
            addresses: [
                '广东省清远市清城区阳光花园A栋2楼201室',
                '广东省清远市清城区碧桂园B1栋3层301号',
                '广东省清远市清新区恒大城C2号楼4楼401房'
            ]
        },
        {
            name: '特殊格式',
            addresses: [
                '阳光花园1-101',
                '碧桂园A2-302',
                '恒大城3-501'
            ]
        }
    ];

    for (const testCase of testCases) {
        console.log(`\n${testCase.name}:`);
        for (const address of testCase.addresses) {
            const result = await addressParser.parseAddress(address);
            console.log(`  ${address} -> 小区:${result.communityName || '未识别'}, 楼栋:${result.building || '未识别'}, 房号:${result.roomNumber || '未识别'}`);
        }
    }
}

// 运行测试
if (require.main === module) {
    testAddressParser()
        .then(() => testSpecificAddressFormats())
        .then(() => {
            console.log('\n所有测试完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('测试失败:', error);
            process.exit(1);
        });
}

module.exports = {
    testAddressParser,
    testSpecificAddressFormats
};