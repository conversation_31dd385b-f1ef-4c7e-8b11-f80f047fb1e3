安检订单流程
1 导入excel表格： 在 维修单列表 中 点击 导入表格 按钮，导入excel表格

2 获取数据：获取 诉求渠道，工单类别，业务类型，工单号，催办信息，催办时间，补话信息，用户编号，用户姓名，固定电话，移动电话，地址，诉求描述，备注，接收部门，维修员，预约时间，预派维修员，诉求发起部门，诉求发起人，诉求时间，维修时间，上传时间，任务状态，退单原因，工单状态，审核状态，审核时间，审核员，费用合计金额，是否时限内处理，是否时限内联系客户，服务评价，BATCHID，工单处理结果，图片数量

3 处理数据：通过 工单号，查找数据库里是否存在数据
    存在数据：跳过
    不存在数据：保存进数据库，增加状态为待分派（状态有待分派，待执行，已完成，不作处理）

4 派单：在 维修单列表 中 通过 工单号，用户编号民，用户姓名，电话，小区等方式 搜索到一个或多个维修单，点击 派单按钮 弹出 派单界面，选择 需要 排给的 师傅，点击保存按钮，保存 派单信息 到数据库，此时这些维修单状态为 待执行

5 录入单个维修单（有异常情况）：在 维修单列表 中 通过 工单号，用户编号民，用户姓名，电话 等方式 搜索到对应的维修单，点击录入按钮，跳转到 维修单录入 页面 并显示数据库里的相应信息，在页面中 录入维修单信息，包括 时间，客户编号 ，电话，小区，地址，维修员，旧表号，新表号，维修原因，处理结果，图片，耗材列表等信息，点击保存按钮，保存 维修单信息 到数据库，此时维修单状态为 已完成（可以重复执行此步骤修改信息）

6 多个维修单正常入户（没有异常情况）：在 维修单列表 中 通过 工单号，用户编号民，用户姓名，电话，小区等方式 搜索到一个或多个维修单，点击 正常入户 按钮，数据库中保存维修订单的 入户情况 为 正常入户，此时这些维修单状态为 已完成

7 维修结算：在 维修结算 页面中选择结算月份，界面会对应切换到这个月的结算信息，包括 汇总，未处理，户内安全检查，安全隐患及风险明细表，换表明细，别墅正常入户明细，洋房正常入户明细这几个模块，除了未处理模块，其它都是只读状态，在未处理中显示这个月周期内状态不为已完成或不作处理的维修单，点击可以跳转到 维修单录入 页面 作相应的处理

8 导出excel表格：在 维修单列表 中 点击 导出表格 按钮，导出excel表格


工单流程
导入excel： 在 工单列表 中 点击 导入表格
派单
录入
核对
结算

增加一个 核对平料 UI示意图，并保存，在散户订单 下
有一个周期选择，先选择月份，然后有 4个按钮 分别是 1-7， 8-15， 16-23， 24-月底（自动计算）点击后显示对应周期
然后 显示该周期状态（未核对，未平料，已平料），有 下载Excel和 上传核对Excel 按钮， 有状态提示（未核对：未与甲方核对，未平料：未与仓库平料）
然后有几个标签页，都显示数据列表



1 从港华系统导出所有数据，导入我们系统
2 自动筛选出我们系统里面没有的，自动录入
3 派单：在列表中选择一个或多个（按小区或按其它），派给师傅
4 师傅完成之后上交表格，我们根据表格选择对应用户，增加 使用物料等其它信息，录入我们系统
5 按 16-23,24-30/31,1-7,8-15 日期为周期，导出这个周期的excel 表格，交给港华（需要核对所有工单材料信息是否正确，还有未核对的会提示）
6 港华核对完后返回一份核对表格，导入我们系统之后，这个周期显示准备平料
7 与仓库核对完后，在我们的系统里点击平帐，这一期的材料库存就封存存档，不能变更 
8 每个月与港华对接平帐，港华给出材料单价，导入系统，系统导出结算书excel

核对材料流程
打开我们系统的工单列表，再打开港华系统的工单详情，核对是否正确，确认正确后在我们的系统里点击核对完成，工单就会有个已核对标签


第3标签页 叫 表前 是个列表
第4个标签页 叫 户内 是个列表
第5个标签页 叫 超领材料 是个列表
第6个标签页 叫 管件（总） 是个列表
第7个标签页 叫 领料数及单价表 是个列表
第8个标签页 叫 其他增量工程 暂时为空
第9个标签页 叫 挂表管件 是个列表
第10个标签页 叫 零星管件 是个列表
第11个标签页 叫 二次安装管件 是个列表
第12个标签页 叫 管件统计表 是个列表
第13个标签页 叫 明装及暗埋(半月板 是个列表
第14个标签页 叫 明装及暗埋（未用半月板保护） 是个列表
第15个标签页 叫 零星安装（半月板 是个列表 
第16个标签页 叫 零星安装（未用半月板保护）是个列表
第17个标签页 叫 不可用燃气表换表 是个列表