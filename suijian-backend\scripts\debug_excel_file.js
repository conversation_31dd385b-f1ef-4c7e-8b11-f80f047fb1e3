#!/usr/bin/env node

/**
 * 诊断Excel文件问题
 */

const fs = require('fs');
const path = require('path');
const ExcelJS = require('exceljs');
const XLSX = require('xlsx');

/**
 * 检查文件基本信息
 */
function checkFileInfo(filePath) {
    console.log('📁 文件基本信息:');
    console.log('文件路径:', filePath);

    if (!fs.existsSync(filePath)) {
        console.log('❌ 文件不存在');
        return false;
    }

    const stats = fs.statSync(filePath);
    console.log('文件大小:', stats.size, 'bytes');
    console.log('创建时间:', stats.birthtime);
    console.log('修改时间:', stats.mtime);

    // 读取文件头信息
    const buffer = fs.readFileSync(filePath, { encoding: null });
    console.log('文件头(前16字节):', buffer.slice(0, 16).toString('hex'));

    return true;
}

/**
 * 尝试使用Excel库读取文件
 */
async function tryReadWithExcel(filePath) {
    console.log('\n📖 尝试使用Excel库读取...');

    try {
        const ext = path.extname(filePath).toLowerCase();
        let workbook;

        if (ext === '.xls') {
            console.log('使用XLSX库读取.xls文件...');
            workbook = XLSX.readFile(filePath);
        } else {
            console.log('使用ExcelJS库读取.xlsx文件...');
            workbook = new ExcelJS.Workbook();
            await workbook.xlsx.readFile(filePath);
        }

        console.log('✅ Excel文件读取成功');

        if (workbook.worksheets) {
            // ExcelJS格式
            console.log('工作表数量:', workbook.worksheets.length);
            workbook.worksheets.forEach((worksheet, index) => {
                console.log(`工作表 ${index + 1}: ${worksheet.name}`);
                console.log(`  行数: ${worksheet.rowCount}`);
                console.log(`  列数: ${worksheet.columnCount}`);
            });
        } else {
            // XLSX格式
            console.log('工作表数量:', workbook.SheetNames.length);
            workbook.SheetNames.forEach((sheetName, index) => {
                const worksheet = workbook.Sheets[sheetName];
                const range = XLSX.utils.decode_range(worksheet['!ref']);
                console.log(`工作表 ${index + 1}: ${sheetName}`);
                console.log(`  行数: ${range.e.r - range.s.r + 1}`);
                console.log(`  列数: ${range.e.c - range.s.c + 1}`);
            });
        }

        return true;
    } catch (error) {
        console.log('❌ Excel文件读取失败:', error.message);
        return false;
    }
}

/**
 * 检查文件扩展名
 */
function checkFileExtension(filePath) {
    console.log('\n🔍 检查文件扩展名...');

    const ext = path.extname(filePath).toLowerCase();
    console.log('文件扩展名:', ext);

    const validExtensions = ['.xlsx', '.xls'];
    if (!validExtensions.includes(ext)) {
        console.log('⚠️ 警告: 文件扩展名不是标准的Excel扩展名');
        console.log('支持的扩展名:', validExtensions.join(', '));
    } else {
        console.log('✅ 文件扩展名正确');
    }

    return validExtensions.includes(ext);
}

/**
 * 检查文件内容类型
 */
function checkFileContent(filePath) {
    console.log('\n🔍 检查文件内容...');

    try {
        const buffer = fs.readFileSync(filePath);

        // 检查Excel文件签名
        const xlsxSignature = [0x50, 0x4B, 0x03, 0x04]; // PK\x03\x04
        const xlsSignature = [0xD0, 0xCF, 0x11, 0xE0]; // \xD0\xCF\x11\xE0

        const fileSignature = Array.from(buffer.slice(0, 4));

        console.log('文件签名:', fileSignature.map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));

        if (fileSignature.every((byte, index) => byte === xlsxSignature[index])) {
            console.log('✅ 检测到XLSX文件签名');
            return 'xlsx';
        } else if (fileSignature.every((byte, index) => byte === xlsSignature[index])) {
            console.log('✅ 检测到XLS文件签名');
            return 'xls';
        } else {
            console.log('❌ 未检测到Excel文件签名');
            return 'unknown';
        }
    } catch (error) {
        console.log('❌ 读取文件内容失败:', error.message);
        return 'error';
    }
}

/**
 * 主函数
 */
async function main() {
    const args = process.argv.slice(2);

    if (args.length === 0) {
        console.log('用法: node debug_excel_file.js <文件路径>');
        console.log('示例: node debug_excel_file.js uploads/excel/test.xlsx');
        process.exit(1);
    }

    const filePath = args[0];
    console.log('🧪 Excel文件诊断工具');
    console.log('='.repeat(50));

    // 检查文件基本信息
    const fileExists = checkFileInfo(filePath);
    if (!fileExists) {
        process.exit(1);
    }

    // 检查文件扩展名
    const validExtension = checkFileExtension(filePath);

    // 检查文件内容
    const contentType = checkFileContent(filePath);

    // 尝试读取文件
    const readSuccess = await tryReadWithExcel(filePath);

    console.log('\n📊 诊断结果:');
    console.log('文件存在:', fileExists ? '✅' : '❌');
    console.log('扩展名正确:', validExtension ? '✅' : '❌');
    console.log('内容类型:', contentType);
    console.log('Excel读取:', readSuccess ? '✅' : '❌');

    if (!readSuccess) {
        console.log('\n💡 建议:');
        if (contentType === 'unknown') {
            console.log('- 文件可能不是真正的Excel文件');
            console.log('- 尝试用Excel打开并重新保存文件');
        } else if (contentType === 'xls') {
            console.log('- 文件是旧版Excel格式(.xls)');
            console.log('- 建议转换为新版格式(.xlsx)');
        } else {
            console.log('- 文件可能已损坏');
            console.log('- 尝试重新创建Excel文件');
        }
    }
}

// 运行诊断
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { checkFileInfo, tryReadWithExcel, checkFileExtension, checkFileContent };