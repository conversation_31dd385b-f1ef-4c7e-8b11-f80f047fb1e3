# 通用导入进度跟踪模块使用说明

## 概述

本模块基于物料列表的导入Excel实现，提供统一的进度跟踪功能，支持所有Excel导入场景的进度显示。模块采用内存存储，支持实时进度查询，自动清理已完成任务。

## 核心文件

- `src/utils/importProgressTracker.js` - 核心进度跟踪类
- `src/controllers/importProgressController.js` - 进度查询控制器
- `src/routes/importProgress.js` - 进度查询路由

## 功能特性

### ✅ 核心功能
- **任务创建与管理**：创建、更新、查询、删除导入任务
- **实时进度跟踪**：支持处理进度、成功/失败统计
- **多阶段进度**：支持读取、处理、插入等不同阶段
- **自动清理**：任务完成后20秒自动清理，避免内存泄漏
- **错误处理**：支持错误信息收集和查询

### ✅ 扩展功能
- **任务统计**：提供全局任务统计信息
- **批量操作**：支持批量清理已完成任务
- **资源管理**：进程退出时自动清理资源
- **内存优化**：限制错误和详情记录数量

## API接口

### 1. 获取导入进度
```
GET /api/import-progress/:taskId
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "taskId": "uuid-123",
    "status": "processing", // processing, done, error
    "total": 1000,
    "current": 500,
    "successCount": 480,
    "fail": 20,
    "message": "正在处理...",
    "startTime": 1640995200000,
    "endTime": null,
    "phase": "processing", // reading, processing, inserting, completed
    "phaseProgress": 50,
    "errors": [...], // 最近5个错误
    "successDetails": [...], // 最近3个成功记录
    "skipDetails": [...] // 最近3个跳过记录
  },
  "message": "获取导入进度成功"
}
```

### 2. 获取所有导入任务
```
GET /api/import-progress/tasks
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "tasks": [
      {
        "taskId": "uuid-123",
        "status": "processing",
        "total": 1000,
        "current": 500,
        "successCount": 480,
        "fail": 20,
        "message": "正在处理...",
        "startTime": 1640995200000,
        "endTime": null,
        "phase": "processing",
        "phaseProgress": 50,
        "errorCount": 5,
        "successDetailCount": 3,
        "skipDetailCount": 2
      }
    ],
    "total": 1,
    "stats": {
      "total": 1,
      "processing": 1,
      "completed": 0,
      "failed": 0
    }
  },
  "message": "获取导入任务列表成功"
}
```

### 3. 删除导入任务
```
DELETE /api/import-progress/tasks/:taskId
```

### 4. 获取统计信息
```
GET /api/import-progress/stats
```

### 5. 清理已完成任务
```
POST /api/import-progress/cleanup
```

## 使用方式

### 1. 在导入控制器中使用

```javascript
const importProgressTracker = require('../utils/importProgressTracker');

const importExcel = async(req, res) => {
    const taskId = uuidv4();
    
    try {
        // 1. 立即返回taskId给前端
        res.json({ 
            success: true, 
            taskId,
            message: '文件上传成功，开始处理...'
        });
        
        // 2. 创建进度跟踪任务
        importProgressTracker.createTask(taskId, 0);
        
        // 3. 读取Excel文件
        const data = await readExcelFile(filePath);
        importProgressTracker.updateTask(taskId, {
            total: data.length,
            phase: 'processing',
            message: '开始处理数据...'
        });
        
        // 4. 处理数据并更新进度
        for (let i = 0; i < data.length; i++) {
            // 处理单条数据
            await processData(data[i]);
            
            // 每处理100条更新一次进度
            if ((i + 1) % 100 === 0) {
                importProgressTracker.updateTask(taskId, {
                    current: i + 1,
                    successCount: successCount,
                    fail: failCount,
                    message: `已处理 ${i + 1}/${data.length} 条数据`
                });
            }
        }
        
        // 5. 完成任务
        importProgressTracker.completeTask(taskId, 'done', 
            `导入完成，成功${successCount}条，失败${failCount}条`);
            
    } catch (error) {
        // 6. 处理错误
        importProgressTracker.completeTask(taskId, 'error', '导入失败');
        importProgressTracker.addError(taskId, {
            message: error.message,
            stack: error.stack
        });
    }
};
```

### 2. 前端进度轮询

```javascript
// Vue 3 + Element Plus 示例
const importExcel = async (file) => {
    try {
        // 1. 上传文件
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await request.post('/api/materials/import', formData);
        
        if (response.success && response.taskId) {
            const taskId = response.taskId;
            
            // 2. 开始轮询进度
            pollImportProgress(taskId);
        }
    } catch (error) {
        ElMessage.error('上传失败');
    }
};

const pollImportProgress = async (taskId) => {
    const timer = setInterval(async () => {
        try {
            const response = await request.get(`/api/import-progress/${taskId}`);
            
            if (response.success) {
                const { status, total, current, successCount, fail, message, phaseProgress } = response.data;
                
                // 更新进度条
                importProgressPercent.value = phaseProgress;
                importProgressMessage.value = message;
                
                if (status === 'done') {
                    importProgressStatus.value = 'success';
                    clearInterval(timer);
                    ElMessage.success(message);
                    loadData(); // 刷新数据
                } else if (status === 'error') {
                    importProgressStatus.value = 'exception';
                    clearInterval(timer);
                    ElMessage.error(message);
                }
            }
        } catch (error) {
            clearInterval(timer);
            ElMessage.error('获取进度失败');
        }
    }, 1000); // 每秒轮询一次
};
```

## 迁移指南

### 从物料列表迁移

**原代码：**
```javascript
// 全局内存存储导入任务进度
const importTasks = {};

const importMaterialsExcel = async(req, res) => {
    const taskId = uuidv4();
    importTasks[taskId] = {
        status: 'processing',
        total: 0,
        current: 0,
        successCount: 0,
        fail: 0,
        message: '',
        startTime: Date.now(),
        endTime: null
    };
    res.json({ success: true, taskId });
    // ... 处理逻辑
};
```

**迁移后：**
```javascript
const importProgressTracker = require('../utils/importProgressTracker');

const importMaterialsExcel = async(req, res) => {
    const taskId = uuidv4();
    
    // 立即返回taskId
    res.json({ success: true, taskId });
    
    // 创建进度跟踪任务
    importProgressTracker.createTask(taskId, 0);
    
    // ... 处理逻辑，使用 importProgressTracker.updateTask() 更新进度
};
```

### 从散户订单迁移

**原代码：**
```javascript
const progressTracker = require('../utils/importProgress');

const importExcel = async(req, res) => {
    const batchId = uuidv4();
    progressTracker.createTask(batchId, 0);
    // ... 处理逻辑
};
```

**迁移后：**
```javascript
const importProgressTracker = require('../utils/importProgressTracker');

const importExcel = async(req, res) => {
    const taskId = uuidv4();
    
    // 立即返回taskId
    Response.success(res, { taskId, message: '开始处理...' });
    
    // 创建进度跟踪任务
    importProgressTracker.createTask(taskId, 0);
    
    // ... 处理逻辑，使用 importProgressTracker.updateTask() 更新进度
};
```

## 配置说明

### 自动清理配置

```javascript
// 在 importProgressTracker.js 中配置
const cleanupTime = 20000; // 20秒后清理
const cleanupInterval = 10000; // 每10秒检查一次
```

### 内存限制配置

```javascript
// 限制记录数量，避免内存溢出
const maxErrors = 50; // 最多保存50个错误
const maxSuccessDetails = 20; // 最多保存20个成功记录
const maxSkipDetails = 20; // 最多保存20个跳过记录
```

## 最佳实践

### 1. 进度更新频率
- 建议每处理100条数据更新一次进度
- 避免过于频繁的更新，影响性能

### 2. 错误处理
- 及时添加错误信息，便于调试
- 限制错误数量，避免内存溢出

### 3. 资源管理
- 任务完成后会自动清理，无需手动删除
- 进程退出时会自动清理所有资源

### 4. 前端轮询
- 建议1-2秒轮询一次
- 任务完成后及时停止轮询
- 添加错误处理，避免无限轮询

## 注意事项

1. **内存使用**：模块使用内存存储，大量任务时注意内存使用
2. **任务清理**：已完成任务20秒后自动清理，无需手动管理
3. **并发安全**：模块支持多任务并发，线程安全
4. **错误恢复**：进程重启后任务会丢失，需要重新导入
5. **网络超时**：前端轮询时注意网络超时处理

## 扩展功能

### 自定义进度阶段
```javascript
// 支持自定义进度阶段
importProgressTracker.updateTask(taskId, {
    phase: 'validating', // 自定义阶段
    phaseProgress: 30,
    message: '正在验证数据...'
});
```

### 添加详细记录
```javascript
// 添加成功记录
importProgressTracker.addSuccessDetail(taskId, {
    rowNumber: 1,
    data: { name: '物料1', code: 'M001' }
});

// 添加跳过记录
importProgressTracker.addSkipDetail(taskId, {
    rowNumber: 2,
    reason: '数据重复'
});
```

这个通用模块可以满足所有Excel导入场景的进度跟踪需求，提供了完整的API接口和详细的使用说明。 