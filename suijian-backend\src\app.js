const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const { logger } = require('./utils/logger');
const errorHandler = require('./middleware/errorHandler');
const routes = require('./routes');

const app = express();
const PORT = process.env.PORT || 3000;

// 安全中间件
app.use(helmet());

// 压缩中间件
app.use(compression());

// 日志中间件
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));

// 速率限制
const limiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15分钟
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // 限制每个IP 15分钟内最多100个请求
    message: {
        success: false,
        message: '请求过于频繁，请稍后再试'
    }
});
app.use(limiter);

// CORS配置
const corsOptions = {
    // origin: process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : ['http://localhost:3002'],
    origin: '*',
    credentials: true,
    optionsSuccessStatus: 200,
    exposedHeaders: ['Content-Disposition'], // 关键：暴露Content-Disposition头
    allowedHeaders: ['Content-Type', 'Authorization', 'Content-Length', 'X-Requested-With']
};
app.use(cors(corsOptions));

// 解析JSON请求体
app.use(express.json({ limit: '200mb' }));
app.use(express.urlencoded({ extended: true, limit: '200mb' }));

// 设置请求超时时间
app.use((req, res, next) => {
    // 对于文件上传接口，设置更长的超时时间
    if (req.path.includes('/import')) {
        req.setTimeout(600000); // 10分钟
        res.setTimeout(600000); // 10分钟
    }
    next();
});

// 文件上传中间件 - 使用multer，移除express-fileupload避免冲突

// 静态文件服务
app.use('/uploads', express.static('uploads'));

// API路由
app.use('/api', routes);

// 健康检查
app.get('/health', (req, res) => {
    res.json({
        success: true,
        message: '服务运行正常',
        timestamp: new Date().toISOString(),
        version: process.env.APP_VERSION || '1.0.0'
    });
});

// 404处理
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: '接口不存在'
    });
});

// 错误处理中间件
app.use(errorHandler);

// 启动服务器
const startServer = () => {
    try {
        // 启动HTTP服务器
        app.listen(PORT, () => {
            logger.info(`服务器启动成功，端口: ${PORT}`);
            logger.info(`环境: ${process.env.NODE_ENV || 'development'}`);
            logger.info(`应用名称: ${process.env.APP_NAME || '工程管理系统'}`);
        });
    } catch (error) {
        logger.error('服务器启动失败:', error);
        process.exit(1);
    }
};

startServer();

// 优雅关闭
process.on('SIGTERM', () => {
    logger.info('收到SIGTERM信号，正在关闭服务器...');
    process.exit(0);
});

process.on('SIGINT', () => {
    logger.info('收到SIGINT信号，正在关闭服务器...');
    process.exit(0);
});

module.exports = app;