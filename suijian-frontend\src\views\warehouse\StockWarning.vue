<template>
  <div class="stock-warning-container">
    <el-card class="main-card">
      <!-- 预警设置 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="12">
          <el-form-item label="预警方式:">
            <el-checkbox-group v-model="warningSettings.warningMethods">
              <el-checkbox label="system">系统消息</el-checkbox>
              <el-checkbox label="email">邮件通知</el-checkbox>
              <el-checkbox label="sms">短信通知</el-checkbox>
              <el-checkbox label="wechat">微信通知</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预警频率:">
            <el-select v-model="warningSettings.warningFrequency" placeholder="请选择预警频率" style="width: 100%">
              <el-option label="实时预警" value="realtime" />
              <el-option label="每日汇总" value="daily" />
              <el-option label="每周汇总" value="weekly" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="库存不足预警:">
            <el-input v-model="warningSettings.lowStockThreshold" placeholder="请输入库存不足预警阈值">
              <template #append>件</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="库存积压预警:">
            <el-input v-model="warningSettings.overstockThreshold" placeholder="请输入库存积压预警阈值">
              <template #append>件</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="接收人员:">
            <el-select 
              v-model="warningSettings.recipients" 
              multiple 
              placeholder="请选择接收人员" 
              style="width: 100%"
            >
              <el-option
                v-for="user in userList"
                :key="user.id"
                :label="user.name"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 物料列表 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <el-table :data="materialList" border class="material-table">
            <el-table-column prop="materialCode" label="公司物料编码" width="150" />
            <el-table-column prop="category" label="物料分类" width="90">
              <template #default="scope">
                <el-tag :type="getCategoryTag(scope.row.category)">
                  {{ scope.row.category }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="materialName" label="物料名称" min-width="120" />
            <el-table-column prop="specification" label="规格" min-width="100" />
            <el-table-column prop="unit" label="单位" width="60" />
            <el-table-column prop="currentStock" label="当前库存" width="90" />
            <el-table-column prop="lowStockThreshold" label="不足预警阈值" width="120">
              <template #default="scope">
                <el-input-number 
                  v-model="scope.row.lowStockThreshold" 
                  :min="0" 
                  controls-position="right" 
                  style="width: 100%" 
                />
              </template>
            </el-table-column>
            <el-table-column prop="overstockThreshold" label="积压预警阈值" width="120">
              <template #default="scope">
                <el-input-number 
                  v-model="scope.row.overstockThreshold" 
                  :min="0" 
                  controls-position="right" 
                  style="width: 100%" 
                />
              </template>
            </el-table-column>
            <el-table-column prop="warningEnabled" label="启用预警" width="90">
              <template #default="scope">
                <el-switch 
                  v-model="scope.row.warningEnabled" 
                  @change="toggleWarning(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="90" fixed="right">
              <template #default="scope">
                <el-button type="primary" link @click="saveThreshold(scope.row)">保存</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="pagination"
      />
      

    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 预警设置
const warningSettings = reactive({
  warningMethods: ['system', 'email'],
  warningFrequency: 'realtime',
  lowStockThreshold: '10',
  overstockThreshold: '1000',
  recipients: [1, 2]
})

// 用户列表
const userList = ref([
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' },
  { id: 4, name: '赵六' }
])

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 156
})

// 物料列表
const materialList = ref([
  {
    id: 1,
    materialCode: 'WL001',
    partyCode: ['JD001', 'JD002'],
    category: '甲料',
    materialName: '电缆线',
    model: 'YJV-3*4',
    specification: '3*4mm²',
    unit: '米',
    currentStock: 5,
    lowStockThreshold: 10,
    overstockThreshold: 1000,
    warningEnabled: true
  },
  {
    id: 2,
    materialCode: 'WL002',
    partyCode: ['JD003'],
    category: '商品',
    materialName: '开关面板',
    model: 'KP-86',
    specification: '86型',
    unit: '个',
    currentStock: 1200,
    lowStockThreshold: 20,
    overstockThreshold: 1000,
    warningEnabled: true
  },
  {
    id: 3,
    materialCode: 'WL003',
    partyCode: ['JD004', 'JD005'],
    category: '辅料',
    materialName: '电线',
    model: 'BV-2.5',
    specification: '2.5mm²',
    unit: '米',
    currentStock: 8,
    lowStockThreshold: 10,
    overstockThreshold: 800,
    warningEnabled: false
  },
  {
    id: 4,
    materialCode: 'WL004',
    partyCode: ['JD006'],
    category: '甲料',
    materialName: '水管',
    model: 'PPR-20',
    specification: '20mm',
    unit: '米',
    currentStock: 1500,
    lowStockThreshold: 50,
    overstockThreshold: 1000,
    warningEnabled: true
  },
  {
    id: 5,
    materialCode: 'WL005',
    partyCode: ['JD007'],
    category: '商品',
    materialName: '灯具',
    model: 'LED-12W',
    specification: '12W',
    unit: '个',
    currentStock: 3,
    lowStockThreshold: 10,
    overstockThreshold: 500,
    warningEnabled: true
  },
  {
    id: 6,
    materialCode: 'WL006',
    partyCode: ['JD008'],
    category: '辅料',
    materialName: '螺丝',
    model: 'M4*20',
    specification: '4mm*20mm',
    unit: '个',
    currentStock: 2000,
    lowStockThreshold: 100,
    overstockThreshold: 2000,
    warningEnabled: false
  },
  {
    id: 7,
    materialCode: 'WL007',
    partyCode: ['JD009', 'JD010'],
    category: '甲料',
    materialName: '阀门',
    model: 'DN25',
    specification: '25mm',
    unit: '个',
    currentStock: 15,
    lowStockThreshold: 5,
    overstockThreshold: 100,
    warningEnabled: true
  },
  {
    id: 8,
    materialCode: 'WL008',
    partyCode: ['JD011'],
    category: '商品',
    materialName: '插座',
    model: 'ZP-86',
    specification: '86型',
    unit: '个',
    currentStock: 80,
    lowStockThreshold: 20,
    overstockThreshold: 300,
    warningEnabled: true
  }
])



// 获取分类标签类型
const getCategoryTag = (category: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  switch (category) {
    case '甲料':
      return 'primary'
    case '商品':
      return 'success'
    case '辅料':
      return 'warning'
    default:
      return 'info'
  }
}



// 切换预警开关
const toggleWarning = (row: any) => {
  const status = row.warningEnabled ? '启用' : '禁用'
  ElMessage.success(`已${status}预警`)
}

// 保存阈值
const saveThreshold = (row: any) => {
  ElMessage.success('保存成功')
  console.log('保存阈值:', row)
}

// 分页变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  console.log('每页条数:', val)
}

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  console.log('当前页:', val)
}
</script>

<style lang="scss" scoped>
.stock-warning-container {
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  
  .material-table {
    margin-top: 10px;
  }
  
  .pagination {
    margin-top: 20px;
    text-align: right;
  }
  

}
</style>