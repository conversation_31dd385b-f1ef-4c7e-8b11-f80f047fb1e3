-- 更新物料记录表结构
-- 将原来的 material_records 表拆分为主表和明细表

-- 1. 创建新的明细表
CREATE TABLE IF NOT EXISTS material_record_items (
    id VARCHAR(36) PRIMARY KEY,
    record_id VARCHAR(36) NOT NULL,
    material_id VARCHAR(36) NOT NULL,
    quantity INTEGER NOT NULL,
    current_quantity INTEGER NOT NULL,
    unit_price REAL,
    total_amount REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 2. 备份原有数据（如果需要）
-- 注意：这个脚本假设原有表结构需要保留，如果需要迁移数据，请先备份

-- 3. 删除原有表（如果存在）
DROP TABLE IF EXISTS material_records_old;

-- 4. 重命名原表为备份表
ALTER TABLE material_records RENAME TO material_records_old;

-- 5. 创建新的主表
CREATE TABLE material_records (
    id VARCHAR(36) PRIMARY KEY,
    type VARCHAR(20) NOT NULL,
    operator_id VARCHAR(36) NOT NULL,
    order_id VARCHAR(36),
    supplier VARCHAR(100),
    batch_number VARCHAR(50),
    recipient VARCHAR(50),
    purpose TEXT,
    remarks TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 6. 创建索引
CREATE INDEX IF NOT EXISTS idx_material_records_type ON material_records(type);
CREATE INDEX IF NOT EXISTS idx_material_records_operator ON material_records(operator_id);
CREATE INDEX IF NOT EXISTS idx_material_records_created ON material_records(created_at);
CREATE INDEX IF NOT EXISTS idx_material_record_items_record ON material_record_items(record_id);
CREATE INDEX IF NOT EXISTS idx_material_record_items_material ON material_record_items(material_id);

-- 7. 数据迁移（可选，如果需要从旧表迁移数据）
-- 注意：这个迁移脚本需要根据实际数据情况调整
/*
INSERT INTO material_records (id, type, operator_id, order_id, supplier, batch_number, recipient, purpose, remarks, created_at)
SELECT 
    id,
    type,
    operator_id,
    order_id,
    supplier,
    batch_number,
    recipient,
    purpose,
    remarks,
    created_at
FROM material_records_old;

INSERT INTO material_record_items (id, record_id, material_id, quantity, current_quantity, unit_price, total_amount, created_at)
SELECT 
    generatePrefixedId('mri'),
    id as record_id,
    material_id,
    quantity,
    quantity as current_quantity, -- 对于历史数据，使用quantity作为current_quantity
    unit_price,
    total_amount,
    created_at
FROM material_records_old;
*/

-- 8. 删除备份表（确认数据迁移成功后执行）
-- DROP TABLE IF EXISTS material_records_old; 