# 简化nginx配置 - 解决重定向循环问题
server {
    listen 80;
    server_name www.suijianranqi.cn suijianranqi.cn;
    
    # 设置根目录
    root /data/wwwroot/mainpage;
    index index.html;

    # 静态资源 - 直接处理，不重定向
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # API请求 - 直接返回404，避免重定向
    location /api/ {
        return 404;
    }

    # 所有其他请求返回index.html
    location / {
        try_files $uri $uri/ /index.html;
    }
} 