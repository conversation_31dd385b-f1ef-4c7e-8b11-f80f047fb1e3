# 登录接口测试说明

## 修改内容

### 1. Vite配置修改
- 修改了 `vite.config.ts` 中的代理配置
- 将API请求代理到 `http://localhost:3000`（后端服务地址）

### 2. Request工具修改
- 修改了 `src/utils/request.ts`
- 登录接口 `/api/auth/login` 使用真实后端API
- 其他接口继续使用mock数据

### 3. Auth Store修改
- 修改了 `src/stores/auth.ts`
- 适配后端API的响应格式：`{ success: true, message: '登录成功', data: { token, user } }`

### 4. 登录页面修改
- 修改了默认密码为 `password`（与后端默认用户匹配）

## 测试步骤

### 1. 启动后端服务
```bash
cd suijian-backend
npm install
npm run dev
```

### 2. 启动前端服务
```bash
cd suijian-frontend
npm run dev
```

### 3. 测试登录
- 访问前端页面：http://localhost:3002
- 使用以下默认用户登录：
  - 用户名：`admin`
  - 密码：`password`

## 后端默认用户

系统预置了以下默认用户：

| 用户名 | 密码 | 角色 | 描述 |
|--------|------|------|------|
| admin | password | admin | 系统管理员 |
| manager | password | manager | 项目经理 |
| worker | password | worker | 普通员工 |

## 注意事项

1. 确保后端服务在 `localhost:3000` 端口运行
2. 前端服务在 `localhost:3002` 端口运行
3. 登录接口会直接调用后端API，其他接口仍使用mock数据
4. 如果登录失败，请检查后端服务是否正常运行 