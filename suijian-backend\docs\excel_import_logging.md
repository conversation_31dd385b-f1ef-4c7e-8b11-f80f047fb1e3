# Excel导入日志功能说明

## 📋 功能概述

工单列表的Excel导入功能现在包含了详细的日志记录，可以帮助您：

- 🔍 **实时监控**：查看导入进度和状态
- 📊 **统计分析**：了解导入成功率和性能
- 🐛 **错误诊断**：快速定位和解决问题
- 📈 **性能优化**：分析导入耗时和瓶颈

## 🔧 日志记录内容

### 1. 导入开始日志
```
=== 开始Excel导入 ===
- 批次ID (唯一标识)
- 文件名和大小
- 用户ID
- 开始时间
```

### 2. 文件处理日志
```
文件上传成功
- 文件路径
- 原始文件名
- 文件大小

Excel文件读取完成
- 总行数
- 工作表名称

Excel文件结构分析
- 表头信息
- 数据行数
- 列数统计
```

### 3. 数据验证日志
```
Excel文件验证通过，开始处理数据
- 总行数
- 必要列验证结果

Excel文件缺少必要列 (警告)
- 缺失的列名
- 实际存在的列名
```

### 4. 行级处理日志
```
处理第X行数据
- 行号
- 原始数据

第X行数据映射完成
- 工单号
- 工单类型
- 状态
- 创建人
- 项目

第X行跳过: 工单号已存在
- 行号
- 工单号

第X行准备插入数据库
- 行号
- 工单号
- 生成的ID
- 地址信息
- 诉求描述

第X行插入成功
- 行号
- 工单号
- 生成的ID
```

### 5. 进度日志
```
导入进度: X/Y
- 已处理行数
- 总行数
- 成功数
- 跳过数
- 错误数
```

### 6. 完成日志
```
=== Excel导入完成 ===
- 总行数
- 成功数
- 跳过数
- 错误数
- 总耗时
- 结束时间
- 成功详情 (前5条)
- 跳过详情 (前5条)
- 错误详情 (前5条)
```

### 7. 错误日志
```
第X行导入失败
- 行号
- 工单号
- 错误信息
- 错误堆栈

导入错误详情
- 总错误数
- 所有错误列表
```

## 🛠️ 日志查看工具

### 安装和使用

1. **进入后端目录**：
   ```bash
   cd suijian-backend
   ```

2. **查看导入统计**：
   ```bash
   node scripts/view_import_logs.js stats
   ```

3. **查看所有批次列表**：
   ```bash
   node scripts/view_import_logs.js list
   ```

4. **查看指定批次详细日志**：
   ```bash
   node scripts/view_import_logs.js detail <batchId>
   ```

5. **查看错误日志**：
   ```bash
   node scripts/view_import_logs.js errors
   ```

### 工具功能说明

#### 1. 统计信息 (stats)
显示所有导入批次的汇总信息：
- 总导入批次数量
- 每个批次的文件名、大小、开始时间
- 每个批次的处理结果（成功、跳过、失败数量）
- 每个批次的耗时

#### 2. 批次列表 (list)
显示所有批次的简要信息：
- 批次ID
- 开始时间
- 处理结果
- 耗时

#### 3. 详细日志 (detail)
显示指定批次的完整处理过程：
- 每行的处理状态
- 数据映射结果
- 数据库操作详情
- 错误信息

#### 4. 错误日志 (errors)
显示所有导入过程中的错误：
- 错误时间
- 错误类型
- 错误详情
- 相关行号和工单号

## 📊 日志示例

### 成功导入示例
```
[16:53:57] info  === 开始Excel导入 === {"batchId":"abc123-def456","fileName":"工单数据.xlsx","fileSize":1048576,"userId":"user123","startTime":"2025-08-08T08:53:57.123Z"}

[16:53:58] info  文件上传成功 {"batchId":"abc123-def456","filePath":"./uploads/excel/1234567890_工单数据.xlsx","originalName":"工单数据.xlsx","size":1048576}

[16:53:59] info  Excel文件读取完成 {"batchId":"abc123-def456","totalRows":1001,"sheetName":"Sheet1"}

[16:54:00] info  处理第2行数据 {"batchId":"abc123-def456","rowNumber":2,"rowData":["WO001","维修","待处理","系统","设备故障","设备无法启动","张三","2025-08-08"]}

[16:54:01] info  第2行插入成功 {"batchId":"abc123-def456","rowNumber":2,"orderNo":"WO001","id":"new-uuid-123"}

[16:54:30] info  导入进度: 100/1000 {"batchId":"abc123-def456","processed":100,"total":1000,"success":95,"skip":3,"error":2}

[16:55:00] info  === Excel导入完成 === {"batchId":"abc123-def456","totalRows":1000,"successCount":950,"skipCount":30,"errorCount":20,"duration":"63000ms"}
```

### 错误导入示例
```
[16:53:57] error 第15行导入失败 {"batchId":"abc123-def456","rowNumber":15,"orderNo":"WO015","error":"SQLITE_CONSTRAINT: UNIQUE constraint failed: loose_orders.order_no","stack":"Error: SQLITE_CONSTRAINT..."}

[16:55:00] error 导入错误详情 {"batchId":"abc123-def456","totalErrors":20,"errors":["第15行: 工单号已存在","第23行: 数据格式错误",...]}
```

## 🔍 日志分析技巧

### 1. 性能分析
- 查看 `duration` 字段了解导入耗时
- 对比不同批次的处理速度
- 识别性能瓶颈（如数据库操作慢）

### 2. 错误分析
- 使用 `errors` 命令查看所有错误
- 根据错误类型分类处理
- 重点关注重复出现的错误

### 3. 数据质量分析
- 统计跳过和失败的比例
- 分析数据格式问题
- 优化Excel模板

### 4. 批次管理
- 使用批次ID追踪特定导入
- 对比不同时间段的导入结果
- 监控导入趋势

## 📝 日志配置

### 日志级别
- `info`: 正常操作信息
- `warn`: 警告信息（如缺少列）
- `error`: 错误信息

### 日志文件位置
- 文件路径: `logs/app.log`
- 自动按日期轮转
- 保留最近30天的日志

### 日志格式
```
时间戳 级别: 消息内容 {JSON数据}
```

## 🚀 最佳实践

### 1. 定期检查日志
- 每次导入后查看统计信息
- 定期分析错误模式
- 监控导入性能趋势

### 2. 优化导入流程
- 根据日志分析优化数据格式
- 调整批量处理大小
- 优化数据库查询

### 3. 问题排查
- 使用批次ID快速定位问题
- 查看详细日志了解处理过程
- 分析错误堆栈信息

### 4. 性能监控
- 记录导入耗时
- 监控内存使用
- 分析数据库性能

## 🔧 故障排除

### 常见问题

1. **日志文件不存在**
   - 检查 `logs` 目录是否存在
   - 确认应用有写入权限

2. **日志格式错误**
   - 检查日志解析正则表达式
   - 确认日志输出格式一致

3. **批次ID不匹配**
   - 确认批次ID格式正确
   - 检查日志中的批次ID

4. **性能问题**
   - 查看耗时较长的操作
   - 分析数据库查询性能
   - 考虑分批处理

---

通过详细的日志记录，您可以全面了解Excel导入的每个环节，快速定位问题，优化导入性能，确保数据质量。 