const importProgressTracker = require('./src/utils/importProgressTracker');

// 测试进度跟踪功能
async function testProgressTracking() {
    console.log('=== 测试进度跟踪功能 ===');

    // 1. 创建任务
    const taskId = 'test-task-' + Date.now();
    console.log('1. 创建任务:', taskId);
    importProgressTracker.createTask(taskId, 100);

    // 2. 更新进度
    console.log('2. 更新进度...');
    for (let i = 0; i <= 100; i += 20) {
        importProgressTracker.updateTask(taskId, {
            current: i,
            successCount: Math.floor(i * 0.8),
            fail: Math.floor(i * 0.1),
            phaseProgress: i
        });

        const task = importProgressTracker.getTask(taskId);
        console.log(`   进度: ${i}/100 (${task.phaseProgress}%) - 成功:${task.successCount}, 失败:${task.fail}`);

        await new Promise(resolve => setTimeout(resolve, 500));
    }

    // 3. 完成任务
    console.log('3. 完成任务');
    importProgressTracker.completeTask(taskId, 'done', '测试完成');

    // 4. 查询任务
    const finalTask = importProgressTracker.getTask(taskId);
    console.log('4. 最终任务状态:', {
        taskId: finalTask.taskId,
        status: finalTask.status,
        total: finalTask.total,
        current: finalTask.current,
        successCount: finalTask.successCount,
        fail: finalTask.fail,
        message: finalTask.message,
        phaseProgress: finalTask.phaseProgress
    });

    // 5. 获取统计信息
    const stats = importProgressTracker.getStats();
    console.log('5. 统计信息:', stats);

    // 6. 清理任务
    console.log('6. 清理任务');
    importProgressTracker.deleteTask(taskId);

    const cleanedTask = importProgressTracker.getTask(taskId);
    console.log('   清理后任务状态:', cleanedTask);

    console.log('=== 测试完成 ===');
}

// 运行测试
testProgressTracking().catch(console.error);