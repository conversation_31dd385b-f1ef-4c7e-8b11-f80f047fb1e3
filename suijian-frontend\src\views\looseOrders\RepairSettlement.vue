<template>
  <div class="repair-settlement">
    <el-card class="settlement-card">
      <!-- 月份选择 -->
      <div class="month-selector">
        <el-form :inline="true">
          <el-form-item label="结算月份">
            <el-date-picker
              v-model="selectedMonth"
              type="month"
              placeholder="选择结算月份"
              format="YYYY-MM"
              value-format="YYYY-MM"
              @change="handleMonthChange"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="success" @click="handleExportExcel">导出Excel</el-button>
            <el-upload
              :show-file-list="false"
              :before-upload="handleImportSpotCheckExcel"
              accept=".xls,.xlsx"
            >
              <el-button type="warning" style="margin-left: 8px;">导入抽查明细Excel</el-button>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>

      <!-- 汇总信息 -->
      <!-- <div class="summary-line">
        <span class="summary-badge badge-blue">总维修单数：{{ summary.totalOrders }}</span>
        <span class="summary-badge badge-green">已完成：{{ summary.completedOrders }}</span>
        <span class="summary-badge badge-orange">未处理：{{ summary.pendingOrders }}</span>
        <span class="summary-badge badge-purple">总金额：¥{{ summary.totalAmount }}</span>
      </div> -->

      <!-- 模块标签页 -->
      <div class="module-tabs">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="未处理" name="pending">
            <div class="tab-content">
              <el-table
                :data="pendingOrders"
                border
                stripe
                style="width: 100%"
                v-loading="loading"
              >
                <el-table-column type="selection" width="45" fixed="left" />
                <el-table-column prop="dispatchTime" label="派单时间" width="150" />
                <el-table-column prop="customerNumber" label="客户编号" width="120" />
                <el-table-column prop="userName" label="用户姓名" width="120" />
                <el-table-column prop="phone" label="电话" width="130" />
                <el-table-column prop="community" label="小区" min-width="150" />
                <el-table-column prop="address" label="地址" min-width="200" />
                <el-table-column prop="repairman" label="维修员" width="100" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getStatusType(row.status)" size="small">
                      {{ getStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="entryStatus" label="正常入户" width="120">
                  <template #default="{ row }">
                    <el-tag :type="getEntryStatusType(row.entryStatus)" size="small">
                      {{ getEntryStatusText(row.entryStatus) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                  <template #default="{ row }">
                    <div style="display: flex; gap: 5px; justify-content: flex-end;">
                      <el-button type="primary" size="small" @click="handleProcess(row)">
                        处理
                      </el-button>
                      <el-button type="info" size="small" @click="handleView(row)">
                        查看
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="封面" name="cover">
            <div class="tab-content">
              <div class="settlement-cover">
                <div class="cover-header">
                  <el-select
                    v-model="coverData.companyName"
                    filterable
                    allow-create
                    clearable
                    placeholder="请输入或选择公司名称"
                    class="company-name-input"
                    style="width: 100%;"
                  >
                    <el-option
                      v-for="item in companyNameOptions"
                      :key="item"
                      :label="item"
                      :value="item"
                    />
                  </el-select>
                  <h2 class="document-title">项目预(结)算书</h2>
                </div>
                
                <div class="cover-content">
                  <div class="left-column">
                    <div class="form-row">
                      <span class="label">项目名称:</span>
                      <span class="value">{{ coverData.projectName }}</span>
                    </div>
                    <div class="form-row">
                      <span class="label">CEA编码:</span>
                      <el-input v-model="coverData.ceaCode" placeholder="请输入CEA编码" clearable style="max-width: 300px;" />
                    </div>
                    <div class="form-row">
                      <span class="label">送审人工费:</span>
                      <span class="value">{{ coverData.submittedLaborCost }}元</span>
                    </div>
                    <div class="form-row">
                      <span class="label">二审造价:</span>
                      <span class="value">{{ coverData.secondReviewCost }}元</span>
                    </div>
                    <div class="form-row">
                      <span class="label">结算编号:</span>
                      <span class="value">{{ coverData.settlementNumber }}</span>
                    </div>
                    <div class="form-row">
                      <span class="label">应付金额:</span>
                      <span class="value">{{ coverData.amountPayableChinese }}</span>
                      <span class="currency-symbol">¥: {{ coverData.amountPayable }}元</span>
                    </div>
                    <div class="form-row">
                      <span class="label">施工单位:</span>
                      <el-select
                        v-model="coverData.constructionUnit"
                        filterable
                        allow-create
                        clearable
                        placeholder="请输入或选择施工单位"
                        style="max-width: 300px; width: 100%;"
                      >
                        <el-option
                          v-for="item in constructionUnitOptions"
                          :key="item"
                          :label="item"
                          :value="item"
                        />
                      </el-select>
                    </div>
                    <div class="form-row">
                      <span class="label">建设单位:</span>
                      <el-select
                        v-model="coverData.clientUnit"
                        filterable
                        allow-create
                        clearable
                        placeholder="请输入或选择建设单位"
                        style="max-width: 300px; width: 100%;"
                      >
                        <el-option
                          v-for="item in clientUnitOptions"
                          :key="item"
                          :label="item"
                          :value="item"
                        />
                      </el-select>
                    </div>
                  </div>
                  
                  <div class="right-column">
                    <div class="form-row">
                      <span class="label">总造价:</span>
                      <span class="value">{{ coverData.totalCost }}元</span>
                    </div>
                    <div class="form-row">
                      <span class="label">人工结算:</span>
                      <span class="value">{{ coverData.laborSettlement }}元</span>
                    </div>
                    <div class="form-row">
                      <span class="label">材料结算:</span>
                      <span class="value">{{ coverData.materialSettlement }}元</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="分部分项汇总" name="summary">
            <div class="tab-content">
              <div class="summary-table-wrapper">
                <div class="summary-title">安全检查费汇总表</div>
                <div class="summary-meta">
                  <span class="summary-label">项目名称：</span>
                  <span class="summary-value">{{ coverData.projectName }}</span>
                  <span class="summary-page">第1页/共1页</span>
                </div>
                <table class="summary-table">
                  <thead>
                    <tr>
                      <th style="width: 60px;">序号</th>
                      <th style="width: 300px;">项目及说明</th>
                      <th style="width: 180px;">合同内人工（不含材料费）</th>
                      <th style="width: 180px;">乙供材料</th>
                      <th style="width: 120px;">备注</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>1</td>
                      <td>居民户内安全检查人工费用</td>
                      <td class="text-right">86,957.00</td>
                      <td></td>
                      <td></td>
                    </tr>
                    <tr>
                      <td>2</td>
                      <td>居民户内隐患整改费用</td>
                      <td class="text-right">20,122.77</td>
                      <td></td>
                      <td></td>
                    </tr>
                    <tr>
                      <td colspan="2" class="text-right">总人工：</td>
                      <td class="text-right">107,079.77</td>
                      <td></td>
                      <td></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="户内安全检查" name="safety-check">
            <div class="tab-content">
              <el-table
                :data="safetyCheckData"
                border
                stripe
                style="width: 100%"
                v-loading="loading"
                :span-method="handleSpanMethod"
              >
                <el-table-column prop="serialNumber" label="序号" width="60" fixed="left" />
                <el-table-column prop="communityName" label="小区名称" width="150" fixed="left" />
                
                <!-- 入户安检模块 -->
                <el-table-column label="入户安检" align="center">
                  <el-table-column prop="plannedInspection" label="计划安检(户)" width="100" />
                  <el-table-column label="成功安检数" align="center">
                    <el-table-column prop="successfulInspection" label="户" width="80" />
                    <el-table-column prop="inspectionAmount" label="小计(金额)" width="120" />
                  </el-table-column>
                  <el-table-column prop="successRate" label="计划安检成功率" width="120">
                    <template #default="{ row }">
                      {{ row.successRate }}%
                    </template>
                  </el-table-column>
                </el-table-column>
                
                <!-- 其他计件模块 -->
                <el-table-column label="其他计件" align="center">
                  <el-table-column label="换表" align="center">
                    <el-table-column prop="meterReplacementCount" label="个" width="80" />
                    <el-table-column prop="meterReplacementAmount" label="小计(金额)" width="120" />
                  </el-table-column>
                  <el-table-column label="更换燃气具连接管" align="center">
                    <el-table-column prop="pipeReplacementCount" label="条" width="80" />
                    <el-table-column prop="pipeReplacementAmount" label="小计(金额)" width="120" />
                  </el-table-column>
                  <el-table-column label="较大风险成功完成整改" align="center">
                    <el-table-column prop="riskRectificationCount" label="处" width="80" />
                    <el-table-column prop="riskRectificationAmount" label="小计(金额)" width="120" />
                  </el-table-column>
                </el-table-column>
                
                <!-- 管道维修模块 -->
                <el-table-column label="管道维修" align="center">
                  <el-table-column label="拆除、安装管道" align="center">
                    <el-table-column prop="pipelineMeters" label="米" width="80" />
                    <el-table-column prop="pipelineAmount" label="小计(金额)" width="120" />
                  </el-table-column>
                  <el-table-column label="燃气表拆、装" align="center">
                    <el-table-column prop="meterInstallByHousehold" label="户" width="80" />
                    <el-table-column prop="meterInstallByHouseholdAmount" label="小计(金额)" width="120" />
                  </el-table-column>
                  <el-table-column label="表前阀更换" align="center">
                    <el-table-column prop="valveReplacementCount" label="个" width="80" />
                    <el-table-column prop="valveReplacementAmount" label="小计(金额)" width="120" />
                  </el-table-column>
                  <el-table-column label="户内低低压调压器更换" align="center">
                    <el-table-column prop="regulatorReplacementCount" label="个" width="80" />
                    <el-table-column prop="regulatorReplacementAmount" label="小计(金额)" width="120" />
                  </el-table-column>
                  <el-table-column label="燃气表拆、装" align="center">
                    <el-table-column prop="meterInstallByUnit" label="个" width="80" />
                    <el-table-column prop="meterInstallByUnitAmount" label="小计(金额)" width="120" />
                  </el-table-column>
                  <el-table-column label="二次进场费" align="center">
                    <el-table-column prop="secondVisitCount" label="户" width="80" />
                    <el-table-column prop="secondVisitAmount" label="小计(金额)" width="120" />
                  </el-table-column>
                </el-table-column>
                
                <!-- 扣罚模块 -->
                <el-table-column label="扣罚" align="center">
                  <el-table-column prop="penaltyPoints" label="分" width="80" />
                  <el-table-column prop="penaltyAmount" label="小计(金额)" width="120" />
                </el-table-column>
                
                <!-- 维修小计 -->
                <el-table-column label="维修小计" align="center">
                  <el-table-column prop="maintenanceSubtotal" label="小计(金额)" width="120" />
                </el-table-column>
                
                <!-- 合计 -->
                <el-table-column label="合计" align="center">
                  <el-table-column prop="totalAmount" label="元" width="120" />
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="安全隐患及风险明细" name="risk-details">
            <div class="tab-content">
              <el-table
                :data="riskDetailsData"
                border
                stripe
                style="width: 100%"
                v-loading="loading"
              >
                <el-table-column prop="serialNumber" label="序号" width="60" fixed="left" />
                <el-table-column prop="userNumber" label="用户编号" width="120" />
                <el-table-column prop="userName" label="用户姓名" width="120" />
                <el-table-column prop="gasSupplyAddress" label="供气点地址" min-width="200" />
                <el-table-column prop="riskLevel" label="风险级别" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getRiskLevelType(row.riskLevel)" size="small">
                      {{ getRiskLevelText(row.riskLevel) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="riskDetails" label="风险详情" min-width="200" />
                
                <!-- 改前图片 -->
                <el-table-column label="改前" align="center">
                  <el-table-column prop="beforeImage" label="图片" width="120">
                    <template #default="{ row }">
                      <el-image 
                        v-if="row.beforeImage" 
                        :src="row.beforeImage" 
                        style="width: 80px; height: 60px;"
                        fit="cover"
                        :preview-src-list="[row.beforeImage]"
                      />
                      <span v-else>-</span>
                    </template>
                  </el-table-column>
                </el-table-column>
                
                <!-- 改后图片 -->
                <el-table-column label="改后" align="center">
                  <el-table-column prop="afterImage" label="图片" width="120">
                    <template #default="{ row }">
                      <el-image 
                        v-if="row.afterImage" 
                        :src="row.afterImage" 
                        style="width: 80px; height: 60px;"
                        fit="cover"
                        :preview-src-list="[row.afterImage]"
                      />
                      <span v-else>-</span>
                    </template>
                  </el-table-column>
                </el-table-column>
                
                <!-- 维修状态 -->
                <el-table-column label="维修" align="center">
                  <el-table-column prop="maintenanceStatus" label="状态" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getMaintenanceStatusType(row.maintenanceStatus)" size="small">
                        {{ getMaintenanceStatusText(row.maintenanceStatus) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                </el-table-column>
                
                <el-table-column prop="safetyCheckDate" label="安检日期" width="120" />
                
                <!-- 波纹管 -->
                <el-table-column label="波纹管" align="center">
                  <el-table-column prop="corrugatedPipe80" label="80" width="80">
                    <template #default="{ row }">
                      {{ row.corrugatedPipe80 }}c□
                    </template>
                  </el-table-column>
                  <el-table-column prop="corrugatedPipe15" label="15" width="80">
                    <template #default="{ row }">
                      {{ row.corrugatedPipe15 }}c□
                    </template>
                  </el-table-column>
                  <el-table-column prop="corrugatedPipe20" label="20" width="80">
                    <template #default="{ row }">
                      {{ row.corrugatedPipe20 }}c□
                    </template>
                  </el-table-column>
                </el-table-column>
                
                <el-table-column prop="meterFrontValve" label="表前阀" width="100" />
                <el-table-column prop="pressureRegulator" label="调压器" width="100" />
                <el-table-column prop="safetyInspector" label="安检员" width="100" />
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="换表明细" name="meter-change">
            <div class="tab-content">
              <el-table
                :data="meterChangeData"
                border
                stripe
                style="width: 100%"
                v-loading="loading"
              >
                <el-table-column prop="serialNumber" label="序号" width="60" fixed="left" />
                <el-table-column prop="changeDate" label="换表日期" width="120" />
                <el-table-column prop="userNumber" label="用户编号" width="120" />
                <el-table-column prop="userName" label="用户姓名" width="120" />
                <el-table-column prop="phone" label="电话" width="130" />
                <el-table-column prop="gasSupplyAddress" label="供气点地址" min-width="200" />
                <el-table-column prop="beforeMeterImage" label="换表前图片" width="120">
                  <template #default="{ row }">
                    <el-image 
                      v-if="row.beforeMeterImage" 
                      :src="row.beforeMeterImage" 
                      style="width: 80px; height: 60px;"
                      fit="cover"
                      :preview-src-list="[row.beforeMeterImage]"
                    />
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column prop="afterMeterImage" label="换表后图片" width="120">
                  <template #default="{ row }">
                    <el-image 
                      v-if="row.afterMeterImage" 
                      :src="row.afterMeterImage" 
                      style="width: 80px; height: 60px;"
                      fit="cover"
                      :preview-src-list="[row.afterMeterImage]"
                    />
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column prop="meterType" label="表型" width="100" />
                <el-table-column prop="meterChanger" label="换表员" width="100" />
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="抽查" name="spot-check">
            <div class="tab-content">
              <el-table :data="spotCheckData" border stripe style="width: 100%" :cell-style="spotCheckCellStyle">
                <el-table-column prop="serial" label="序号" width="60" />
                <el-table-column prop="community" label="小区" width="120" />
                <el-table-column prop="address" label="地址" min-width="180" />
                <el-table-column prop="inspector" label="安检员" width="100" />
                <el-table-column prop="penaltyItem" label="扣分项" min-width="120" />
                <el-table-column prop="penalty" label="扣分" width="60" />
                <el-table-column prop="remark" label="备注" min-width="120" />
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="别墅正常入户明细" name="villa-entry">
            <div class="tab-content">
              <el-table
                :data="villaEntryData"
                border
                stripe
                style="width: 100%"
                v-loading="loading"
              >
                <el-table-column prop="serialNumber" label="序号" width="60" fixed="left" />
                <el-table-column prop="name" label="名称" width="120" />
                <el-table-column prop="number" label="编号" width="120" />
                <el-table-column prop="userName" label="姓名" width="120" />
                <el-table-column prop="phone" label="手机" width="130" />
                <el-table-column prop="address" label="地址" min-width="200" />
                <el-table-column prop="safetyInspector" label="安检员" width="100" />
                <el-table-column prop="safetyCheckTime" label="安检时间" width="150" />
                <el-table-column prop="entryStatus" label="入户情况" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getEntryStatusType(row.entryStatus)" size="small">
                      {{ getEntryStatusText(row.entryStatus) }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="洋房正常入户明细" name="apartment-entry">
            <div class="tab-content">
              <el-table
                :data="apartmentEntryData"
                border
                stripe
                style="width: 100%"
                v-loading="loading"
              >
                <el-table-column prop="serialNumber" label="序号" width="60" fixed="left" />
                <el-table-column prop="name" label="名称" width="120" />
                <el-table-column prop="number" label="编号" width="120" />
                <el-table-column prop="userName" label="姓名" width="120" />
                <el-table-column prop="phone" label="手机" width="130" />
                <el-table-column prop="address" label="地址" min-width="200" />
                <el-table-column prop="safetyInspector" label="安检员" width="100" />
                <el-table-column prop="safetyCheckTime" label="安检时间" width="150" />
                <el-table-column prop="entryStatus" label="入户情况" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getEntryStatusType(row.entryStatus)" size="small">
                      {{ getEntryStatusText(row.entryStatus) }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="维修单详情"
      width="80%"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentDetail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="派单时间">{{ formatDate(currentDetail.dispatchTime) }}</el-descriptions-item>
          <el-descriptions-item label="维修时间">{{ formatDate(currentDetail.repairTime) }}</el-descriptions-item>
          <el-descriptions-item label="客户编号">{{ currentDetail.customerNumber }}</el-descriptions-item>
          <el-descriptions-item label="用户姓名">{{ currentDetail.userName }}</el-descriptions-item>
          <el-descriptions-item label="电话">{{ currentDetail.phone }}</el-descriptions-item>
          <el-descriptions-item label="小区">{{ currentDetail.community }}</el-descriptions-item>
          <el-descriptions-item label="地址">{{ currentDetail.address }}</el-descriptions-item>
          <el-descriptions-item label="维修员">{{ currentDetail.repairman }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentDetail.status)" size="small">
              {{ getStatusText(currentDetail.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否归档">
            <el-tag :type="currentDetail.isArchived ? 'info' : 'success'" size="small">
              {{ currentDetail.isArchived ? '已归档' : '未归档' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="正常入户">
            <el-tag :type="getEntryStatusType(currentDetail.entryStatus)" size="small">
              {{ getEntryStatusText(currentDetail.entryStatus) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <div style="margin-top: 20px;">
          <h4>维修原因</h4>
          <p>{{ currentDetail.repairReason }}</p>
        </div>

        <div style="margin-top: 20px;">
          <h4>处理结果</h4>
          <p>{{ currentDetail.repairResult }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'

const router = useRouter()

// 响应式数据
const selectedMonth = ref('')
const activeTab = ref('pending')
const loading = ref(false)
const detailDialogVisible = ref(false)
const currentDetail = ref(null)

// 汇总数据
const summary = reactive({
  totalOrders: 0,
  completedOrders: 0,
  pendingOrders: 0,
  totalAmount: 0
})

// 各模块数据
const pendingOrders = ref([])
const safetyCheckData = ref([])
const riskDetailsData = ref([])
const meterChangeData = ref([])
const villaEntryData = ref([])
const apartmentEntryData = ref([])

// 封面数据
const coverData = reactive({
  companyName: '', // 新增字段，默认空
  projectName: '',
  ceaCode: '',
  submittedLaborCost: 0,
  secondReviewCost: 0,
  settlementNumber: '',
  amountPayableChinese: '',
  amountPayable: 0,
  preparedBy: '',
  responsibleVicePresident: '',
  constructionUnit: '',
  clientUnit: '',
  date: '',
  totalCost: 0,
  laborSettlement: 0,
  materialSettlement: 0,
  reviewedBy: '',
  financialDirector: '',
  generalManager: ''
})

// 抽查数据
const spotCheckData = [
  { serial: 1, community: '阳光花园', address: 'A栋1单元101室', inspector: '张三', penaltyItem: '未按时到场', penalty: 2, remark: '迟到5分钟' },
  { serial: 2, community: '翠湖小区', address: 'B栋2单元205室', inspector: '李四', penaltyItem: '未穿工服', penalty: 1, remark: '未着装' },
]

// 公司名称选项
const companyNameOptions = [
  '清远港华燃气有限公司'
]

// 施工单位
const constructionUnitOptions = [
  '港华到家(广东)工程建设有限公司华南分公司'
]
const clientUnitOptions = [
  '清远港华燃气有限公司'
]

// 获取当前月份
const getCurrentMonth = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  return `${year}-${month}`
}

// 月份变化处理
const handleMonthChange = () => {
  fetchData()
}

// 搜索
const handleSearch = () => {
  fetchData()
}

// 重置
const handleReset = () => {
  selectedMonth.value = getCurrentMonth()
  fetchData()
}

// 导出Excel
const handleExportExcel = () => {
  ElMessage.info('导出Excel功能开发中...')
  // TODO: 实现Excel导出功能
  // 可以根据当前选中的月份和标签页导出对应的数据
}

// 标签页切换
const handleTabClick = (tab: any) => {
  console.log('切换到标签页:', tab.props.name)
}



// 处理维修单
const handleProcess = (row: any) => {
  router.push({
    path: '/loose-orders/repair-order-entry',
    query: { id: row.id }
  })
}

// 查看详情
const handleView = (row: any) => {
  currentDetail.value = row
  detailDialogVisible.value = true
}

// 关闭详情对话框
const handleCloseDetail = () => {
  detailDialogVisible.value = false
  currentDetail.value = null
}

// 获取数据
const fetchData = async () => {
  if (!selectedMonth.value) {
    selectedMonth.value = getCurrentMonth()
  }

  loading.value = true
  try {
    const response = await request.get('/api/repair/settlement', {
      params: { month: selectedMonth.value }
    })
    
    if (response.code === 200) {
      const data = response.data
      // 汇总数据
      summary.totalOrders = data.summary.totalOrders
      summary.completedOrders = data.summary.completedOrders
      summary.pendingOrders = data.summary.pendingOrders
      summary.totalAmount = data.summary.totalAmount
      
      // 各模块数据
      pendingOrders.value = data.pendingOrders || []
      safetyCheckData.value = data.safetyCheck || []
      riskDetailsData.value = data.riskDetails || []
      meterChangeData.value = data.meterChange || []
      villaEntryData.value = data.villaEntry || []
      apartmentEntryData.value = data.apartmentEntry || []

      // 封面数据
      coverData.companyName = data.coverData.companyName || ''
      coverData.projectName = data.coverData.projectName || ''
      coverData.ceaCode = data.coverData.ceaCode || ''
      coverData.submittedLaborCost = data.coverData.submittedLaborCost || 0
      coverData.secondReviewCost = data.coverData.secondReviewCost || 0
      coverData.settlementNumber = data.coverData.settlementNumber || ''
      coverData.amountPayableChinese = data.coverData.amountPayableChinese || ''
      coverData.amountPayable = data.coverData.amountPayable || 0
      coverData.preparedBy = data.coverData.preparedBy || ''
      coverData.responsibleVicePresident = data.coverData.responsibleVicePresident || ''
      coverData.constructionUnit = data.coverData.constructionUnit || ''
      coverData.clientUnit = data.coverData.clientUnit || ''
      coverData.date = data.coverData.date || ''
      coverData.totalCost = data.coverData.totalCost || 0
      coverData.laborSettlement = data.coverData.laborSettlement || 0
      coverData.materialSettlement = data.coverData.materialSettlement || 0
      coverData.reviewedBy = data.coverData.reviewedBy || ''
      coverData.financialDirector = data.coverData.financialDirector || ''
      coverData.generalManager = data.coverData.generalManager || ''
    } else {
      ElMessage.error(response.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 格式化日期 - 显示北京时间
const formatDate = (date: string | null) => {
  if (!date) return '-'
  
  // 创建日期对象并添加8小时转换为北京时间
  const dateObj = new Date(date)
  dateObj.setHours(dateObj.getHours() + 8)
  
  return dateObj.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

// 获取状态类型
const getStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    'pending': 'warning',
    'processing': 'primary',
    'completed': 'success',
    'cancelled': 'info'
  }
  return typeMap[status] || 'info'
}

// 获取入户状态文本
const getEntryStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'not_checked': '未检修',
    'normal_entry': '正常入户',
    'need_repair': '需检修'
  }
  return statusMap[status] || status
}

// 获取入户状态类型
const getEntryStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    'not_checked': 'warning',
    'normal_entry': 'success',
    'need_repair': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取风险等级文本
const getRiskLevelText = (level: string) => {
  const levelMap: Record<string, string> = {
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险',
    'critical': '极高风险'
  }
  return levelMap[level] || level
}

// 获取风险等级类型
const getRiskLevelType = (level: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    'low': 'success',
    'medium': 'warning',
    'high': 'danger',
    'critical': 'danger'
  }
  return typeMap[level] || 'info'
}

// 获取维修状态文本
const getMaintenanceStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待维修',
    'processing': '维修中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

// 获取维修状态类型
const getMaintenanceStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    'pending': 'warning',
    'processing': 'primary',
    'completed': 'success',
    'cancelled': 'info'
  }
  return typeMap[status] || 'info'
}

// 表头合并方法
const handleSpanMethod = ({ row, column, rowIndex, columnIndex }: any) => {
  // 这里可以根据需要实现复杂的表头合并逻辑
  return [1, 1]
}

function spotCheckCellStyle({ column }) {
  if (column.property === 'remark') {
    return { background: '#ffd6d6', color: '#b30000', fontWeight: 'bold' }
  }
  return {}
}

function handleImportSpotCheckExcel(file: File) {
  // 这里只做提示，后续可扩展为实际导入逻辑
  ElMessage.success(`已选择文件：${file.name}`)
  return false // 阻止自动上传
}

onMounted(() => {
  selectedMonth.value = getCurrentMonth()
  fetchData()
})
</script>

<style scoped>
.repair-settlement {
  padding-bottom: 60px;
}

.settlement-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 0;
  box-shadow: none;
}

.month-selector {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.summary-line {
  display: flex;
  align-items: center;
  gap: 18px;
  margin: 18px 0 18px 20px;
  font-size: 16px;
}
.summary-badge {
  display: inline-block;
  padding: 4px 16px;
  border-radius: 16px;
  font-weight: bold;
  color: #fff;
  font-size: 15px;
  letter-spacing: 1px;
}
.badge-blue {
  background: #409eff;
}
.badge-green {
  background: #67c23a;
}
.badge-orange {
  background: #e6a23c;
}
.badge-purple {
  background: #a279e6;
}

.module-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tab-content {
  padding: 20px 0;
}

.table-header {
  margin-bottom: 20px;
}

.table-header h3 {
  margin: 0 0 5px 0;
  color: #303133;
}

.table-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.readonly-content {
  text-align: center;
  padding: 40px 20px;
}

.readonly-content h3 {
  margin-bottom: 10px;
  color: #303133;
}

.readonly-content p {
  margin-bottom: 20px;
  color: #909399;
}

:deep(.el-table__row) {
  height: 50px;
}

/* 三级表头样式 */
:deep(.el-table__header) {
  background-color: #f5f7fa;
}

:deep(.el-table__header th) {
  background-color: #f5f7fa !important;
  color: #303133;
  font-weight: bold;
  text-align: center;
  border: 1px solid #dcdfe6;
}

:deep(.el-table__header th.is-leaf) {
  background-color: #e6a23c !important;
  color: white;
  font-weight: bold;
}

:deep(.el-table__header th:not(.is-leaf)) {
  background-color: #409eff !important;
  color: white;
  font-weight: bold;
}

:deep(.el-table__header th.el-table__cell) {
  background-color: #67c23a !important;
  color: white;
  font-weight: bold;
}

/* 封面页面样式 */
.settlement-cover {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px;
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.cover-header {
  text-align: center;
  margin-bottom: 40px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 20px;
}

.company-name {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin: 0 0 10px 0;
}

.document-title {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
  margin: 0;
  text-decoration: underline;
  text-decoration-color: #409eff;
  text-decoration-thickness: 2px;
}

.cover-content {
  display: flex;
  gap: 60px;
}

.left-column,
.right-column {
  flex: 1;
}

.form-row {
  display: flex;
  margin-bottom: 15px;
  align-items: center;
  min-height: 30px;
}

.form-row .label {
  font-weight: bold;
  color: #303133;
  min-width: 120px;
  margin-right: 10px;
}

.form-row .value {
  color: #606266;
  flex: 1;
}

.form-row .currency-symbol {
  color: #409eff;
  font-weight: bold;
  margin-left: 10px;
}

/* 特殊样式 */
.form-row:nth-child(6) .value {
  font-weight: bold;
  color: #409eff;
  font-size: 16px;
}

.form-row:nth-child(6) .currency-symbol {
  font-size: 16px;
  color: #409eff;
  font-weight: bold;
}

/* 公司名称输入框样式 */
.company-name-input {
  font-size: 18px;
  font-weight: normal;
  text-align: center;
  margin: 0 0 10px 0;
  width: 100%;
}

/* 样式部分 */
.summary-table-wrapper {
  max-width: 900px;
  margin: 0 auto;
  background: #fff;
  padding: 32px 24px 24px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.06);
}
.summary-title {
  text-align: center;
  font-size: 26px;
  font-weight: bold;
  letter-spacing: 12px;
  margin-bottom: 18px;
  text-decoration: underline;
  text-underline-offset: 4px;
}
.summary-meta {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 16px;
  margin-bottom: 10px;
}
.summary-label {
  font-weight: bold;
}
.summary-value {
  margin-right: 32px;
  font-weight: bold;
}
.summary-page {
  margin-left: auto;
  font-size: 15px;
}
.summary-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  font-size: 16px;
}
.summary-table th, .summary-table td {
  border: 1px solid #333;
  padding: 8px 6px;
  text-align: center;
}
.summary-table .text-right {
  text-align: right;
  padding-right: 16px;
}

/* 抽查表格样式 */
.spot-check-table-wrapper {
  max-width: 1000px;
  margin: 0 auto;
  background: #fff;
  padding: 32px 24px 24px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.06);
}
.spot-check-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 16px;
}
.spot-check-table th, .spot-check-table td {
  border: 1px solid #333;
  padding: 8px 6px;
  text-align: center;
}
.spot-check-table .remark-col {
  background: #ffd6d6;
  color: #b30000;
  font-weight: bold;
}
</style> 