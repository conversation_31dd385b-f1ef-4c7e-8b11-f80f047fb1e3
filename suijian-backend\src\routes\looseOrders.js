const express = require('express');
const router = express.Router();
const looseOrdersController = require('../controllers/looseOrdersController');
const { authMiddleware } = require('../middleware/auth');
const { uploadSingle, handleUploadError } = require('../middleware/upload');

// 获取散户订单列表
router.get('/', authMiddleware, looseOrdersController.getLooseOrders);

// 获取散户订单详情
router.get('/:id', authMiddleware, looseOrdersController.getLooseOrderById);

// 创建散户订单
router.post('/', authMiddleware, looseOrdersController.createLooseOrder);

// 更新散户订单
router.put('/:id', authMiddleware, looseOrdersController.updateLooseOrder);

// 删除散户订单
router.delete('/:id', authMiddleware, looseOrdersController.deleteLooseOrder);

// 导入Excel文件
router.post('/import', authMiddleware, uploadSingle('file'), handleUploadError, looseOrdersController.importExcel);

// 订单派发
router.post('/:id/assign', authMiddleware, looseOrdersController.assignOrder);

module.exports = router;