<template>
  <div class="safety-inspection-entry">
    <el-card class="form-card">
      <!-- 返回按钮和状态指示 -->
      <div class="header-section">
        <div class="back-button">
          <el-button @click="goBack" icon="ArrowLeft">
            返回列表
          </el-button>
        </div>
        <div class="status-indicator" v-if="hasDraft">
          <el-tag type="warning" size="small">
            <el-icon><Document /></el-icon>
            有草稿
          </el-tag>
        </div>
      </div>
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="safety-inspection-form"
      >
        <!-- 基本信息 -->
        <el-divider content-position="left">基本信息</el-divider>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="时间">{{ formData.inspectionTime }}</el-descriptions-item>
          <el-descriptions-item label="客户编号">{{ formData.customerNumber }}</el-descriptions-item>
          <el-descriptions-item label="电话">{{ formData.phone }}</el-descriptions-item>
          <el-descriptions-item label="小区">{{ formData.community }}</el-descriptions-item>
          <el-descriptions-item label="地址">{{ formData.address }}</el-descriptions-item>
          <el-descriptions-item label="安检员">{{ formData.inspector }}</el-descriptions-item>
        </el-descriptions>
        
        <!-- 操作类型选择 -->
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="24">
            <el-form-item label="操作类型">
              <el-checkbox v-model="formData.isMeterChange" @change="handleMeterChangeChange">
                是否换表
              </el-checkbox>
              <el-checkbox v-model="formData.isInspection" @change="handleInspectionChange" style="margin-left: 20px;">
                是否安检
              </el-checkbox>
              <el-checkbox v-model="formData.isNormalEntry" @change="handleNormalEntryChange" style="margin-left: 20px;">
                正常入户
              </el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 换表信息 -->
        <el-divider content-position="left" v-if="formData.isMeterChange">换表信息</el-divider>
        
        <el-row :gutter="20" v-if="formData.isMeterChange">
          <el-col :span="12">
            <el-form-item label="旧表号" prop="oldMeterNumber">
              <el-input
                v-model="formData.oldMeterNumber"
                placeholder="请输入旧表号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="新表号" prop="newMeterNumber">
              <el-input
                v-model="formData.newMeterNumber"
                placeholder="请输入新表号"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="formData.isMeterChange">
          <el-col :span="12">
            <el-form-item label="换表前图片">
              <el-upload
                ref="beforeMeterUploadRef"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :before-upload="beforeUpload"
                :on-success="handleBeforeMeterUploadSuccess"
                :on-error="handleUploadError"
                :file-list="beforeMeterFileList"
                :limit="1"
                accept="image/*"
                list-type="picture-card"
                :auto-upload="false"
              >
                <el-icon><Plus /></el-icon>
                <template #tip>
                  <div class="el-upload__tip">
                    支持jpg/png格式，文件大小不超过10MB
                  </div>
                </template>
              </el-upload>
              
              <div class="upload-actions" v-if="beforeMeterFileList.length > 0">
                <el-button @click="clearBeforeMeterUpload">清空</el-button>
              </div>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="换表后图片">
              <el-upload
                ref="afterMeterUploadRef"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :before-upload="beforeUpload"
                :on-success="handleAfterMeterUploadSuccess"
                :on-error="handleUploadError"
                :file-list="afterMeterFileList"
                :limit="1"
                accept="image/*"
                list-type="picture-card"
                :auto-upload="false"
              >
                <el-icon><Plus /></el-icon>
                <template #tip>
                  <div class="el-upload__tip">
                    支持jpg/png格式，文件大小不超过10MB
                  </div>
                </template>
              </el-upload>
              
              <div class="upload-actions" v-if="afterMeterFileList.length > 0">
                <el-button @click="clearAfterMeterUpload">清空</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 安检信息 -->
        <el-divider content-position="left" v-if="formData.isInspection">安检信息</el-divider>
        
        <el-form-item label="安检原因" prop="inspectionReason" v-if="formData.isInspection">
          <el-input
            v-model="formData.inspectionReason"
            type="textarea"
            :rows="3"
            placeholder="请输入安检原因"
              />
            </el-form-item>
        
        <el-form-item label="安检结果" prop="inspectionResult" v-if="formData.isInspection">
          <el-input
            v-model="formData.inspectionResult"
            type="textarea"
            :rows="3"
            placeholder="请输入安检结果"
              />
            </el-form-item>

        <!-- 安检图片 -->
        <el-divider content-position="left" v-if="formData.isInspection">安检图片</el-divider>

        <el-row :gutter="20" v-if="formData.isInspection">
          <el-col :span="12">
            <el-form-item label="安检前图片">
              <el-upload
                ref="beforeUploadRef"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :before-upload="beforeUpload"
                :on-success="handleBeforeUploadSuccess"
                :on-error="handleUploadError"
                :file-list="beforeFileList"
                :limit="3"
                accept="image/*"
                list-type="picture-card"
                :auto-upload="false"
              >
                <el-icon><Plus /></el-icon>
                <template #tip>
                  <div class="el-upload__tip">
                    支持jpg/png格式，文件大小不超过10MB，最多上传3张图片
                  </div>
                </template>
              </el-upload>
              
              <div class="upload-actions" v-if="beforeFileList.length > 0">
                <el-button @click="clearBeforeUpload">清空</el-button>
              </div>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="安检后图片">
              <el-upload
                ref="afterUploadRef"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :before-upload="beforeUpload"
                :on-success="handleAfterUploadSuccess"
                :on-error="handleUploadError"
                :file-list="afterFileList"
                :limit="3"
                accept="image/*"
                list-type="picture-card"
                :auto-upload="false"
              >
                <el-icon><Plus /></el-icon>
                <template #tip>
                  <div class="el-upload__tip">
                    支持jpg/png格式，文件大小不超过10MB，最多上传3张图片
                  </div>
                </template>
              </el-upload>
              
              <div class="upload-actions" v-if="afterFileList.length > 0">
                <el-button @click="clearAfterUpload">清空</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 耗材列表 -->
        <el-divider content-position="left">耗材列表</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="materials-list">
              <div class="materials-header">
                <el-button type="primary" @click="addMaterial">
                  <el-icon><Plus /></el-icon>
                  添加耗材
                </el-button>
              </div>
              
              <el-table :data="formData.materials" border style="width: 100%">
                <el-table-column label="耗材名称" min-width="200">
                  <template #default="{ row }">
                    <el-input v-model="row.name" placeholder="请输入耗材名称" />
                  </template>
                </el-table-column>
                <el-table-column label="规格型号" width="150">
                  <template #default="{ row }">
                    <el-input v-model="row.specification" placeholder="规格型号" />
                  </template>
                </el-table-column>
                <el-table-column label="数量" width="120">
                  <template #default="{ row }">
                    <el-input-number
                      v-model="row.quantity"
                      :min="0"
                      :precision="1"
                      controls-position="right"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="单位" width="100">
                  <template #default="{ row }">
                    <el-select v-model="row.unit" placeholder="单位" style="width: 100%">
                      <el-option label="个" value="个" />
                      <el-option label="件" value="件" />
                      <el-option label="米" value="米" />
                      <el-option label="套" value="套" />
                      <el-option label="台" value="台" />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80">
                  <template #default="{ $index }">
                    <el-button
                      type="danger"
                      size="small"
                      @click="removeMaterial($index)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-col>
        </el-row>

        <!-- 操作按钮 -->
        <el-row :gutter="20" style="margin-top: 30px;">
          <el-col :span="24" style="text-align: center;">
            <el-button type="primary" @click="submitForm">保存</el-button>
            <el-button @click="saveDraft">保存草稿</el-button>
            <el-button @click="resetForm">重置</el-button>
            <el-button @click="printForm">打印</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, ArrowLeft, Document } from '@element-plus/icons-vue'
import request from '@/utils/request'

// 上传相关
const beforeFileList = ref([])
const afterFileList = ref([])
const beforeMeterFileList = ref([])
const afterMeterFileList = ref([])
const beforeUploadRef = ref()
const afterUploadRef = ref()
const beforeMeterUploadRef = ref()
const afterMeterUploadRef = ref()
const uploadUrl = '/api/upload' // 上传接口
const uploadHeaders = {
  'Authorization': 'Bearer ' + localStorage.getItem('token') || ''
}

// 草稿状态
const hasDraft = ref(false)

// 表单数据
const formData = reactive({
  // 基本信息
  inspectionTime: '', // 安检时间
  customerNumber: '', // 客户编号
  phone: '', // 电话
  community: '', // 小区
  address: '', // 地址
  inspector: '', // 安检员
  oldMeterNumber: '', // 旧表号
  newMeterNumber: '', // 新表号
  
  // 操作类型
  isMeterChange: true, // 是否换表，默认勾选
  isInspection: true, // 是否安检，默认勾选
  isNormalEntry: false, // 正常入户
  
  // 安检信息
  inspectionReason: '', // 安检原因
  inspectionResult: '', // 安检结果
  
  // 耗材列表
  materials: [] as Array<{
    name: string
    specification: string
    quantity: number
    unit: string
  }>
})

// 表单验证规则
const rules = {
  inspectionTime: [
    { required: true, message: '请选择安检时间', trigger: 'change' }
  ],
  customerNumber: [
    { required: true, message: '请输入客户编号', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  community: [
    { required: true, message: '请输入小区名称', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入详细地址', trigger: 'blur' }
  ],
  inspector: [
    { required: true, message: '请输入安检员姓名', trigger: 'blur' }
  ],
  inspectionReason: [
    { required: true, message: '请输入安检原因', trigger: 'blur' }
  ],
  inspectionResult: [
    { required: true, message: '请输入安检结果', trigger: 'blur' }
  ]
}

const route = useRoute()
const router = useRouter()
const formRef = ref()

// 获取安检单数据
const fetchSafetyInspectionData = async (id: string) => {
  try {
    const response = await request.get(`/api/safety/inspection/orders/${id}`)
    
    if (response.code === 200) {
      const data = response.data
      
      // 填充表单数据
      formData.inspectionTime = data.inspectionTime || ''
      formData.customerNumber = data.customerNumber || ''
      formData.phone = data.phone || ''
      formData.community = data.community || ''
      formData.address = data.address || ''
      formData.inspector = data.inspector || ''
      formData.oldMeterNumber = data.oldMeterNumber || ''
      formData.newMeterNumber = data.newMeterNumber || ''
      formData.inspectionReason = data.inspectionReason || ''
      formData.inspectionResult = data.inspectionResult || ''
      formData.materials = data.materials || []
      
      // 设置操作类型
      formData.isMeterChange = data.isMeterChange !== undefined ? data.isMeterChange : true
      formData.isInspection = data.isInspection !== undefined ? data.isInspection : true
      formData.isNormalEntry = data.isNormalEntry || false
      
      // 处理图片数据
      if (data.beforeImages && data.beforeImages.length > 0) {
        beforeFileList.value = data.beforeImages.map((url: string, index: number) => ({
          name: `安检前图片${index + 1}`,
          url: url
        }))
      }
      
      if (data.afterImages && data.afterImages.length > 0) {
        afterFileList.value = data.afterImages.map((url: string, index: number) => ({
          name: `安检后图片${index + 1}`,
          url: url
        }))
      }
      
      // 处理换表图片数据
      if (data.beforeMeterImage) {
        beforeMeterFileList.value = [{
          name: '换表前图片',
          url: data.beforeMeterImage
        }]
      }
      
      if (data.afterMeterImage) {
        afterMeterFileList.value = [{
          name: '换表后图片',
          url: data.afterMeterImage
        }]
      }
      
      ElMessage.success('数据加载成功')
    } else {
      ElMessage.error(response.message || '获取数据失败')
    }
  } catch (error) {
    ElMessage.error('获取数据失败')
    console.error('获取安检单数据失败:', error)
  }
}

// 返回列表页面
const goBack = () => {
  router.back()
}

// 换表checkbox变化处理
const handleMeterChangeChange = (checked: boolean) => {
  if (!checked) {
    // 如果取消勾选换表，清空换表相关数据
    formData.oldMeterNumber = ''
    formData.newMeterNumber = ''
    clearBeforeMeterUpload()
    clearAfterMeterUpload()
  }
}

// 安检checkbox变化处理
const handleInspectionChange = (checked: boolean) => {
  if (!checked) {
    // 如果取消勾选安检，清空安检相关数据
    formData.inspectionReason = ''
    formData.inspectionResult = ''
    clearBeforeUpload()
    clearAfterUpload()
  }
}

// 正常入户checkbox变化处理
const handleNormalEntryChange = (checked: boolean) => {
  // 正常入户状态变化时的处理逻辑
  console.log('正常入户状态变化:', checked)
}

// 页面加载时获取数据
onMounted(() => {
  const id = route.query.id as string
  if (id) {
    fetchSafetyInspectionData(id)
  } else {
    ElMessage.warning('未找到安检单ID')
    router.push('/loose-orders/safety-inspection-list')
  }
})

// 上传前验证
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过 10MB!')
    return false
  }
  return true
}

// 安检前图片上传成功
const handleBeforeUploadSuccess = (response: any, file: any) => {
  ElMessage.success('安检前图片上传成功')
  console.log('安检前图片上传成功:', response)
}

// 安检后图片上传成功
const handleAfterUploadSuccess = (response: any, file: any) => {
  ElMessage.success('安检后图片上传成功')
  console.log('安检后图片上传成功:', response)
  }

// 换表前图片上传成功
const handleBeforeMeterUploadSuccess = (response: any, file: any) => {
  ElMessage.success('换表前图片上传成功')
  console.log('换表前图片上传成功:', response)
}

// 换表后图片上传成功
const handleAfterMeterUploadSuccess = (response: any, file: any) => {
  ElMessage.success('换表后图片上传成功')
  console.log('换表后图片上传成功:', response)
  }

// 上传失败
const handleUploadError = (error: any) => {
  ElMessage.error('图片上传失败')
  console.error('上传失败:', error)
}

// 清空安检前图片
const clearBeforeUpload = () => {
  beforeFileList.value = []
  if (beforeUploadRef.value) {
    beforeUploadRef.value.clearFiles()
  }
}

// 清空安检后图片
const clearAfterUpload = () => {
  afterFileList.value = []
  if (afterUploadRef.value) {
    afterUploadRef.value.clearFiles()
  }
}

// 清空换表前图片
const clearBeforeMeterUpload = () => {
  beforeMeterFileList.value = []
  if (beforeMeterUploadRef.value) {
    beforeMeterUploadRef.value.clearFiles()
  }
}

// 清空换表后图片
const clearAfterMeterUpload = () => {
  afterMeterFileList.value = []
  if (afterMeterUploadRef.value) {
    afterMeterUploadRef.value.clearFiles()
  }
}

// 添加耗材
const addMaterial = () => {
  formData.materials.push({
    name: '',
    specification: '',
    quantity: 0,
    unit: ''
  })
}

// 删除耗材
const removeMaterial = (index: number) => {
  formData.materials.splice(index, 1)
}

// 保存草稿
const saveDraft = async () => {
  const id = route.query.id as string
  if (!id) {
    ElMessage.error('未找到安检单ID')
    return
  }
  
  try {
    // 准备草稿数据（不进行表单验证）
    const draftData = {
      ...formData,
      beforeImages: beforeFileList.value.map((file: any) => file.url || file.response?.url),
      afterImages: afterFileList.value.map((file: any) => file.url || file.response?.url),
      beforeMeterImage: beforeMeterFileList.value.length > 0 ? (beforeMeterFileList.value[0].url || beforeMeterFileList.value[0].response?.url) : null,
      afterMeterImage: afterMeterFileList.value.length > 0 ? (afterMeterFileList.value[0].url || afterMeterFileList.value[0].response?.url) : null,
      isDraft: true
    }
    
    // 调用API保存草稿
    const response = await request.post(`/api/safety/inspection/orders/${id}/draft`, draftData)
    
    if (response.code === 200) {
      ElMessage.success('草稿保存成功')
      hasDraft.value = true
    } else {
      ElMessage.error(response.message || '草稿保存失败')
    }
  } catch (error) {
    ElMessage.error('草稿保存失败')
    console.error('草稿保存失败:', error)
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    const id = route.query.id as string
    if (!id) {
      ElMessage.error('未找到安检单ID')
      return
    }
    
    // 准备提交的数据
    const submitData = {
      ...formData,
      beforeImages: beforeFileList.value.map((file: any) => file.url || file.response?.url),
      afterImages: afterFileList.value.map((file: any) => file.url || file.response?.url),
      beforeMeterImage: beforeMeterFileList.value.length > 0 ? (beforeMeterFileList.value[0].url || beforeMeterFileList.value[0].response?.url) : null,
      afterMeterImage: afterMeterFileList.value.length > 0 ? (afterMeterFileList.value[0].url || afterMeterFileList.value[0].response?.url) : null
    }
    
    // 调用API更新数据
    const response = await request.put(`/api/safety/inspection/orders/${id}`, submitData)
    
    if (response.code === 200) {
      ElMessage.success('保存成功')
      // 可以选择跳转回列表页面
      // router.push('/loose-orders/safety-inspection-list')
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败')
    console.error('表单提交失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  if (!formRef.value) return
  
  ElMessageBox.confirm('确定要重置表单吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    formRef.value.resetFields()
    formData.materials = []
    // 重置操作类型为默认值
    formData.isMeterChange = true
    formData.isInspection = true
    formData.isNormalEntry = false
  clearBeforeUpload()
  clearAfterUpload()
  clearBeforeMeterUpload()
  clearAfterMeterUpload()
    ElMessage.success('表单已重置')
  })
}

// 打印表单
const printForm = () => {
  // TODO: 实现打印功能
  ElMessage.info('打印功能开发中')
  }
</script>

<style scoped>
.safety-inspection-entry {
  padding: 0;
  height: 100%;
  width: 100%;
  overflow: auto;
  background: #f5f7fa;
}

.form-card {
  width: 100%;
  min-height: 100%;
  border: none;
  box-shadow: none;
  background: #fff;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.back-button {
  margin-bottom: 0;
}

.status-indicator {
  display: flex;
  align-items: center;
}

.safety-inspection-form {
  margin-top: 20px;
}

.materials-list {
  margin: 20px 0;
}

.materials-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 15px;
}

.upload-actions {
  margin-top: 10px;
}

:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px;
}
</style> 