version: '3.8'

services:
  backend:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - JWT_SECRET=your_jwt_secret_key_production
      - CORS_ORIGIN=http://localhost:8080
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 如果需要Redis缓存
  # redis:
  #   image: redis:7-alpine
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   restart: unless-stopped

  # 如果需要MySQL数据库
  # mysql:
  #   image: mysql:8.0
  #   ports:
  #     - "3306:3306"
  #   environment:
  #     MYSQL_ROOT_PASSWORD: rootpassword
  #     MYSQL_DATABASE: engineering_management
  #     MYSQL_USER: appuser
  #     MYSQL_PASSWORD: apppassword
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #   restart: unless-stopped

volumes:
  # redis_data:
  # mysql_data: 