-- 创建小区表
-- 用于存储小区信息，辅助地址解析

-- 小区表 - 存储小区信息，用于地址解析
CREATE TABLE IF NOT EXISTS communities (
    id VARCHAR(36) PRIMARY KEY,                    -- 小区ID，UUID格式
    name VARCHAR(100) NOT NULL,                    -- 小区名称
    alias VARCHAR(200),                            -- 小区别名（多个别名用逗号分隔）
    district VARCHAR(50),                          -- 所属区域
    city VARCHAR(50),                              -- 所属城市
    province VARCHAR(50),                          -- 所属省份
    address TEXT,                                  -- 小区详细地址
    status INTEGER DEFAULT 1,                      -- 小区状态：1-启用，0-禁用
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

-- 小区表索引
CREATE INDEX IF NOT EXISTS idx_communities_name ON communities(name);           -- 小区名称索引
CREATE INDEX IF NOT EXISTS idx_communities_alias ON communities(alias(100));   -- 小区别名索引
CREATE INDEX IF NOT EXISTS idx_communities_district ON communities(district);   -- 区域索引
CREATE INDEX IF NOT EXISTS idx_communities_status ON communities(status);       -- 状态索引

-- 小区表更新时间触发器
CREATE TRIGGER IF NOT EXISTS update_communities_updated_at 
    AFTER UPDATE ON communities
    FOR EACH ROW
    BEGIN
        UPDATE communities SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- 插入一些常见的小区数据作为示例
INSERT OR IGNORE INTO communities (id, name, alias, district, city, province, address, status) VALUES 
('community-001', '阳光花园', '阳光花园,阳光小区,阳光花苑', '清城区', '清远市', '广东省', '广东省清远市清城区阳光花园', 1),
('community-002', '碧桂园', '碧桂园,碧桂园小区,碧桂园花园', '清城区', '清远市', '广东省', '广东省清远市清城区碧桂园', 1),
('community-003', '恒大城', '恒大城,恒大小区,恒大花园', '清新区', '清远市', '广东省', '广东省清远市清新区恒大城', 1),
('community-004', '保利城', '保利城,保利小区,保利花园', '清城区', '清远市', '广东省', '广东省清远市清城区保利城', 1),
('community-005', '万科城', '万科城,万科小区,万科花园', '清新区', '清远市', '广东省', '广东省清远市清新区万科城', 1);

-- 为loose_orders表添加地址相关索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_loose_orders_community_name ON loose_orders(community_name);
CREATE INDEX IF NOT EXISTS idx_loose_orders_building ON loose_orders(building);
CREATE INDEX IF NOT EXISTS idx_loose_orders_room_number ON loose_orders(room_number); 