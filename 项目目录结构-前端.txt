工程管理系统 - 前端项目目录结构
==========================================

suijian-frontend/
├── public/                          # 静态资源目录
│   ├── favicon.ico                  # 网站图标
│   ├── index.html                   # HTML模板
│   └── robots.txt                   # 搜索引擎配置
│
├── src/                             # 源代码目录
│   ├── api/                         # API接口目录
│   │   ├── auth.ts                  # 认证相关接口
│   │   ├── dashboard.ts             # 首页相关接口
│   │   ├── warehouse.ts             # 仓库管理接口
│   │   ├── looseOrders.ts           # 散户订单接口
│   │   ├── projects.ts              # 工程订单接口
│   │   ├── employees.ts             # 员工管理接口
│   │   ├── system.ts                # 系统设置接口
│   │   └── types/                   # API类型定义
│   │       ├── auth.ts              # 认证类型
│   │       ├── dashboard.ts         # 首页类型
│   │       ├── warehouse.ts         # 仓库管理类型
│   │       ├── looseOrders.ts       # 散户订单类型
│   │       ├── projects.ts          # 工程订单类型
│   │       ├── employees.ts         # 员工管理类型
│   │       └── system.ts            # 系统设置类型
│   │
│   ├── assets/                      # 资源文件目录
│   │   ├── images/                  # 图片资源
│   │   │   ├── logo.png             # 系统Logo
│   │   │   ├── avatar.png           # 默认头像
│   │   │   └── icons/               # 图标文件
│   │   ├── styles/                  # 样式文件
│   │   │   ├── variables.scss       # 全局变量
│   │   │   ├── mixins.scss          # 混入样式
│   │   │   ├── common.scss          # 公共样式
│   │   │   └── element-plus.scss    # Element Plus样式覆盖
│   │   └── fonts/                   # 字体文件
│   │
│   ├── components/                  # 公共组件目录
│   │   ├── common/                  # 通用组件
│   │   │   ├── AppHeader.vue        # 应用头部
│   │   │   ├── AppSidebar.vue       # 应用侧边栏
│   │   │   ├── AppFooter.vue        # 应用底部
│   │   │   ├── Breadcrumb.vue       # 面包屑导航
│   │   │   ├── Loading.vue          # 加载组件
│   │   │   ├── Empty.vue            # 空状态组件
│   │   │   └── Confirm.vue          # 确认对话框
│   │   ├── form/                    # 表单组件
│   │   │   ├── SearchForm.vue       # 搜索表单
│   │   │   ├── MaterialForm.vue     # 物料表单
│   │   │   ├── OrderForm.vue        # 订单表单
│   │   │   ├── ProjectForm.vue      # 工程表单
│   │   │   └── EmployeeForm.vue     # 员工表单
│   │   ├── table/                   # 表格组件
│   │   │   ├── DataTable.vue        # 数据表格
│   │   │   ├── MaterialTable.vue    # 物料表格
│   │   │   ├── OrderTable.vue       # 订单表格
│   │   │   └── ProjectTable.vue     # 工程表格
│   │   └── chart/                   # 图表组件
│   │       ├── PieChart.vue         # 饼图组件
│   │       ├── LineChart.vue        # 折线图组件
│   │       ├── BarChart.vue         # 柱状图组件
│   │       └── Dashboard.vue        # 仪表盘组件
│   │
│   ├── views/                       # 页面组件目录
│   │   ├── dashboard/               # 首页模块
│   │   │   ├── index.vue            # 首页主页面
│   │   │   ├── MaterialStats.vue    # 工程物料统计
│   │   │   ├── ProjectStats.vue     # 工程进度统计
│   │   │   ├── LooseMaterialStats.vue # 散单物料统计

│   │   │
│   │   ├── warehouse/               # 仓库管理模块
│   │   │   ├── index.vue            # 仓库管理首页
│   │   │   ├── MaterialList.vue     # 物料列表
│   │   │   ├── MaterialApply.vue    # 领料申请
│   │   │   ├── MaterialInbound.vue  # 甲料入库
│   │   │   ├── MaterialReturn.vue   # 物料退仓
│   │   │   ├── AuxiliaryPurchase.vue # 辅料采购
│   │   │   ├── ProductInbound.vue   # 商品入库
│   │   │   ├── MaterialRecords.vue  # 进出记录
│   │   │   ├── MaterialPrice.vue    # 物料价格
│   │   │   ├── MaterialBase.vue     # 物料基础库
│   │   │   └── StockWarning.vue     # 库存预警
│   │   │
│   │   ├── looseOrders/             # 散户订单模块
│   │   │   ├── index.vue            # 散户订单首页
│   │   │   ├── OrderList.vue        # 订单列表
│   │   │   ├── OrderAssign.vue      # 甲方派单
│   │   │   ├── OrderExecute.vue     # 订单执行
│   │   │   ├── MonthlyBalance.vue   # 月度平账
│   │   │   └── BalanceRecords.vue   # 平账记录
│   │   │
│   │   ├── projects/                # 工程订单模块
│   │   │   ├── index.vue            # 工程订单首页
│   │   │   ├── ProjectList.vue      # 工程订单列表
│   │   │   ├── ProjectAssign.vue    # 甲方派单
│   │   │   ├── WorkTypeSetting.vue  # 工种设置
│   │   │   ├── ProjectStart.vue     # 工程开始
│   │   │   ├── ProjectProgress.vue  # 工程推进
│   │   │   ├── ProjectPause.vue     # 工程暂停
│   │   │   ├── ProjectFinish.vue    # 工程完成
│   │   │   └── ExternalCost.vue     # 外部成本
│   │   │
│   │   ├── employees/               # 员工管理模块
│   │   │   ├── index.vue            # 员工管理首页
│   │   │   ├── EmployeeList.vue     # 员工列表
│   │   │   ├── WorkTypeSetting.vue  # 工种设置
│   │   │   └── PerformanceSetting.vue # 绩效设置
│   │   │
│   │   └── system/                  # 系统设置模块
│   │       ├── index.vue            # 系统设置首页
│   │       ├── UserManagement.vue   # 用户管理
│   │       ├── AddUser.vue          # 增加用户
│   │       ├── PermissionManagement.vue # 权限管理
│   │       ├── SystemLog.vue        # 系统日志
│   │       └── DataImport.vue       # 基础数据导入
│   │
│   ├── router/                      # 路由配置目录
│   │   ├── index.ts                 # 路由主文件
│   │   ├── routes.ts                # 路由配置
│   │   └── guards.ts                # 路由守卫
│   │
│   ├── stores/                      # Pinia状态管理目录
│   │   ├── index.ts                 # 状态管理入口
│   │   ├── auth.ts                  # 认证状态
│   │   ├── user.ts                  # 用户状态
│   │   ├── warehouse.ts             # 仓库管理状态
│   │   ├── looseOrders.ts           # 散户订单状态
│   │   ├── projects.ts              # 工程订单状态
│   │   ├── employees.ts             # 员工管理状态
│   │   └── system.ts                # 系统设置状态
│   │
│   ├── utils/                       # 工具函数目录
│   │   ├── request.ts               # HTTP请求封装
│   │   ├── auth.ts                  # 认证工具
│   │   ├── storage.ts               # 本地存储工具
│   │   ├── validate.ts              # 验证工具
│   │   ├── format.ts                # 格式化工具
│   │   ├── constants.ts             # 常量定义
│   │   └── mock/                    # 模拟数据目录
│   │       ├── index.ts             # 模拟数据入口
│   │       ├── auth.ts              # 认证模拟数据
│   │       ├── dashboard.ts         # 首页模拟数据
│   │       ├── warehouse.ts         # 仓库管理模拟数据
│   │       ├── looseOrders.ts       # 散户订单模拟数据
│   │       ├── projects.ts          # 工程订单模拟数据
│   │       ├── employees.ts         # 员工管理模拟数据
│   │       └── system.ts            # 系统设置模拟数据
│   │
│   ├── hooks/                       # 组合式函数目录
│   │   ├── useAuth.ts               # 认证相关Hook
│   │   ├── useTable.ts              # 表格相关Hook
│   │   ├── useForm.ts               # 表单相关Hook
│   │   ├── usePagination.ts         # 分页相关Hook
│   │   └── useSearch.ts             # 搜索相关Hook
│   │
│   ├── directives/                  # 自定义指令目录
│   │   ├── permission.ts            # 权限指令
│   │   ├── loading.ts               # 加载指令
│   │   └── debounce.ts              # 防抖指令
│   │
│   ├── plugins/                     # 插件目录
│   │   ├── element-plus.ts          # Element Plus插件
│   │   ├── axios.ts                 # Axios插件
│   │   └── mock.ts                  # Mock插件
│   │
│   ├── App.vue                      # 根组件
│   ├── main.ts                      # 应用入口
│   └── env.d.ts                     # 环境变量类型声明
│
├── mock/                            # Mock数据目录
│   ├── auth.json                    # 认证相关数据
│   ├── dashboard.json               # 首页统计数据
│   ├── materials.json               # 物料数据
│   ├── orders.json                  # 订单数据
│   ├── projects.json                # 工程数据
│   ├── employees.json               # 员工数据
│   ├── users.json                   # 用户数据
│   └── system.json                  # 系统数据
│
├── .env.development                 # 开发环境配置
├── .env.production                  # 生产环境配置
├── .env.test                        # 测试环境配置
├── .eslintrc.js                     # ESLint配置
├── .prettierrc.js                   # Prettier配置
├── tsconfig.json                    # TypeScript配置
├── vite.config.ts                   # Vite配置
├── package.json                     # 项目依赖配置
├── README.md                        # 项目说明文档
└── index.html                       # HTML入口文件

-- 主要配置文件内容示例 --

package.json:
{
  "name": "suijian-frontend",
  "version": "1.0.0",
  "description": "工程管理系统前端",
  "scripts": {
    "dev": "vite --mode development",
    "build": "vue-tsc && vite build --mode production",
    "preview": "vite preview",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore",
    "format": "prettier --write src/"
  },
  "dependencies": {
    "vue": "^3.3.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0",
    "element-plus": "^2.3.0",
    "axios": "^1.4.0",
    "dayjs": "^1.11.0",
    "lodash-es": "^4.17.21"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "@types/lodash-es": "^4.17.9",
    "@vitejs/plugin-vue": "^4.2.0",
    "@vue/tsconfig": "^0.4.0",
    "eslint": "^8.45.0",
    "eslint-plugin-vue": "^9.15.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "prettier": "^3.0.0",
    "sass": "^1.64.0",
    "typescript": "^5.1.0",
    "vite": "^4.4.0",
    "vue-tsc": "^1.8.0"
  }
}

vite.config.ts:
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          elementPlus: ['element-plus'],
          utils: ['axios', 'dayjs', 'lodash-es']
        }
      }
    }
  }
})

tsconfig.json:
{
  "extends": "@vue/tsconfig/tsconfig.web.json",
  "include": ["env.d.ts", "src/**/*", "src/**/*.vue"],
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },
    "types": ["element-plus/global"]
  },
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ]
}

.eslintrc.js:
module.exports = {
  root: true,
  env: {
    node: true
  },
  extends: [
    'plugin:vue/vue3-essential',
    'eslint:recommended',
    '@vue/typescript/recommended'
  ],
  parserOptions: {
    ecmaVersion: 2020
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    '@typescript-eslint/no-explicit-any': 'warn',
    'vue/multi-word-component-names': 'off'
  }
}

.prettierrc.js:
module.exports = {
  semi: false,
  singleQuote: true,
  printWidth: 100,
  tabWidth: 2,
  useTabs: false,
  trailingComma: 'none',
  bracketSpacing: true,
  arrowParens: 'avoid'
}

环境配置文件:

.env.development:
VITE_APP_TITLE=工程管理系统
VITE_APP_API_BASE_URL=http://localhost:3000/api
VITE_APP_MOCK_ENABLED=true
VITE_APP_DEBUG=true

.env.production:
VITE_APP_TITLE=工程管理系统
VITE_APP_API_BASE_URL=https://api.domain.com
VITE_APP_MOCK_ENABLED=false
VITE_APP_DEBUG=false

.env.test:
VITE_APP_TITLE=工程管理系统
VITE_APP_API_BASE_URL=http://test-api.domain.com
VITE_APP_MOCK_ENABLED=false
VITE_APP_DEBUG=true

-- Mock数据示例 --

mock/dashboard.json:
{
  "materialStats": {
    "unused": {
      "totalItems": 25,
      "totalAmount": 125680,
      "items": [
        {
          "name": "电缆线",
          "quantity": 100,
          "unit": "米",
          "totalPrice": 15000
        }
      ]
    },
    "used": {
      "totalItems": 18,
      "totalAmount": 89320,
      "items": [
        {
          "name": "电线",
          "quantity": 200,
          "unit": "米",
          "totalPrice": 8000
        }
      ]
    }
  },
  "projectStats": {
    "notStarted": 5,
    "inProgress": 12,
    "paused": 3,
    "completed": 8,
    "projects": [
      {
        "name": "阳光小区A栋",
        "status": "在建",
        "lastUpdateTime": "2024-01-15 14:30"
      }
    ]
  }
}

mock/materials.json:
{
  "list": [
    {
      "id": 1,
      "code": "WL001",
      "name": "电缆线",
      "model": "YJV-3*4",
      "specification": "3*4mm²",
      "unit": "米",
      "category": "甲料",
      "price": 30.00,
      "quantity": 500,
      "warningQuantity": 100,
      "status": 1
    }
  ],
  "total": 156,
  "page": 1,
  "pageSize": 20
}

-- 主要组件示例 --

src/views/dashboard/index.vue:
<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <el-col :span="6">
        <MaterialStats />
      </el-col>
      <el-col :span="6">
        <ProjectStats />
      </el-col>
      <el-col :span="6">
        <LooseMaterialStats />
      </el-col>
      <el-col :span="6">
        <SalesStats />
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import MaterialStats from './MaterialStats.vue'
import ProjectStats from './ProjectStats.vue'
import LooseMaterialStats from './LooseMaterialStats.vue'
import SalesStats from './SalesStats.vue'
</script>

src/api/warehouse.ts:
import request from '@/utils/request'
import type { MaterialListParams, MaterialItem } from './types/warehouse'

export const getMaterialList = (params: MaterialListParams) => {
  return request.get<MaterialItem[]>('/materials', { params })
}

export const createMaterial = (data: Partial<MaterialItem>) => {
  return request.post<MaterialItem>('/materials', data)
}

export const updateMaterial = (id: number, data: Partial<MaterialItem>) => {
  return request.put<MaterialItem>(`/materials/${id}`, data)
}

export const deleteMaterial = (id: number) => {
  return request.delete(`/materials/${id}`)
}

src/utils/mock/index.ts:
import Mock from 'mockjs'
import authMock from './auth'
import dashboardMock from './dashboard'
import warehouseMock from './warehouse'
import looseOrdersMock from './looseOrders'
import projectsMock from './projects'
import employeesMock from './employees'
import systemMock from './system'

// 设置Mock延迟
Mock.setup({
  timeout: '200-600'
})

// 注册所有Mock
authMock()
dashboardMock()
warehouseMock()
looseOrdersMock()
projectsMock()
employeesMock()
systemMock()

export default Mock 