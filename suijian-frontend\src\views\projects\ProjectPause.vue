<template>
  <div class="project-pause-container">
    <el-card class="main-card">
      
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
        <!-- 工程选择 -->
        <el-row :gutter="20" class="form-section">
          <el-col :span="24">
            <div class="section-title">
              <el-icon><Tools /></el-icon>
              工程选择
            </div>
          </el-col>
          <el-col :span="24">
            <el-form-item label="选择工程:" prop="projectId">
              <el-select
                v-model="formData.projectId"
                placeholder="请选择要暂停的工程"
                style="width: 100%"
                @change="onProjectChange"
              >
                <el-option
                  v-for="project in projectList"
                  :key="project.id"
                  :label="`${project.name} (${project.status})`"
                  :value="project.id"
                  :disabled="project.status !== '在建'"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 工程信息 -->
        <el-row :gutter="20" class="form-section" v-if="selectedProject">
          <el-col :span="24">
            <div class="section-title">
              <el-icon><Document /></el-icon>
              当前工程信息
            </div>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工程名称:">
              <el-input v-model="selectedProject.name" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="当前状态:">
              <el-tag :type="getStatusType(selectedProject.status)">
                {{ selectedProject.status }}
              </el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="当前进度:">
              <el-progress :percentage="selectedProject.progress" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开始时间:">
              <el-input v-model="selectedProject.startDate" readonly />
            </el-form-item>
          </el-col>
        </el-row>
      
      <!-- 暂停信息 -->
      <el-row :gutter="20" class="form-section" v-if="selectedProject">
        <el-col :span="24">
          <div class="section-title">
            <el-icon><EditPen /></el-icon>
            暂停信息
          </div>
        </el-col>
        <el-col :span="24">
          <el-form-item label="暂停原因:">
            <el-input
              v-model="formData.pauseReason"
              type="textarea"
              :rows="3"
              placeholder="请输入暂停原因"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-checkbox-group v-model="formData.pauseReasonTags">
            <el-checkbox label="材料供应不足">材料供应不足</el-checkbox>
            <el-checkbox label="人员不足">人员不足</el-checkbox>
            <el-checkbox label="天气原因">天气原因</el-checkbox>
            <el-checkbox label="客户要求">客户要求</el-checkbox>
            <el-checkbox label="其他">其他</el-checkbox>
          </el-checkbox-group>
        </el-col>
        <el-col :span="12" style="margin-top: 20px;">
          <el-form-item label="预计恢复时间:">
            <el-date-picker
              v-model="formData.estimatedResumeTime"
              type="date"
              placeholder="请选择预计恢复时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="暂停期间安排:">
            <el-input
              v-model="formData.pauseArrangement"
              type="textarea"
              :rows="3"
              placeholder="请输入暂停期间安排"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 暂停影响评估 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">暂停影响评估</div>
        </el-col>
        <el-col :span="24">
          <el-card class="assessment-card">
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="assessment-item">
                  <span class="label">进度影响:</span>
                  <span>已完成65%，暂停后预计延期5天</span>
                </div>
              </el-col>
              <el-col :span="24">
                <div class="assessment-item">
                  <span class="label">成本影响:</span>
                  <span>暂停期间人员费用约¥3,200</span>
                </div>
              </el-col>
              <el-col :span="24">
                <div class="assessment-item">
                  <span class="label">物料影响:</span>
                  <span>已使用物料价值¥28,500</span>
                </div>
              </el-col>
              <el-col :span="24">
                <div class="assessment-item">
                  <span class="label">客户沟通:</span>
                  <span>已通知客户并获得确认</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 备注信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">备注信息</div>
        </el-col>
        <el-col :span="24">
          <el-form-item label="详细说明:">
            <el-input
              v-model="formData.detailedExplanation"
              type="textarea"
              :rows="3"
              placeholder="请输入详细说明"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="后续处理计划:">
            <el-input
              v-model="formData.followUpPlan"
              type="textarea"
              :rows="3"
              placeholder="请输入后续处理计划"
            />
          </el-form-item>
        </el-col>
      </el-row>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button type="primary" @click="handleSave">保存</el-button>
          <el-button type="success" @click="handleSubmit">提交</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import {
  Tools,
  Document,
  EditPen
} from '@element-plus/icons-vue'

// 表单引用
const formRef = ref<FormInstance>()

// 项目列表
const projectList = ref([
  { id: 1, name: '阳光小区A栋', status: '在建', progress: 65, startDate: '2024-01-01' },
  { id: 2, name: '花园广场项目', status: '在建', progress: 45, startDate: '2024-01-05' },
  { id: 3, name: '绿城花园D区', status: '在建', progress: 75, startDate: '2023-12-01' },
  { id: 4, name: '商业中心B区', status: '已完成', progress: 100, startDate: '2023-10-01' },
  { id: 5, name: '工业园区C栋', status: '未开始', progress: 0, startDate: '' }
])

// 选中的项目
const selectedProject = ref<any>(null)

// 表单数据
const formData = reactive({
  projectId: '',
  pauseReason: '',
  pauseReasonTags: [] as string[],
  estimatedResumeTime: '',
  pauseArrangement: '',
  detailedExplanation: '',
  followUpPlan: ''
})

// 表单验证规则
const formRules: FormRules = {
  projectId: [
    { required: true, message: '请选择工程', trigger: 'change' }
  ],
  pauseReason: [
    { required: true, message: '请输入暂停原因', trigger: 'blur' }
  ]
}

// 工程选择变更
const onProjectChange = (projectId: string) => {
  selectedProject.value = projectList.value.find(p => p.id === Number(projectId))
}

// 获取状态类型
const getStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '未开始': 'info',
    '在建': 'warning',
    '暂停': 'danger',
    '已完成': 'success'
  }
  return statusMap[status] || 'info'
}

// 保存
const handleSave = () => {
  ElMessage.success('保存成功')
  console.log('保存数据:', formData)
}

// 提交
const handleSubmit = () => {
  ElMessage.success('提交成功')
  console.log('提交数据:', formData)
}

// 取消
const handleCancel = () => {
  ElMessage.info('已取消')
}
</script>

<style lang="scss" scoped>
.project-pause-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
    
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #606266;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
    
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  
  .assessment-card {
    background-color: #f0f9ff;
    border-color: #b3e0ff;
    
    .assessment-item {
      margin-bottom: 10px;
      
      .label {
        font-weight: bold;
        color: #409eff;
        margin-right: 10px;
      }
    }
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
    }
  }
}
</style>