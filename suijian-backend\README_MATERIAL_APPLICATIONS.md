# 领料申请功能实现说明

## 功能概述

领料申请功能允许用户创建物料申请单，包含申请信息、物料清单和审批流程。

## 数据库表结构

### 1. material_applications (领料申请表)
- `id`: 主键ID
- `apply_no`: 申请单号（唯一）
- `apply_date`: 申请日期
- `applicant_id`: 申请人ID
- `receiver_id`: 领料人ID
- `purpose`: 申领用途
- `order_no`: 关联订单号
- `status`: 申请状态（pending/approved/rejected）
- `remarks`: 备注
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 2. material_application_items (领料申请明细表)
- `id`: 主键ID
- `application_id`: 申请ID（外键）
- `material_id`: 物料ID（外键）
- `quantity`: 申请数量
- `remarks`: 备注
- `created_at`: 创建时间

## API接口

### 1. 创建领料申请
- **URL**: `POST /api/material-applications`
- **认证**: 需要JWT token
- **请求体**:
```json
{
  "applyNo": "LY20250108001",
  "applyDate": "2025-01-08",
  "applicantId": "emp-001",
  "receiverId": "emp-002",
  "purpose": "工程",
  "orderNo": "DD001",
  "remarks": "申请备注",
  "materials": [
    {
      "materialId": "material-xxx",
      "quantity": 10,
      "remarks": "物料备注"
    }
  ]
}
```

### 2. 获取申请列表
- **URL**: `GET /api/material-applications`
- **认证**: 需要JWT token
- **查询参数**:
  - `page`: 页码
  - `pageSize`: 每页数量
  - `applyNo`: 申请单号
  - `applicantId`: 申请人ID
  - `receiverId`: 领料人ID
  - `purpose`: 用途
  - `status`: 状态
  - `startDate`: 开始日期
  - `endDate`: 结束日期

### 3. 获取申请详情
- **URL**: `GET /api/material-applications/:id`
- **认证**: 需要JWT token

### 4. 更新申请状态
- **URL**: `PUT /api/material-applications/:id/status`
- **认证**: 需要JWT token
- **请求体**:
```json
{
  "status": "approved",
  "remarks": "审批备注"
}
```

### 5. 删除申请
- **URL**: `DELETE /api/material-applications/:id`
- **认证**: 需要JWT token
- **限制**: 只能删除待审核状态的申请

## 前端功能

### 主要特性
1. **表单验证**: 验证必填字段和物料清单
2. **库存检查**: 自动检查物料库存是否充足
3. **数据持久化**: 使用localStorage保存草稿
4. **物料选择**: 集成物料选择器组件
5. **实时保存**: 自动保存表单数据

### 页面组件
- 申请信息表单
- 物料清单表格
- 物料选择器弹窗
- 统一备注区域

## 安装和配置

### 1. 创建数据库表
```bash
cd suijian-backend
node scripts/init_material_applications.js
```

### 2. 重启后端服务
```bash
npm run dev
```

### 3. 前端访问
访问 `http://localhost:3000/warehouse/material-apply` 即可使用领料申请功能。

## 使用流程

1. **填写申请信息**: 选择申请人、领料人、用途等
2. **添加物料**: 点击"添加物料"选择需要的物料
3. **设置数量**: 为每个物料设置申请数量
4. **添加备注**: 填写统一备注或单个物料备注
5. **保存申请**: 点击"保存"或"保存并打印"

## 注意事项

1. 申请单号由系统自动生成，格式为 `LY + 年月日 + 3位随机数`
2. 物料库存不足时无法提交申请
3. 申请状态包括：pending（待审核）、approved（已批准）、rejected（已拒绝）
4. 只有待审核状态的申请可以删除
5. 所有操作都会记录在系统日志中

## 错误处理

- 表单验证失败会显示具体错误信息
- 库存不足会提示具体物料
- 网络错误会显示重试提示
- 数据库错误会记录详细日志 