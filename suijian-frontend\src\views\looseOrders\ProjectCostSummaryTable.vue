<template>
  <el-table :data="data" style="width: 100%" border show-summary :summary-method="summaryMethod">
    <el-table-column prop="serialNo" label="序号" width="60" align="center" />
    <el-table-column prop="projectName" label="单项工程名称" width="200" />
    <el-table-column label="工程安装人工费(元)" align="center">
      <el-table-column prop="preMeterLaborCost" label="表前安装人工费" width="120" align="center">
        <template #default="{ row }">
          ¥{{ formatNumber(row.preMeterLaborCost) }}
        </template>
      </el-table-column>
      <el-table-column prop="indoorLaborCost" label="户内安装人工费" width="120" align="center">
        <template #default="{ row }">
          ¥{{ formatNumber(row.indoorLaborCost) }}
        </template>
      </el-table-column>
      <el-table-column prop="laborSubtotal" label="小计(元)" width="120" align="center">
        <template #default="{ row }">
          ¥{{ formatNumber(row.laborSubtotal) }}
        </template>
      </el-table-column>
    </el-table-column>
    <el-table-column prop="installedHouseholds" label="安装户数" width="100" align="center" />
    <el-table-column label="实际耗用材料金额" align="center">
      <el-table-column prop="gasMeterCost" label="气表" width="100" align="center">
        <template #default="{ row }">
          ¥{{ formatNumber(row.gasMeterCost) }}
        </template>
      </el-table-column>
      <el-table-column prop="postMeterCost" label="表后" width="100" align="center">
        <template #default="{ row }">
          ¥{{ formatNumber(row.postMeterCost) }}
        </template>
      </el-table-column>
      <el-table-column prop="fittingsCost" label="管件" width="100" align="center">
        <template #default="{ row }">
          ¥{{ formatNumber(row.fittingsCost) }}
        </template>
      </el-table-column>
      <el-table-column prop="materialSubtotal" label="小计(元)" width="120" align="center">
        <template #default="{ row }">
          ¥{{ formatNumber(row.materialSubtotal) }}
        </template>
      </el-table-column>
    </el-table-column>
    <el-table-column prop="actualReceivedAmount" label="实际领用材料金额(元)" width="150" align="center">
      <template #default="{ row }">
        ¥{{ formatNumber(row.actualReceivedAmount) }}
      </template>
    </el-table-column>
    <el-table-column prop="overReceivedAmount" label="超领甲供材料金额" width="150" align="center">
      <template #default="{ row }">
        ¥{{ formatNumber(row.overReceivedAmount) }}
      </template>
    </el-table-column>
    <el-table-column prop="totalProjectCost" label="工程总造价(元)" width="150" align="center">
      <template #header>
        <el-tooltip content="计算公式：工程总造价 = 人工费小计 + 实际领用材料金额" placement="top">
          <span>工程总造价(元)</span>
        </el-tooltip>
      </template>
      <template #default="{ row }">
        ¥{{ formatNumber(row.totalProjectCost) }}
      </template>
    </el-table-column>
    <el-table-column prop="payableAmount" label="应付施工单位金额" width="150" align="center">
      <template #header>
        <el-tooltip content="计算公式：应付施工单位金额 = 人工费小计 - 超领甲供材料金额" placement="top">
          <span>应付施工单位金额</span>
        </el-tooltip>
      </template>
      <template #default="{ row }">
        ¥{{ formatNumber(row.payableAmount) }}
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'
import type { TableColumnCtx } from 'element-plus'

interface SummaryMethodParams {
  columns: TableColumnCtx<any>[]
  data: any[]
}

const props = defineProps<{
  data: any[]
  summaryMethod: (params: SummaryMethodParams) => string[]
  formatNumber: (num: number | undefined | null) => string
}>()
</script>