#!/usr/bin/env node

/**
 * Excel导入日志查看工具
 * 用于查看和分析Excel导入的详细日志
 */

const fs = require('fs');
const path = require('path');

// 日志文件路径
const LOG_DIR = path.join(__dirname, '../logs');
const LOG_FILE = path.join(LOG_DIR, 'app.log');

/**
 * 读取日志文件
 */
function readLogFile() {
    try {
        if (!fs.existsSync(LOG_FILE)) {
            console.log('❌ 日志文件不存在:', LOG_FILE);
            return null;
        }

        const content = fs.readFileSync(LOG_FILE, 'utf8');
        return content;
    } catch (error) {
        console.error('❌ 读取日志文件失败:', error.message);
        return null;
    }
}

/**
 * 解析日志行
 */
function parseLogLine(line) {
    try {
        // 匹配日志格式: timestamp level: message
        const match = line.match(/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (\w+): (.+)$/);
        if (!match) return null;

        const [, timestamp, level, message] = match;

        // 尝试解析JSON数据
        let data = null;
        try {
            const jsonMatch = message.match(/\{.*\}$/);
            if (jsonMatch) {
                data = JSON.parse(jsonMatch[0]);
            }
        } catch (e) {
            // JSON解析失败，忽略
        }

        return {
            timestamp,
            level,
            message: data ? message.replace(/\{.*\}$/, '').trim() : message,
            data
        };
    } catch (error) {
        return null;
    }
}

/**
 * 过滤Excel导入相关的日志
 */
function filterImportLogs(logs) {
    const importKeywords = [
        'Excel导入',
        '开始Excel导入',
        'Excel导入完成',
        '导入进度',
        '处理第',
        '行数据',
        '行跳过',
        '行插入成功',
        '行导入失败',
        '导入错误详情'
    ];

    return logs.filter(log => {
        if (!log) return false;
        return importKeywords.some(keyword => log.message.includes(keyword));
    });
}

/**
 * 按批次ID分组日志
 */
function groupLogsByBatch(logs) {
    const batches = {};

    logs.forEach(log => {
        if (log.data && log.data.batchId) {
            const batchId = log.data.batchId;
            if (!batches[batchId]) {
                batches[batchId] = [];
            }
            batches[batchId].push(log);
        }
    });

    return batches;
}

/**
 * 显示导入统计信息
 */
function showImportStats(batches) {
    console.log('\n📊 Excel导入统计信息');
    console.log('='.repeat(50));

    const batchIds = Object.keys(batches);
    console.log(`总导入批次: ${batchIds.length}`);

    batchIds.forEach((batchId, index) => {
        const batchLogs = batches[batchId];
        const startLog = batchLogs.find(log => log.message.includes('开始Excel导入'));
        const endLog = batchLogs.find(log => log.message.includes('Excel导入完成'));

        console.log(`\n${index + 1}. 批次ID: ${batchId}`);

        if (startLog) {
            console.log(`   开始时间: ${startLog.timestamp}`);
            if (startLog.data) {
                console.log(`   文件名: ${startLog.data.fileName || '未知'}`);
                console.log(`   文件大小: ${startLog.data.fileSize ? (startLog.data.fileSize / 1024 / 1024).toFixed(2) + 'MB' : '未知'}`);
            }
        }

        if (endLog && endLog.data) {
            console.log(`   总行数: ${endLog.data.totalRows || 0}`);
            console.log(`   成功: ${endLog.data.successCount || 0}`);
            console.log(`   跳过: ${endLog.data.skipCount || 0}`);
            console.log(`   失败: ${endLog.data.errorCount || 0}`);
            console.log(`   耗时: ${endLog.data.duration || '未知'}`);
        }
    });
}

/**
 * 显示详细日志
 */
function showDetailedLogs(batches, batchId = null) {
    if (batchId) {
        // 显示指定批次的详细日志
        const batchLogs = batches[batchId];
        if (!batchLogs) {
            console.log(`❌ 未找到批次ID: ${batchId}`);
            return;
        }

        console.log(`\n📋 批次 ${batchId} 详细日志`);
        console.log('='.repeat(50));

        batchLogs.forEach(log => {
            const time = log.timestamp.split(' ')[1]; // 只显示时间部分
            const level = log.level.padEnd(5);
            console.log(`[${time}] ${level} ${log.message}`);

            if (log.data) {
                // 显示关键数据
                if (log.data.rowNumber) {
                    console.log(`   行号: ${log.data.rowNumber}`);
                }
                if (log.data.orderNo) {
                    console.log(`   工单号: ${log.data.orderNo}`);
                }
                if (log.data.error) {
                    console.log(`   错误: ${log.data.error}`);
                }
            }
        });
    } else {
        // 显示所有批次的简要信息
        console.log('\n📋 所有导入批次');
        console.log('='.repeat(50));

        Object.keys(batches).forEach((batchId, index) => {
            const batchLogs = batches[batchId];
            const startLog = batchLogs.find(log => log.message.includes('开始Excel导入'));
            const endLog = batchLogs.find(log => log.message.includes('Excel导入完成'));

            console.log(`\n${index + 1}. 批次ID: ${batchId}`);

            if (startLog) {
                console.log(`   开始: ${startLog.timestamp}`);
            }

            if (endLog && endLog.data) {
                console.log(`   结果: 成功${endLog.data.successCount || 0}条, 跳过${endLog.data.skipCount || 0}条, 失败${endLog.data.errorCount || 0}条`);
                console.log(`   耗时: ${endLog.data.duration || '未知'}`);
            }
        });
    }
}

/**
 * 显示错误日志
 */
function showErrorLogs(logs) {
    const errorLogs = logs.filter(log => log.level === 'error');

    if (errorLogs.length === 0) {
        console.log('\n✅ 没有发现错误日志');
        return;
    }

    console.log('\n❌ 错误日志');
    console.log('='.repeat(50));

    errorLogs.forEach((log, index) => {
        console.log(`\n${index + 1}. [${log.timestamp}] ${log.message}`);
        if (log.data) {
            if (log.data.batchId) {
                console.log(`   批次ID: ${log.data.batchId}`);
            }
            if (log.data.rowNumber) {
                console.log(`   行号: ${log.data.rowNumber}`);
            }
            if (log.data.orderNo) {
                console.log(`   工单号: ${log.data.orderNo}`);
            }
            if (log.data.error) {
                console.log(`   错误详情: ${log.data.error}`);
            }
        }
    });
}

/**
 * 主函数
 */
function main() {
    console.log('🔍 Excel导入日志查看工具');
    console.log('='.repeat(50));

    // 读取日志文件
    const logContent = readLogFile();
    if (!logContent) {
        return;
    }

    // 解析日志行
    const lines = logContent.split('\n').filter(line => line.trim());
    const logs = lines.map(parseLogLine).filter(log => log);

    console.log(`📄 总日志行数: ${lines.length}`);
    console.log(`📝 有效日志条数: ${logs.length}`);

    // 过滤导入相关日志
    const importLogs = filterImportLogs(logs);
    console.log(`📊 Excel导入相关日志: ${importLogs.length}条`);

    if (importLogs.length === 0) {
        console.log('\n❌ 没有找到Excel导入相关的日志');
        return;
    }

    // 按批次分组
    const batches = groupLogsByBatch(importLogs);

    // 获取命令行参数
    const args = process.argv.slice(2);
    const command = args[0];
    const batchId = args[1];

    switch (command) {
        case 'stats':
            showImportStats(batches);
            break;
        case 'detail':
            showDetailedLogs(batches, batchId);
            break;
        case 'errors':
            showErrorLogs(importLogs);
            break;
        case 'list':
            showDetailedLogs(batches);
            break;
        default:
            console.log('\n📖 使用方法:');
            console.log('  node view_import_logs.js stats          # 显示导入统计信息');
            console.log('  node view_import_logs.js list           # 显示所有批次列表');
            console.log('  node view_import_logs.js detail <batchId> # 显示指定批次详细日志');
            console.log('  node view_import_logs.js errors         # 显示错误日志');
            console.log('\n💡 示例:');
            console.log('  node view_import_logs.js stats');
            console.log('  node view_import_logs.js detail abc123-def456');
            console.log('  node view_import_logs.js errors');
    }
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = {
    readLogFile,
    parseLogLine,
    filterImportLogs,
    groupLogsByBatch
};