const express = require('express');
const router = express.Router();

// 导入各个模块的路由
const authRoutes = require('./auth');
const dashboardRoutes = require('./dashboard');
const materialsRoutes = require('./materials');
const employeesRoutes = require('./employees');
const materialApplicationsRoutes = require('./materialApplications');
const productsRoutes = require('./products');
const looseOrdersRoutes = require('./looseOrders');
const importProgressRoutes = require('./importProgress');
const communitiesRoutes = require('./communities');

// 注册路由
router.use('/auth', authRoutes);
router.use('/dashboard', dashboardRoutes);
router.use('/materials', materialsRoutes);
router.use('/employees', employeesRoutes);
router.use('/material-applications', materialApplicationsRoutes);
router.use('/products', productsRoutes);
router.use('/loose-orders', looseOrdersRoutes);
router.use('/import-progress', importProgressRoutes);
router.use('/communities', communitiesRoutes);

// API根路径
router.get('/', (req, res) => {
    res.json({
        success: true,
        message: '工程管理系统API服务',
        version: process.env.APP_VERSION || '1.0.0',
        timestamp: new Date().toISOString()
    });
});

module.exports = router;