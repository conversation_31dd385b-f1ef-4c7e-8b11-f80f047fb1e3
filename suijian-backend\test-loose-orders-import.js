const request = require('supertest');
const app = require('./src/app');
const path = require('path');

/**
 * 测试loose_orders导入功能
 */
async function testLooseOrdersImport() {
    console.log('开始测试loose_orders导入功能...\n');

    try {
        // 1. 测试获取订单列表
        console.log('1. 测试获取订单列表...');
        const listResponse = await request(app)
            .get('/api/loose-orders')
            .set('Authorization', 'Bearer test-token');

        console.log('响应状态:', listResponse.status);
        console.log('响应数据:', JSON.stringify(listResponse.body, null, 2));
        console.log('');

        // 2. 测试创建订单
        console.log('2. 测试创建订单...');
        const createResponse = await request(app)
            .post('/api/loose-orders')
            .set('Authorization', 'Bearer test-token')
            .send({
                orderNo: 'TEST001',
                customerName: '测试用户',
                orderType: '测试工单',
                status: 'pending',
                address: '测试地址',
                appealDescription: '测试诉求描述',
                totalAmount: 100.00,
                remarks: '测试备注'
            });

        console.log('响应状态:', createResponse.status);
        console.log('响应数据:', JSON.stringify(createResponse.body, null, 2));
        console.log('');

        // 3. 测试重复创建（应该失败）
        console.log('3. 测试重复创建订单（应该失败）...');
        const duplicateResponse = await request(app)
            .post('/api/loose-orders')
            .set('Authorization', 'Bearer test-token')
            .send({
                orderNo: 'TEST001', // 重复的订单号
                customerName: '测试用户2',
                orderType: '测试工单2',
                status: 'pending'
            });

        console.log('响应状态:', duplicateResponse.status);
        console.log('响应数据:', JSON.stringify(duplicateResponse.body, null, 2));
        console.log('');

        // 4. 测试获取订单详情
        if (createResponse.body.success && createResponse.body.data.id) {
            console.log('4. 测试获取订单详情...');
            const detailResponse = await request(app)
                .get(`/api/loose-orders/${createResponse.body.data.id}`)
                .set('Authorization', 'Bearer test-token');

            console.log('响应状态:', detailResponse.status);
            console.log('响应数据:', JSON.stringify(detailResponse.body, null, 2));
            console.log('');
        }

        // 5. 测试更新订单
        if (createResponse.body.success && createResponse.body.data.id) {
            console.log('5. 测试更新订单...');
            const updateResponse = await request(app)
                .put(`/api/loose-orders/${createResponse.body.data.id}`)
                .set('Authorization', 'Bearer test-token')
                .send({
                    status: 'in_progress',
                    remarks: '已更新备注'
                });

            console.log('响应状态:', updateResponse.status);
            console.log('响应数据:', JSON.stringify(updateResponse.body, null, 2));
            console.log('');
        }

        // 6. 测试订单派发
        if (createResponse.body.success && createResponse.body.data.id) {
            console.log('6. 测试订单派发...');
            const assignResponse = await request(app)
                .post(`/api/loose-orders/${createResponse.body.data.id}/assign`)
                .set('Authorization', 'Bearer test-token')
                .send({
                    assignedWorkerId: 'test-worker-id',
                    estimatedDays: 3,
                    startDate: '2024-01-15',
                    remarks: '派发备注'
                });

            console.log('响应状态:', assignResponse.status);
            console.log('响应数据:', JSON.stringify(assignResponse.body, null, 2));
            console.log('');
        }

        // 7. 测试删除订单
        if (createResponse.body.success && createResponse.body.data.id) {
            console.log('7. 测试删除订单...');
            const deleteResponse = await request(app)
                .delete(`/api/loose-orders/${createResponse.body.data.id}`)
                .set('Authorization', 'Bearer test-token');

            console.log('响应状态:', deleteResponse.status);
            console.log('响应数据:', JSON.stringify(deleteResponse.body, null, 2));
            console.log('');
        }

        console.log('所有测试完成！');

    } catch (error) {
        console.error('测试过程中发生错误:', error);
    }
}

// 运行测试
testLooseOrdersImport();