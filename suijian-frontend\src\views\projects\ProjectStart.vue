<template>
  <div class="project-start-container">
    <el-card class="main-card">
      
      <!-- 工程信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">工程信息</div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="选择工程:" prop="selectedProject">
            <el-select
              v-model="formData.selectedProject"
              placeholder="请选择工程"
              style="width: 100%"
              @change="handleProjectChange"
            >
              <el-option
                v-for="project in projectList"
                :key="project.id"
                :label="project.projectName"
                :value="project.id"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <!-- 只有选择工程后才显示其他信息 -->
        <template v-if="selectedProjectInfo">
          <el-col :span="12">
            <el-form-item label="甲方订单号:">
              <span>{{ selectedProjectInfo.partyOrderNo }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工程名称:">
              <span>{{ selectedProjectInfo.projectName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工程地址:">
              <span>{{ selectedProjectInfo.projectAddress }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工程状态:">
              <el-tag :type="getStatusType(selectedProjectInfo.status)">
                {{ selectedProjectInfo.status }}
              </el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预估开始时间:">
              <span>{{ selectedProjectInfo.estimatedStartTime }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预估结束时间:">
              <span>{{ selectedProjectInfo.estimatedEndTime }}</span>
            </el-form-item>
          </el-col>
        </template>
      </el-row>
      
      <!-- 开始信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">开始信息</div>
        </el-col>

        <el-col :span="12">
          <el-form-item label="工程负责人:">
            <el-input v-model="formData.projectManager" placeholder="请输入工程负责人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人电话:">
            <el-input v-model="formData.managerPhone" placeholder="请输入负责人电话" />
          </el-form-item>
        </el-col>
      </el-row>

      
      <!-- 物料准备 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">物料准备</div>
        </el-col>
        <el-col :span="24">
          <el-table :data="materialList" border class="material-table">
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="materialCode" label="公司物料编码" width="120">
              <template #default="scope">
                <span>{{ scope.row.materialCode }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="materialName" label="名称" width="120" />
            <el-table-column prop="model" label="型号" width="100" />
            <el-table-column prop="specification" label="规格" width="100" />
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column prop="stockQuantity" label="库存数量" width="100" />
            <el-table-column prop="plannedUsage" label="计划用量">
              <template #default="scope">
                <el-input-number 
                  v-model="scope.row.plannedUsage" 
                  :min="0" 
                  controls-position="right" 
                  style="width: 100%" 
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" fixed="right">
              <template #default="scope">
                <el-button type="danger" link @click="removeMaterial(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 15px;">
            <el-button type="primary" icon="Plus" @click="selectMaterial">添加物料</el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 备注信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">备注信息</div>
        </el-col>
        <el-col :span="24">
          <el-form-item label="开始说明:">
            <el-input
              v-model="formData.startInstructions"
              type="textarea"
              :rows="3"
              placeholder="请输入开始说明"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注:">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="2"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button type="success" @click="handleSubmit">提交</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
      
      <!-- 选择物料弹窗 -->
      <el-dialog v-model="materialDialogVisible" title="选择物料" width="800">
        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :span="18">
            <el-input v-model="materialSearch" placeholder="请输入搜索关键词" clearable />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" icon="Search" @click="searchMaterials">搜索</el-button>
          </el-col>
        </el-row>
        
        <el-table :data="materialOptions" border height="400">
          <el-table-column prop="materialCode" label="公司物料编码" width="120" />
          <el-table-column prop="materialName" label="物料名称" width="120" />
          <el-table-column prop="model" label="型号" width="100" />
          <el-table-column prop="specification" label="规格" width="100" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="stockQuantity" label="库存" width="80" />
          <el-table-column label="操作" width="80" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="selectMaterialItem(scope.row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <template #footer>
          <el-button @click="materialDialogVisible = false">取消</el-button>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 表单数据
const formData = reactive({
  selectedProject: '',
  projectManager: '',
  managerPhone: '',
  startInstructions: '',
  remarks: ''
})

// 工程列表
const projectList = ref([
  {
    id: 'P001',
    partyOrderNo: 'GC202401001',
    projectName: '阳光小区A栋',
    projectAddress: '阳光小区A栋1-6层',
    status: '未开始',
    estimatedStartTime: '2024-02-01',
    estimatedEndTime: '2024-03-15'
  },
  {
    id: 'P002',
    partyOrderNo: 'GC202401002',
    projectName: '花园广场项目',
    projectAddress: '花园广场B区',
    status: '未开始',
    estimatedStartTime: '2024-02-01',
    estimatedEndTime: '2024-04-30'
  },
  {
    id: 'P003',
    partyOrderNo: 'GC202401003',
    projectName: '商业中心B区',
    projectAddress: '商业中心B区地下停车场',
    status: '未开始',
    estimatedStartTime: '2024-02-15',
    estimatedEndTime: '2024-05-01'
  }
])

// 选中的工程信息
const selectedProjectInfo = computed(() => {
  if (!formData.selectedProject) return null
  return projectList.value.find(project => project.id === formData.selectedProject)
})



// 物料列表
const materialList = ref([
  {
    id: 1,
    materialCode: 'WL001',
    materialName: '电缆线',
    model: 'YJV-3*4',
    specification: '3*4mm²',
    unit: '米',
    stockQuantity: 500,
    plannedUsage: 200
  },
  {
    id: 2,
    materialCode: 'WL002',
    materialName: '开关面板',
    model: 'KP-86',
    specification: '86型',
    unit: '个',
    stockQuantity: 100,
    plannedUsage: 30
  }
])

// 物料选项
const materialOptions = ref([
  {
    id: 1,
    materialCode: 'WL001',
    materialName: '电缆线',
    model: 'YJV-3*4',
    specification: '3*4mm²',
    unit: '米',
    stockQuantity: 500
  },
  {
    id: 2,
    materialCode: 'WL002',
    materialName: '开关面板',
    model: 'KP-86',
    specification: '86型',
    unit: '个',
    stockQuantity: 100
  },
  {
    id: 3,
    materialCode: 'WL003',
    materialName: '插座',
    model: 'ZP-86',
    specification: '86型',
    unit: '个',
    stockQuantity: 150
  },
  {
    id: 4,
    materialCode: 'WL004',
    materialName: '灯具',
    model: 'LED-12W',
    specification: '12W',
    unit: '个',
    stockQuantity: 200
  }
])

// 弹窗控制
const materialDialogVisible = ref(false)

// 物料搜索
const materialSearch = ref('')

// 工程选择变更处理
const handleProjectChange = (projectId: string) => {
  console.log('选择的工程ID:', projectId)
  // 这里可以根据选择的工程加载相关的物料信息等
}

// 获取状态类型
const getStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  switch (status) {
    case '未开始':
      return 'info'
    case '进行中':
      return 'primary'
    case '暂停':
      return 'warning'
    case '已完成':
      return 'success'
    default:
      return 'info'
  }
}

// 工种变更


// 添加物料
const selectMaterial = () => {
  materialDialogVisible.value = true
}

// 搜索物料
const searchMaterials = () => {
  ElMessage.success('搜索物料')
  console.log('搜索关键词:', materialSearch.value)
}

// 选择物料项
const selectMaterialItem = (row: any) => {
  // 检查是否已添加
  const exists = materialList.value.some(item => item.id === row.id)
  if (exists) {
    ElMessage.warning('该物料已添加')
    return
  }
  
  materialList.value.push({
    ...row,
    plannedUsage: 0
  })
  
  materialDialogVisible.value = false
  ElMessage.success('添加成功')
}

// 删除物料
const removeMaterial = (index: number) => {
  materialList.value.splice(index, 1)
}

// 保存
const handleSave = () => {
  ElMessage.success('保存成功')
  console.log('保存数据:', formData, materialList.value)
}

// 提交
const handleSubmit = () => {
  ElMessage.success('提交成功')
  console.log('提交数据:', formData, materialList.value)
}

// 取消
const handleCancel = () => {
  ElMessage.info('已取消')
}
</script>

<style lang="scss" scoped>
.project-start-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
    
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #606266;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
    
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  
  .staff-table,
  .material-table {
    margin-top: 10px;
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
    }
  }
}
</style>