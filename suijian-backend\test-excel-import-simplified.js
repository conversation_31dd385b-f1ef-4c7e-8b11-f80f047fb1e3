const { importExcel } = require('./src/controllers/looseOrdersController');

// 模拟请求和响应对象
const mockReq = {
    file: {
        path: './test-data/sample-orders.xlsx',
        originalname: 'sample-orders.xlsx',
        size: 1024,
        mimetype: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit'
    },
    user: {
        id: 'test-user-001'
    }
};

const mockRes = {
    status: (code) => ({
        json: (data) => {
            console.log('Response:', { status: code, data });
        }
    })
};

// 测试导入功能
console.log('测试Excel导入功能（Excel数据映射到甲单字段）...');
console.log('支持的字段:');
console.log('- 工单号 (工单号, 订单号, 编号, ID, 序号) → order_no');
console.log('- 用户编号 (用户编号, 客户编号, 客户编码, 编号, Code) → customer_code');
console.log('- 用户姓名 (用户姓名, 客户姓名, 姓名, 客户名称, Name) → customer_name');
console.log('- 移动电话 (移动电话, 手机号码, 联系电话, 电话, Phone) → phone');
console.log('- 地址 (地址, 详细地址, 住址, Address) → party_address (甲单地址)');
console.log('- 诉求描述 (诉求描述, 工单内容, 描述, 内容, Description) → party_appeal_description (甲单诉求描述)');
console.log('- 备注 (备注, 说明, Remarks, Note) → party_remarks (甲单备注)');
console.log('- 费用合计金额 (费用合计金额, 合计金额, 总金额, 金额, Amount) → party_total_amount (甲单费用合计金额)');
console.log('');
console.log('重要说明：Excel中的数据直接映射到甲单字段，基础字段（address、appeal_description、total_amount、remarks）会被设置为空或默认值。');

// 注意：这个测试脚本需要实际的Excel文件才能运行
// 请确保在 test-data 目录下有 sample-orders.xlsx 文件