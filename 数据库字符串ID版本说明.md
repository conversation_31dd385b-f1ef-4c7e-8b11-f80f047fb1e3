# 数据库字符串ID版本说明

## 概述

本版本将数据库中的所有ID字段从整数类型改为字符串类型，使用UUID格式的字符串ID，提供更好的扩展性和唯一性保证。

## 主要变更

### 1. ID字段类型变更
- **原类型**: `INTEGER PRIMARY KEY AUTOINCREMENT`
- **新类型**: `VARCHAR(36) PRIMARY KEY`
- **格式**: 前缀 + UUID（如：`material-12345678`）

### 2. ID生成规则
- **用户ID**: `user-` + 8位数字（如：`user-00000001`）
- **物料ID**: `material-` + 8位数字（如：`material-00000001`）
- **工种ID**: `wt-` + 8位数字（如：`wt-00000001`）
- **订单ID**: `order-` + 8位数字（如：`order-00000001`）
- **项目ID**: `project-` + 8位数字（如：`project-00000001`）

### 3. 涉及的表
- `users` - 用户表
- `user_sessions` - 用户会话表
- `materials` - 物料表
- `material_records` - 物料记录表
- `products` - 产品表
- `product_records` - 产品记录表
- `loose_orders` - 散户订单表
- `projects` - 工程订单表
- `safety_inspection_orders` - 安检订单表
- `work_types` - 工种表
- `employees` - 员工表
- `system_logs` - 系统日志表
- `data_dictionary` - 数据字典表
- `settlements` - 结算表

## 文件说明

### 1. 数据库建表语句
- **原文件**: `数据库建表语句.sql` - 整数ID版本
- **新文件**: `数据库建表语句_字符串ID.sql` - 字符串ID版本

### 2. 迁移脚本
- **文件**: `数据库迁移_整数ID转字符串ID.sql` - 数据迁移脚本

### 3. 工具文件
- **文件**: `suijian-backend/src/utils/uuid.js` - UUID生成工具

## 使用方法

### 1. 新建数据库
如果是从零开始，直接使用字符串ID版本的建表语句：

```sql
-- 执行字符串ID版本的建表语句
source 数据库建表语句_字符串ID.sql;
```

### 2. 迁移现有数据库
如果已有整数ID版本的数据库，需要执行迁移：

```sql
-- 1. 备份现有数据库
-- 2. 执行迁移脚本
source 数据库迁移_整数ID转字符串ID.sql;
```

### 3. 后端代码修改
后端代码已经更新以支持字符串ID：

- 修改了物料控制器 (`materialsController.js`)
- 更新了验证规则 (`validator.js`)
- 添加了UUID生成工具 (`uuid.js`)

### 4. 前端代码
前端代码无需修改，因为ID字段在前端都是作为字符串处理的。

## 优势

### 1. 更好的扩展性
- 支持分布式系统
- 避免ID冲突
- 便于数据分片

### 2. 更强的唯一性
- UUID保证全局唯一
- 减少ID重复风险
- 支持多环境部署

### 3. 更好的安全性
- ID不可预测
- 减少信息泄露风险
- 提高系统安全性

## 注意事项

### 1. 性能考虑
- 字符串ID比整数ID占用更多存储空间
- 字符串比较比整数比较稍慢
- 建议在ID字段上创建索引

### 2. 兼容性
- 确保所有外键引用都已更新
- 检查所有API接口的ID参数处理
- 验证前端代码的ID处理逻辑

### 3. 数据完整性
- 迁移前务必备份数据库
- 迁移后验证数据完整性
- 检查外键约束是否正确

## 验证步骤

### 1. 检查ID格式
```sql
-- 检查用户ID格式
SELECT id FROM users WHERE id NOT LIKE 'user-%';

-- 检查物料ID格式
SELECT id FROM materials WHERE id NOT LIKE 'material-%';

-- 检查工种ID格式
SELECT id FROM work_types WHERE id NOT LIKE 'wt-%';
```

### 2. 检查数据完整性
```sql
-- 检查记录数量
SELECT 'users' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'materials' as table_name, COUNT(*) as count FROM materials
UNION ALL
SELECT 'work_types' as table_name, COUNT(*) as count FROM work_types;
```

### 3. 检查外键约束
```sql
-- 检查外键引用
SELECT COUNT(*) as orphaned_records 
FROM material_records mr 
LEFT JOIN materials m ON mr.material_id = m.id 
WHERE m.id IS NULL;
```

## 回滚方案

如果需要在迁移后回滚到整数ID版本：

1. 恢复备份的数据库
2. 或者执行反向迁移脚本（需要单独编写）

## 技术支持

如有问题，请检查：
1. 数据库日志
2. 应用日志
3. 外键约束错误
4. API响应错误 