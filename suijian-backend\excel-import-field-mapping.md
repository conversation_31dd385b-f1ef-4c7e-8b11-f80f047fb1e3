# Excel导入字段映射完整说明

## 概述
Excel导入功能将Excel中的字段直接映射到甲单字段。Excel中的数据就是甲单数据。

## 字段映射表

| Excel字段名称 | 数据库字段名 | 字段类型 | 是否必填 | 说明 | 默认值 |
|---------------|-------------|----------|----------|------|--------|
| 工单号 | `order_no` | VARCHAR(50) | ✅ 必填 | 订单编号，唯一标识 | 自动生成 |
| 用户编号 | `customer_code` | VARCHAR(50) | ❌ 可选 | 客户编码 | '' |
| 用户姓名 | `customer_name` | VARCHAR(50) | ✅ 必填 | 客户姓名 | '' |
| 移动电话 | `phone` | VARCHAR(20) | ❌ 可选 | 联系电话 | '' |
| 地址 | `party_address` | TEXT | ❌ 可选 | **甲单地址信息** | '' |
| 诉求描述 | `party_appeal_description` | TEXT | ❌ 可选 | **甲单诉求描述** | '' |
| 备注 | `party_remarks` | TEXT | ❌ 可选 | **甲单备注信息** | '' |
| 费用合计金额 | `party_total_amount` | DECIMAL(10,2) | ❌ 可选 | **甲单费用合计金额** | 0.00 |

## 系统自动填充字段

| 字段名 | 说明 | 值 |
|--------|------|-----|
| `id` | 订单ID | UUID自动生成 |
| `community_name` | 小区名称 | '' |
| `building` | 楼栋号 | '' |
| `room_number` | 房间号 | '' |
| `contact_person` | 联系人 | '' |
| `order_type` | 订单类型 | '普通工单' |
| `project_name` | 项目名称 | '' |
| `address` | 地址信息 | '' (Excel地址映射到甲单地址) |
| `appeal_description` | 诉求描述 | '' (Excel诉求描述映射到甲单诉求描述) |
| `total_amount` | 费用合计金额 | 0.00 (Excel费用合计金额映射到甲单费用合计金额) |
| `remarks` | 备注信息 | '' (Excel备注映射到甲单备注) |
| `batch_id` | 批次ID | 导入时生成的UUID |
| `status` | 订单状态 | 'pending' |
| `created_at` | 创建时间 | 当前时间 |
| `updated_at` | 更新时间 | 当前时间 |

## 数据库插入SQL

```sql
INSERT INTO loose_orders (
    id, order_no, customer_name, customer_code, community_name,
    building, room_number, phone, contact_person, order_type,
    project_name, address, appeal_description, total_amount,
    batch_id, party_address, party_appeal_description, party_total_amount, party_remarks,
    status, remarks, created_at, updated_at
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
```

## 参数顺序

```javascript
const insertParams = [
    id,                    // id - UUID自动生成
    orderNo,               // order_no - 工单号
    customerName,          // customer_name - 用户姓名
    customerCode,          // customer_code - 用户编号
    '',                    // community_name - 小区名称（空）
    '',                    // building - 楼栋号（空）
    '',                    // room_number - 房间号（空）
    phone,                 // phone - 移动电话
    '',                    // contact_person - 联系人（空）
    orderType,             // order_type - 订单类型（'普通工单'）
    '',                    // project_name - 项目名称（空）
    address,               // address - 地址（空，Excel地址映射到甲单地址）
    appealDescription,     // appeal_description - 诉求描述（空，Excel诉求描述映射到甲单诉求描述）
    totalAmount,           // total_amount - 费用合计金额（0，Excel费用合计金额映射到甲单费用合计金额）
    batchId,               // batch_id - 批次ID
    partyAddress,          // party_address - 甲单地址（来自Excel地址字段）
    partyAppealDescription, // party_appeal_description - 甲单诉求描述（来自Excel诉求描述字段）
    partyTotalAmount,      // party_total_amount - 甲单费用合计金额（来自Excel费用合计金额字段）
    partyRemarks,          // party_remarks - 甲单备注（来自Excel备注字段）
    status,                // status - 订单状态（'pending'）
    remarks                // remarks - 备注（空，Excel备注映射到甲单备注）
];
```

## 支持的Excel字段名称变体

### 基础字段
- **工单号**: 工单号、订单号、编号、ID、序号
- **用户编号**: 用户编号、客户编号、客户编码、编号、Code
- **用户姓名**: 用户姓名、客户姓名、姓名、客户名称、Name
- **移动电话**: 移动电话、手机号码、联系电话、电话、Phone
- **地址**: 地址、详细地址、住址、Address → **映射到甲单地址**
- **诉求描述**: 诉求描述、工单内容、描述、内容、Description → **映射到甲单诉求描述**
- **备注**: 备注、说明、Remarks、Note → **映射到甲单备注**
- **费用合计金额**: 费用合计金额、合计金额、总金额、金额、Amount → **映射到甲单费用合计金额**

## 数据验证规则

1. **工单号**: 必须唯一，重复的工单号会被跳过
2. **用户姓名**: 必填字段，不能为空
3. **费用金额**: 自动转换为数字格式，非数字会被转换为0
4. **字段长度**: 按照数据库字段类型限制

## 错误处理

- 缺少工单号列：返回错误
- 工单号重复：跳过并记录
- 数据格式错误：记录在错误日志中
- 数据库连接失败：返回错误信息

## 示例

### Excel文件格式
| 工单号 | 用户姓名 | 用户编号 | 移动电话 | 地址 | 诉求描述 | 备注 | 费用合计金额 |
|--------|----------|----------|----------|------|----------|------|--------------|
| WO001  | 张三     | C001     | 13800138000 | 北京市朝阳区XX小区 | 燃气管道维修 | 紧急处理 | 150.00 |

### 数据库记录
```sql
INSERT INTO loose_orders (
    id, order_no, customer_name, customer_code, phone, 
    party_address, party_appeal_description, party_remarks, party_total_amount, 
    order_type, status
) VALUES (
    'uuid-123', 'WO001', '张三', 'C001', '13800138000',
    '北京市朝阳区XX小区', '燃气管道维修', '紧急处理', 150.00,
    '普通工单', 'pending'
);
```

## 重要说明

- **Excel数据就是甲单数据**：Excel中的所有字段都直接映射到对应的甲单字段
- **基础字段为空**：由于Excel数据映射到甲单字段，基础字段（address、appeal_description、total_amount、remarks）会被设置为空或默认值
- **数据完整性**：确保Excel数据的完整性，因为数据会直接存储到甲单字段中 