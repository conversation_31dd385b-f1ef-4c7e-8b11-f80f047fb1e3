# 工程管理系统 - 后端API服务

## 项目简介

工程管理系统是一个综合性的工程管理平台，主要用于管理工程项目的物料、订单、员工、财务等各个方面。系统采用前后端分离架构，后端提供RESTful API接口服务。

## 技术栈

- **开发语言**: Node.js 18.x
- **Web框架**: Express.js 4.x
- **认证**: JWT + bcrypt
- **API文档**: Swagger UI Express
- **文件上传**: Multer
- **日志**: Winston
- **测试**: Jest + Supertest

## 项目结构

```
suijian-backend/
├── src/                          # 源代码目录
│   ├── app.js                    # 应用入口文件
│   ├── config/                   # 配置文件目录
│   ├── routes/                   # 路由目录
│   ├── controllers/              # 控制器目录
│   ├── middleware/               # 中间件目录
│   ├── services/                 # 业务逻辑服务目录
│   ├── utils/                    # 工具函数目录
│   └── uploads/                  # 文件上传目录
├── tests/                        # 测试文件目录
├── docs/                         # 文档目录
├── scripts/                      # 脚本目录
├── package.json                  # 项目依赖配置
├── env.example                   # 环境配置示例
└── README.md                     # 项目说明文档
```

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 环境配置

复制环境配置文件：

```bash
cp env.example .env
```

根据实际情况修改 `.env` 文件中的配置项。

### 3. 启动开发服务器

```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

### 4. 运行测试

```bash
# 运行所有测试
npm test

# 监听模式
npm run test:watch

# 生成覆盖率报告
npm run test:coverage
```

## API接口

### 认证相关
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/profile` - 获取用户信息

### 仪表板相关
- `GET /api/dashboard/materials/unused` - 获取未使用物料统计
- `GET /api/dashboard/materials/used` - 获取已使用物料统计
- `GET /api/dashboard/materials/issued` - 获取已领出物料统计
- `GET /api/dashboard/projects/status` - 获取工程进度统计
- `GET /api/dashboard/loose-materials` - 获取散单物料统计

- `GET /api/dashboard/sales/statistics` - 获取销售统计
- `GET /api/dashboard/loose-materials/stats` - 获取散户物料统计

## 环境配置

### 开发环境
```bash
NODE_ENV=development
PORT=3000
```

### 生产环境
```bash
NODE_ENV=production
PORT=3000
```

## 部署

### 使用PM2部署

```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start ecosystem.config.js

# 查看状态
pm2 status

# 重启应用
pm2 restart suijian-backend
```

### 使用Docker部署

```bash
# 构建镜像
docker build -t suijian-backend .

# 运行容器
docker run -p 3000:3000 suijian-backend
```

## 开发规范

### 代码规范
- 使用ESLint进行代码检查
- 使用Prettier进行代码格式化
- 遵循RESTful API设计规范

### 提交规范
- 使用语义化的提交信息
- 每次提交前运行测试
- 确保代码通过ESLint检查

### 分支管理
- `main`: 主分支，用于生产环境
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

## 测试

### 单元测试
```bash
npm test
```

### 集成测试
```bash
npm run test:integration
```

### 测试覆盖率
```bash
npm run test:coverage
```

## 日志

系统使用Winston进行日志管理，日志文件位于 `logs/` 目录：

- `combined.log`: 所有日志
- `error.log`: 错误日志
- `requests.log`: 请求日志

## 监控

### 健康检查
```bash
curl http://localhost:3000/health
```

## 默认用户

系统预置了以下默认用户：

- **管理员**: username: `admin`, password: `password`
- **经理**: username: `manager`, password: `password`
- **员工**: username: `worker`, password: `password`

## 许可证

MIT License