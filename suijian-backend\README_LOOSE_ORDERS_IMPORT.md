# 工单列表Excel导入功能说明

## 功能概述

本功能实现了工单列表界面的Excel导入功能，支持从Excel文件中批量导入工单数据到`loose_orders`表中。

## 主要特性

1. **Excel文件导入**：支持`.xlsx`和`.xls`格式的Excel文件
2. **重复数据跳过**：根据工单号自动跳过已存在的数据
3. **字段映射**：将Excel列映射到数据库字段
4. **地址构建**：自动组合多个字段构建地址信息
5. **批次管理**：为每次导入生成唯一的批次ID
6. **错误处理**：详细的错误报告和日志记录

## 数据库字段扩展

在原有的`loose_orders`表基础上，新增了以下字段：

- `address` (TEXT): 地址信息
- `appeal_description` (TEXT): 诉求描述
- `total_amount` (DECIMAL(10,2)): 费用合计金额
- `batch_id` (VARCHAR(36)): 批次ID

## 安装和配置

### 1. 安装依赖

```bash
cd suijian-backend
npm install
```

### 2. 更新数据库

执行数据库更新脚本：

```bash
# 如果使用SQLite
sqlite3 database.sqlite < database/update_loose_orders.sql

# 如果使用MySQL
mysql -u username -p database_name < database/update_loose_orders.sql
```

### 3. 启动服务

```bash
npm run dev
```

## API接口

### 1. 获取工单列表

```
GET /api/loose-orders
```

**查询参数：**
- `page`: 页码（默认1）
- `pageSize`: 每页数量（默认20）
- `orderNo`: 订单号（模糊查询）
- `customerName`: 客户姓名（模糊查询）
- `address`: 地址（模糊查询）
- `phone`: 电话（模糊查询）
- `contactPerson`: 联系人（模糊查询）
- `orderType`: 订单类型
- `status`: 订单状态
- `assignedWorker`: 指派工人

### 2. 创建工单

```
POST /api/loose-orders
```

**请求体：**
```json
{
  "orderNo": "工单号",
  "customerName": "客户姓名",
  "customerCode": "客户编码",
  "communityName": "小区名称",
  "building": "楼栋号",
  "roomNumber": "房间号",
  "phone": "联系电话",
  "contactPerson": "联系人",
  "orderType": "订单类型",
  "projectName": "项目名称",
  "address": "地址",
  "appealDescription": "诉求描述",
  "totalAmount": 100.00,
  "batchId": "批次ID",
  "remarks": "备注"
}
```

### 3. 更新工单

```
PUT /api/loose-orders/:id
```

### 4. 删除工单

```
DELETE /api/loose-orders/:id
```

### 5. 获取工单详情

```
GET /api/loose-orders/:id
```

### 6. 导入Excel文件

```
POST /api/loose-orders/import
```

**请求体：** `multipart/form-data`
- `file`: Excel文件

**响应示例：**
```json
{
  "success": true,
  "message": "Excel导入完成: 成功10条，跳过2条，失败0条",
  "data": {
    "total": 12,
    "success": 10,
    "skip": 2,
    "error": 0,
    "errors": [],
    "batchId": "uuid-string"
  }
}
```

### 7. 订单派发

```
POST /api/loose-orders/:id/assign
```

**请求体：**
```json
{
  "assignedWorkerId": "工人ID",
  "estimatedDays": 3,
  "startDate": "2024-01-15",
  "remarks": "派发备注"
}
```

## 前端使用

### 1. 导入Excel按钮

在工单列表页面，点击"导入Excel"按钮即可选择Excel文件进行导入。

### 2. 文件要求

- 文件格式：`.xlsx` 或 `.xls`
- 文件大小：不超过200MB
- 编码格式：UTF-8

### 3. 导入结果

导入完成后会显示导入结果：
- 成功导入的记录数
- 跳过的记录数（工单号重复）
- 失败的记录数
- 错误详情（如果有）

## Excel模板

详细的Excel模板说明请参考：`docs/excel_template_loose_orders.md`

### 必要列

Excel文件必须包含以下列：
- 工单号
- 工单类型
- 工单状态
- 工单来源
- 工单标题
- 工单内容
- 创建人
- 创建时间

### 可选列

- 处理人
- 处理时间
- 完成时间
- 所属部门
- 所属区域
- 所属公司
- 所属项目
- 所属设备
- 所属系统
- 所属模块
- 所属功能
- 所属页面
- 所属接口
- 所属数据
- 所属文件
- 所属图片
- 所属视频
- 所属音频
- 所属其他
- 备注

## 测试

运行测试脚本：

```bash
node test-loose-orders-import.js
```

## 错误处理

### 常见错误

1. **文件格式错误**：确保上传的是Excel文件
2. **文件大小超限**：文件不能超过200MB
3. **列名不匹配**：Excel列名必须与模板完全一致
4. **数据格式错误**：日期时间格式建议使用 `YYYY-MM-DD HH:MM:SS`
5. **编码问题**：确保Excel文件使用UTF-8编码

### 错误日志

错误信息会记录在日志文件中，可以通过以下方式查看：

```bash
tail -f logs/app.log
```

## 性能优化

1. **批量处理**：建议单次导入不超过1000条记录
2. **索引优化**：已为关键字段创建索引
3. **事务处理**：导入过程使用数据库事务确保数据一致性
4. **内存管理**：大文件导入时使用流式处理

## 安全考虑

1. **文件验证**：严格验证文件类型和大小
2. **SQL注入防护**：使用参数化查询
3. **权限控制**：需要认证才能访问API
4. **数据验证**：对导入数据进行格式验证

## 维护和监控

1. **日志监控**：监控导入操作的日志
2. **性能监控**：监控导入操作的性能指标
3. **数据备份**：定期备份数据库
4. **错误报告**：及时处理导入错误

## 联系支持

如有问题，请联系开发团队或查看相关文档。 