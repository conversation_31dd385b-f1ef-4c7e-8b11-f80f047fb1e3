const { execute, query } = require('./src/config/database');
const { v4: uuidv4 } = require('uuid');

async function testBatchInsert() {
    console.log('测试批量插入功能...');

    try {
        // 生成测试数据
        const testData = [];
        const batchId = uuidv4();
        const now = new Date().toISOString();

        console.log('1. 生成测试数据...');
        for (let i = 1; i <= 10; i++) {
            const id = uuidv4();
            const orderNo = `BATCH-TEST-${Date.now()}-${i}`;

            testData.push([
                id, // id
                orderNo, // order_no
                `测试用户${i}`, // customer_name
                `TEST${i.toString().padStart(3, '0')}`, // customer_code
                '', // community_name
                '', // building
                '', // room_number
                `1380013800${i.toString().padStart(1, '0')}`, // phone
                '', // contact_person
                '普通工单', // order_type
                '', // project_name
                batchId, // batch_id
                `测试地址${i}`, // party_address
                `测试诉求${i}`, // party_appeal_description
                100.00 + i, // party_total_amount
                `测试备注${i}`, // party_remarks
                'pending', // status
                now, // created_at
                now // updated_at
            ]);
        }

        console.log(`✓ 生成了 ${testData.length} 条测试数据`);

        // 测试批量插入
        console.log('2. 测试批量插入...');
        const sql = `
            INSERT INTO loose_orders (
                id, order_no, customer_name, customer_code, community_name,
                building, room_number, phone, contact_person, order_type,
                project_name, batch_id, party_address, party_appeal_description, party_total_amount, party_remarks,
                status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        // 使用事务进行批量插入
        await execute('BEGIN TRANSACTION');

        for (let i = 0; i < testData.length; i++) {
            const insertParams = testData[i];
            await execute(sql, insertParams);
            console.log(`✓ 插入第 ${i + 1} 条数据: ${insertParams[1]}`);
        }

        await execute('COMMIT');
        console.log('✓ 批量插入完成');

        // 验证插入结果
        console.log('3. 验证插入结果...');
        const result = await query('SELECT * FROM loose_orders WHERE batch_id = ?', [batchId]);
        console.log(`✓ 查询到 ${result.length} 条记录`);

        if (result.length > 0) {
            console.log('记录示例:', {
                id: result[0].id,
                order_no: result[0].order_no,
                customer_name: result[0].customer_name,
                party_address: result[0].party_address,
                created_at: result[0].created_at
            });
        }

        // 清理测试数据
        console.log('4. 清理测试数据...');
        await execute('DELETE FROM loose_orders WHERE batch_id = ?', [batchId]);
        console.log('✓ 测试数据清理完成');

        console.log('\n🎉 批量插入测试通过！');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('错误详情:', error);

        // 尝试回滚事务
        try {
            await execute('ROLLBACK');
            console.log('✓ 事务已回滚');
        } catch (rollbackError) {
            console.error('回滚失败:', rollbackError.message);
        }
    }
}

// 运行测试
testBatchInsert().then(() => {
    console.log('测试完成');
    process.exit(0);
}).catch(error => {
    console.error('测试异常:', error);
    process.exit(1);
});