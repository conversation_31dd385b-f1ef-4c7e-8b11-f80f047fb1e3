#!/usr/bin/env node

/**
 * 不需要认证的文件上传测试脚本
 */

const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 创建测试服务器
const app = express();
const PORT = 3001;

// 配置multer
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path.join(__dirname, '../uploads/test');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'test-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 200 * 1024 * 1024, // 200MB
        files: 1
    }
});

// 添加请求日志
app.use((req, res, next) => {
    console.log(`📥 ${req.method} ${req.url}`);
    console.log('Content-Type:', req.headers['content-type']);
    console.log('Content-Length:', req.headers['content-length']);
    next();
});

// 测试上传端点（不需要认证）
app.post('/test-upload', upload.single('file'), (req, res) => {
    console.log('\n=== 文件上传详情 ===');
    console.log('req.file:', req.file);
    console.log('req.body:', req.body);

    if (req.file) {
        console.log('✅ 文件上传成功:');
        console.log('- 原始文件名:', req.file.originalname);
        console.log('- 存储文件名:', req.file.filename);
        console.log('- 文件大小:', req.file.size);
        console.log('- 文件路径:', req.file.path);
        console.log('- MIME类型:', req.file.mimetype);

        res.json({
            success: true,
            message: '文件上传成功',
            file: {
                originalname: req.file.originalname,
                filename: req.file.filename,
                size: req.file.size,
                path: req.file.path,
                mimetype: req.file.mimetype
            }
        });
    } else {
        console.log('❌ 文件上传失败: req.file 为 null');
        res.status(400).json({
            success: false,
            message: '文件上传失败',
            error: 'req.file 为 null'
        });
    }
});

// 健康检查
app.get('/health', (req, res) => {
    res.json({
        success: true,
        message: '测试服务器运行正常',
        timestamp: new Date().toISOString()
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🧪 测试服务器运行在端口 ${PORT}`);
    console.log(`📝 上传测试端点: http://localhost:${PORT}/test-upload`);
    console.log(`🏥 健康检查端点: http://localhost:${PORT}/health`);
    console.log('\n💡 现在可以使用以下命令测试:');
    console.log('curl -X POST -F "file=@uploads/test_simple.xls" http://localhost:3001/test-upload');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭测试服务器...');
    process.exit(0);
});