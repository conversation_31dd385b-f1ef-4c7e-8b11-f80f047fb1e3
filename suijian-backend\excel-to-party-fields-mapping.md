# Excel字段映射到甲单字段说明

## 概述
Excel导入功能将Excel中的字段直接映射到甲单字段。Excel中的数据就是甲单数据，基础字段会被设置为空或默认值。

## 字段映射关系

### 直接映射字段
| Excel字段 | 数据库字段 | 说明 |
|-----------|------------|------|
| 工单号 | `order_no` | 订单编号，唯一标识 |
| 用户编号 | `customer_code` | 客户编码 |
| 用户姓名 | `customer_name` | 客户姓名 |
| 移动电话 | `phone` | 联系电话 |

### Excel字段映射到甲单字段
| Excel字段 | 数据库字段 | 说明 |
|-----------|------------|------|
| 地址 | `party_address` | Excel地址 → 甲单地址 |
| 诉求描述 | `party_appeal_description` | Excel诉求描述 → 甲单诉求描述 |
| 备注 | `party_remarks` | Excel备注 → 甲单备注 |
| 费用合计金额 | `party_total_amount` | Excel费用合计金额 → 甲单费用合计金额 |

### 系统自动填充字段
| 字段名 | 值 | 说明 |
|--------|-----|------|
| `address` | '' | 空值，因为Excel地址映射到甲单地址 |
| `appeal_description` | '' | 空值，因为Excel诉求描述映射到甲单诉求描述 |
| `total_amount` | 0.00 | 0值，因为Excel费用合计金额映射到甲单费用合计金额 |
| `remarks` | '' | 空值，因为Excel备注映射到甲单备注 |

## 导入逻辑

1. **字段识别**：系统自动识别Excel中的字段名称
2. **数据提取**：从Excel中提取对应字段的数据
3. **字段映射**：将Excel字段映射到对应的甲单字段
4. **默认值设置**：基础字段设置为空或默认值
5. **数据库插入**：将数据插入到数据库

## 示例

### Excel文件
| 工单号 | 用户姓名 | 用户编号 | 移动电话 | 地址 | 诉求描述 | 备注 | 费用合计金额 |
|--------|----------|----------|----------|------|----------|------|--------------|
| WO001  | 张三     | C001     | 13800138000 | 北京市朝阳区XX小区 | 燃气管道维修 | 紧急处理 | 150.00 |

### 数据库记录
```sql
INSERT INTO loose_orders (
    id, order_no, customer_name, customer_code, phone, 
    party_address, party_appeal_description, party_remarks, party_total_amount, 
    order_type, status
) VALUES (
    'uuid-123', 'WO001', '张三', 'C001', '13800138000',
    '北京市朝阳区XX小区', '燃气管道维修', '紧急处理', 150.00,
    '普通工单', 'pending'
);
```

## 重要说明

1. **Excel数据就是甲单数据**：Excel中的所有字段都直接映射到对应的甲单字段
2. **基础字段为空**：由于Excel数据映射到甲单字段，基础字段会被设置为空或默认值
3. **数据完整性**：确保Excel数据的完整性，因为数据会直接存储到甲单字段中
4. **字段验证**：系统会验证必填字段（工单号、用户姓名）是否存在
5. **错误处理**：重复工单号会被跳过，数据格式错误会记录在日志中

## 支持的字段名称变体

- **工单号**: 工单号、订单号、编号、ID、序号
- **用户编号**: 用户编号、客户编号、客户编码、编号、Code
- **用户姓名**: 用户姓名、客户姓名、姓名、客户名称、Name
- **移动电话**: 移动电话、手机号码、联系电话、电话、Phone
- **地址**: 地址、详细地址、住址、Address
- **诉求描述**: 诉求描述、工单内容、描述、内容、Description
- **备注**: 备注、说明、Remarks、Note
- **费用合计金额**: 费用合计金额、合计金额、总金额、金额、Amount 