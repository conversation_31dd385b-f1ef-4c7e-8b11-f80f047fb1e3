const request = require('supertest');
const app = require('../../src/app');

describe('App', () => {
    describe('GET /health', () => {
        it('should return health status', async() => {
            const response = await request(app)
                .get('/health')
                .expect(200);

            expect(response.body.success).toBe(true);
            expect(response.body.message).toBe('服务运行正常');
            expect(response.body).toHaveProperty('timestamp');
            expect(response.body).toHaveProperty('version');
        });
    });

    describe('GET /api', () => {
        it('should return API info', async() => {
            const response = await request(app)
                .get('/api')
                .expect(200);

            expect(response.body.success).toBe(true);
            expect(response.body.message).toBe('工程管理系统API服务');
            expect(response.body).toHaveProperty('version');
            expect(response.body).toHaveProperty('timestamp');
        });
    });

    describe('GET /nonexistent', () => {
        it('should return 404 for non-existent routes', async() => {
            const response = await request(app)
                .get('/nonexistent')
                .expect(404);

            expect(response.body.success).toBe(false);
            expect(response.body.message).toBe('接口不存在');
        });
    });
});