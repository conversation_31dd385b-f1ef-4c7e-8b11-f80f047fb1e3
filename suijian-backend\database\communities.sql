/*
 Navicat Premium Data Transfer

 Source Server         : suijian
 Source Server Type    : SQLite
 Source Server Version : 3017000
 Source Schema         : main

 Target Server Type    : SQLite
 Target Server Version : 3017000
 File Encoding         : 65001

 Date: 09/08/2025 13:48:43
*/

PRAGMA foreign_keys = false;

-- ----------------------------
-- Table structure for communities
-- ----------------------------
DROP TABLE IF EXISTS "communities";
CREATE TABLE "communities" (
  "id" VARCHAR(36),
  "name" VARCHAR(100) NOT NULL,
  "alias" VARCHAR(100),
  "district" VARCHAR(50),
  "address" TEXT,
  "building_pattern" VARCHAR(200),
  "room_pattern" VARCHAR(200),
  "status" INTEGER DEFAULT 1,
  "created_at" DATETIME DEFAULT CURRENT_TIMESTAMP,
  "updated_at" DATETIME DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id")
);

PRAGMA foreign_keys = true;
