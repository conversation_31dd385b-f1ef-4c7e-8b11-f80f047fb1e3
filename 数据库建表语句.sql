-- 工程管理系统 SQLite 数据库建表语句（字符串ID版本）
-- 创建时间: 2024年
-- 数据库版本: SQLite 3.x
-- 注意：所有ID字段使用字符串类型

-- 用户表 - 存储系统用户信息
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,                    -- 用户ID，UUID格式，主键
    username VARCHAR(50) UNIQUE NOT NULL,          -- 用户名，唯一，用于登录
    password VARCHAR(255) NOT NULL,                -- 密码，加密存储
    real_name VARCHAR(50) NOT NULL,                -- 真实姓名
    phone VARCHAR(20),                             -- 手机号码
    email VARCHAR(100),                            -- 邮箱地址
    role VARCHAR(20) NOT NULL DEFAULT 'worker',    -- 用户角色：admin-管理员，worker-工人
    status INTEGER NOT NULL DEFAULT 1,             -- 用户状态：1-启用，0-禁用
    last_login_at DATETIME,                        -- 最后登录时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

-- 用户会话表 - 存储用户登录会话信息
CREATE TABLE user_sessions (
    id VARCHAR(36) PRIMARY KEY,                    -- 会话ID，UUID格式
    user_id VARCHAR(36) NOT NULL,                  -- 用户ID，关联users表
    token VARCHAR(255) UNIQUE NOT NULL,            -- 会话令牌，用于身份验证
    expires_at DATETIME NOT NULL,                  -- 会话过期时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 会话创建时间
);

-- 物料基础信息表 - 存储物料的基本信息
CREATE TABLE materials (
    id VARCHAR(36) PRIMARY KEY,                    -- 物料ID，UUID格式
    company_code VARCHAR(50) NOT NULL,             -- 公司物料编码，唯一标识
    client_code VARCHAR(50),                       -- 甲方编码，客户提供的编码
    name VARCHAR(100) NOT NULL,                    -- 物料名称
    model VARCHAR(100),                            -- 型号规格
    specification VARCHAR(200),                    -- 详细规格说明
    unit VARCHAR(20),                              -- 计量单位（个、米、套等）
    category VARCHAR(20) NOT NULL,                 -- 物料分类：甲料、乙料、商品、辅料
    price REAL,                                    -- 单价
    stock_quantity INTEGER DEFAULT 0,              -- 当前库存数量
    warning_quantity INTEGER DEFAULT 0,            -- 库存预警数量
    location VARCHAR(100),                         -- 存放位置
    status INTEGER DEFAULT 1,                      -- 物料状态：1-正常，0-禁用
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

-- 物料出入库记录主表 - 记录物料的出入库操作
CREATE TABLE material_records (
    id VARCHAR(36) PRIMARY KEY,                    -- 记录ID，UUID格式
    type VARCHAR(20) NOT NULL,                     -- 操作类型：inbound-入库，outbound-出库，return-退库，apply-申请
    operator_id VARCHAR(36) NOT NULL,              -- 操作员ID，关联users表
    order_id VARCHAR(36),                          -- 关联订单号
    supplier VARCHAR(100),                         -- 供应商信息
    batch_number VARCHAR(50),                      -- 批次号
    recipient VARCHAR(50),                         -- 收货人/领料人
    purpose TEXT,                                  -- 用途说明（申领用途、退仓类型等）
    remarks TEXT,                                  -- 备注信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 操作时间
);

-- 物料出入库记录明细表 - 记录每次操作的具体物料明细
CREATE TABLE material_record_items (
    id VARCHAR(36) PRIMARY KEY,                    -- 明细ID，UUID格式
    record_id VARCHAR(36) NOT NULL,                -- 记录ID，关联material_records表
    material_id VARCHAR(36) NOT NULL,              -- 物料ID，关联materials表
    quantity INTEGER NOT NULL,                     -- 操作数量（正数-入库，负数-出库）
    current_quantity INTEGER NOT NULL,             -- 操作时的当前库存数量
    unit_price REAL,                               -- 单价
    total_amount REAL,                             -- 总金额
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 创建时间
);

-- 产品信息表 - 存储产品的基本信息
CREATE TABLE products (
    id VARCHAR(36) PRIMARY KEY,                    -- 产品ID，UUID格式
    product_code VARCHAR(50) UNIQUE NOT NULL,      -- 产品编码，唯一标识
    name VARCHAR(100) NOT NULL,                    -- 产品名称
    model VARCHAR(100),                            -- 产品型号
    specification VARCHAR(200),                    -- 产品规格
    unit VARCHAR(20),                              -- 计量单位
    price REAL,                                    -- 销售价格
    cost_price REAL,                               -- 成本价格
    stock_quantity INTEGER DEFAULT 0,              -- 当前库存数量
    warning_quantity INTEGER DEFAULT 0,            -- 库存预警数量
    location VARCHAR(100),                         -- 存放位置
    status INTEGER DEFAULT 1,                      -- 产品状态：1-正常，0-禁用
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

-- 产品出入库记录表 - 记录产品的出入库操作
CREATE TABLE product_records (
    id VARCHAR(36) PRIMARY KEY,                    -- 记录ID，UUID格式
    product_id VARCHAR(36) NOT NULL,               -- 产品ID，关联products表
    type VARCHAR(20) NOT NULL,                     -- 操作类型：inbound-入库，outbound-出库
    quantity INTEGER NOT NULL,                     -- 操作数量
    unit_price REAL,                               -- 单价
    total_amount REAL,                             -- 总金额
    operator_id VARCHAR(36) NOT NULL,              -- 操作员ID，关联users表
    order_id VARCHAR(36),                          -- 关联订单号
    supplier VARCHAR(100),                         -- 供应商信息
    batch_number VARCHAR(50),                      -- 批次号
    recipient VARCHAR(50),                         -- 收货人
    purpose TEXT,                                  -- 用途说明
    remarks TEXT,                                  -- 备注信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 操作时间
);

-- 散户订单表 - 存储散户的订单信息
CREATE TABLE loose_orders (
    id VARCHAR(36) PRIMARY KEY,                    -- 订单ID，UUID格式
    order_no VARCHAR(50) UNIQUE NOT NULL,          -- 订单编号，唯一标识
    customer_name VARCHAR(50) NOT NULL,            -- 客户姓名
    customer_code VARCHAR(50),                     -- 客户编码
    community_name VARCHAR(100),                   -- 小区名称
    building VARCHAR(50),                          -- 楼栋号
    room_number VARCHAR(50),                       -- 房间号
    phone VARCHAR(20),                             -- 联系电话
    contact_person VARCHAR(50),                    -- 联系人
    order_type VARCHAR(50),                        -- 订单类型：一次挂表、二次挂表、一次安装、二次安装、售后、单项工程
    project_name VARCHAR(100),                     -- 项目名称
    address TEXT,                                  -- 地址信息
    appeal_description TEXT,                       -- 诉求描述
    total_amount DECIMAL(10,2) DEFAULT 0.00,       -- 费用合计金额
    batch_id VARCHAR(36),                          -- 批次ID，用于标识同一批导入的数据
    party_address TEXT,                            -- 甲单地址
    party_appeal_description TEXT,                 -- 甲单诉求描述
    party_total_amount DECIMAL(10,2) DEFAULT 0.00, -- 甲单费用合计金额
    party_remarks TEXT,                            -- 甲单备注
    status VARCHAR(20) DEFAULT 'pending',          -- 订单状态：pending-待处理，in_progress-进行中，completed-已完成，cancelled-已取消
    installation_date DATE,                        -- 安装日期
    assigned_worker_id VARCHAR(36),                -- 指派工人ID，关联users表
    remarks TEXT,                                  -- 备注信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

-- 工程订单表 - 存储工程项目的订单信息
CREATE TABLE projects (
    id VARCHAR(36) PRIMARY KEY,                    -- 项目ID，UUID格式
    project_no VARCHAR(50) UNIQUE NOT NULL,        -- 项目编号，唯一标识
    project_name VARCHAR(100) NOT NULL,            -- 项目名称
    project_address TEXT,                          -- 项目地址
    status VARCHAR(20) DEFAULT 'not_started',      -- 项目状态：not_started-未开始，in_progress-进行中，completed-已完成，paused-暂停
    estimated_start_date DATE,                     -- 预计开始日期
    estimated_end_date DATE,                       -- 预计结束日期
    actual_start_date DATE,                        -- 实际开始日期
    actual_end_date DATE,                          -- 实际结束日期
    contract_amount REAL,                          -- 合同金额
    project_manager_id VARCHAR(36),                -- 项目经理ID，关联users表
    remarks TEXT,                                  -- 备注信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

-- 安检订单表 - 存储安全检查订单信息
CREATE TABLE safety_inspection_orders (
    id VARCHAR(36) PRIMARY KEY,                    -- 安检订单ID，UUID格式
    order_no VARCHAR(50) UNIQUE NOT NULL,          -- 安检订单编号，唯一标识
    customer_name VARCHAR(50) NOT NULL,            -- 客户姓名
    customer_code VARCHAR(50),                     -- 客户编码
    community_name VARCHAR(100),                   -- 小区名称
    building VARCHAR(50),                          -- 楼栋号
    room_number VARCHAR(50),                       -- 房间号
    phone VARCHAR(20),                             -- 联系电话
    contact_person VARCHAR(50),                    -- 联系人
    inspection_type VARCHAR(50),                   -- 安检类型
    status VARCHAR(20) DEFAULT 'pending',          -- 安检状态：pending-待处理，in_progress-进行中，completed-已完成
    inspection_date DATE,                          -- 安检日期
    assigned_inspector_id VARCHAR(36),             -- 指派安检员ID，关联users表
    inspection_result TEXT,                        -- 安检结果
    remarks TEXT,                                  -- 备注信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

-- 工种表 - 存储工种信息
CREATE TABLE work_types (
    id VARCHAR(36) PRIMARY KEY,                    -- 工种ID，UUID格式
    name VARCHAR(50) NOT NULL,                     -- 工种名称
    description TEXT,                              -- 工种描述
    base_price REAL,                               -- 基础工价
    status INTEGER DEFAULT 1,                      -- 工种状态：1-启用，0-禁用
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

-- 员工表 - 存储员工信息
CREATE TABLE employees (
    id VARCHAR(36) PRIMARY KEY,                    -- 员工ID，UUID格式
    name VARCHAR(50) NOT NULL,                     -- 员工姓名
    phone VARCHAR(20),                             -- 联系电话
    id_card VARCHAR(18),                           -- 身份证号
    work_type_id VARCHAR(36),                      -- 工种ID，关联work_types表
    base_salary REAL,                              -- 基础工资
    performance_rate REAL DEFAULT 1.0,             -- 绩效系数
    status INTEGER DEFAULT 1,                      -- 员工状态：1-在职，0-离职
    entry_date DATE,                               -- 入职日期
    leave_date DATE,                               -- 离职日期
    remarks TEXT,                                  -- 备注信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

-- 系统日志表 - 记录系统操作日志
CREATE TABLE system_logs (
    id VARCHAR(36) PRIMARY KEY,                    -- 日志ID，UUID格式
    user_id VARCHAR(36),                           -- 操作用户ID，关联users表
    action VARCHAR(100) NOT NULL,                  -- 操作类型
    target_type VARCHAR(50),                       -- 操作对象类型
    target_id VARCHAR(36),                         -- 操作对象ID
    details TEXT,                                  -- 操作详情
    ip_address VARCHAR(45),                        -- IP地址
    user_agent TEXT,                               -- 用户代理信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 操作时间
);

-- 数据字典表 - 存储系统配置数据
CREATE TABLE data_dictionary (
    id VARCHAR(36) PRIMARY KEY,                    -- 字典项ID，UUID格式
    dict_type VARCHAR(50) NOT NULL,                -- 字典类型
    dict_key VARCHAR(50) NOT NULL,                 -- 字典键
    dict_value VARCHAR(100) NOT NULL,              -- 字典值
    sort_order INTEGER DEFAULT 0,                  -- 排序顺序
    status INTEGER DEFAULT 1,                      -- 状态：1-启用，0-禁用
    remarks TEXT,                                  -- 备注信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

-- 结算表 - 存储员工工资结算信息
CREATE TABLE settlements (
    id VARCHAR(36) PRIMARY KEY,                    -- 结算ID，UUID格式
    settlement_no VARCHAR(50) UNIQUE NOT NULL,     -- 结算单号，唯一标识
    worker_id VARCHAR(36) NOT NULL,                -- 工人ID，关联users表
    settlement_type VARCHAR(20) NOT NULL,          -- 结算类型：monthly-月度结算，project-项目结算
    settlement_period VARCHAR(20) NOT NULL,        -- 结算期间
    start_date DATE NOT NULL,                      -- 开始日期
    end_date DATE NOT NULL,                        -- 结束日期
    total_orders INTEGER DEFAULT 0,                -- 总订单数
    total_amount REAL DEFAULT 0,                   -- 总金额
    base_amount REAL DEFAULT 0,                    -- 基础金额
    bonus_amount REAL DEFAULT 0,                   -- 奖金金额
    deduction_amount REAL DEFAULT 0,               -- 扣款金额
    final_amount REAL DEFAULT 0,                   -- 最终金额
    status VARCHAR(20) DEFAULT 'pending',          -- 结算状态：pending-待结算，completed-已结算，paid-已发放
    settlement_date DATE,                          -- 结算日期
    payment_date DATE,                             -- 发放日期
    remarks TEXT,                                  -- 备注信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

-- 小区表 - 存储小区信息，用于地址解析
CREATE TABLE communities (
    id VARCHAR(36) PRIMARY KEY,                    -- 小区ID，UUID格式
    name VARCHAR(100) NOT NULL,                    -- 小区名称
    alias VARCHAR(200),                            -- 小区别名（多个别名用逗号分隔）
    district VARCHAR(50),                          -- 所属区域
    city VARCHAR(50),                              -- 所属城市
    province VARCHAR(50),                          -- 所属省份
    address TEXT,                                  -- 小区详细地址
    status INTEGER DEFAULT 1,                      -- 小区状态：1-启用，0-禁用
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

-- 小区表索引
CREATE INDEX idx_communities_name ON communities(name);           -- 小区名称索引
CREATE INDEX idx_communities_alias ON communities(alias(100));   -- 小区别名索引
CREATE INDEX idx_communities_district ON communities(district);   -- 区域索引
CREATE INDEX idx_communities_status ON communities(status);       -- 状态索引

-- 小区表更新时间触发器
CREATE TRIGGER update_communities_updated_at 
    AFTER UPDATE ON communities
    FOR EACH ROW
    BEGIN
        UPDATE communities SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- 插入一些常见的小区数据作为示例
INSERT INTO communities (id, name, alias, district, city, province, address, status) VALUES 
('community-001', '阳光花园', '阳光花园,阳光小区,阳光花苑', '清城区', '清远市', '广东省', '广东省清远市清城区阳光花园', 1),
('community-002', '碧桂园', '碧桂园,碧桂园小区,碧桂园花园', '清城区', '清远市', '广东省', '广东省清远市清城区碧桂园', 1),
('community-003', '恒大城', '恒大城,恒大小区,恒大花园', '清新区', '清远市', '广东省', '广东省清远市清新区恒大城', 1),
('community-004', '保利城', '保利城,保利小区,保利花园', '清城区', '清远市', '广东省', '广东省清远市清城区保利城', 1),
('community-005', '万科城', '万科城,万科小区,万科花园', '清新区', '清远市', '广东省', '广东省清远市清新区万科城', 1);

-- ==========================================
-- 7. 索引创建 - 提高查询性能
-- ==========================================

-- 用户表索引
CREATE INDEX idx_users_username ON users(username);                    -- 用户名索引，用于登录查询
CREATE INDEX idx_users_role ON users(role);                           -- 用户角色索引，用于权限查询
CREATE INDEX idx_users_status ON users(status);                       -- 用户状态索引，用于筛选有效用户

-- 物料表索引
CREATE INDEX idx_materials_company_code ON materials(company_code);   -- 公司物料编码索引，用于物料查询
CREATE INDEX idx_materials_category ON materials(category);           -- 物料分类索引，用于分类筛选
CREATE INDEX idx_materials_status ON materials(status);               -- 物料状态索引，用于筛选有效物料
CREATE INDEX idx_materials_company_client ON materials(company_code, client_code); -- 复合索引，用于编码查询

-- 物料记录表索引
CREATE INDEX idx_material_records_type ON material_records(type);     -- 操作类型索引，用于筛选特定操作
CREATE INDEX idx_material_records_operator ON material_records(operator_id); -- 操作员索引，用于查询操作记录
CREATE INDEX idx_material_records_created ON material_records(created_at); -- 创建时间索引，用于时间范围查询

-- 物料记录明细表索引
CREATE INDEX idx_material_record_items_record ON material_record_items(record_id); -- 记录ID索引，用于关联查询
CREATE INDEX idx_material_record_items_material ON material_record_items(material_id); -- 物料ID索引，用于物料历史查询

-- 订单表索引
CREATE INDEX idx_loose_orders_order_no ON loose_orders(order_no);     -- 订单编号索引，用于订单查询
CREATE INDEX idx_loose_orders_status ON loose_orders(status);         -- 订单状态索引，用于状态筛选
CREATE INDEX idx_loose_orders_assigned_worker ON loose_orders(assigned_worker_id); -- 指派工人索引，用于工人订单查询
CREATE INDEX idx_loose_orders_batch_id ON loose_orders(batch_id);     -- 批次ID索引，用于批量导入查询
CREATE INDEX idx_loose_orders_address ON loose_orders(address(100));  -- 地址索引，用于地址查询
CREATE INDEX idx_loose_orders_total_amount ON loose_orders(total_amount); -- 费用金额索引，用于金额查询
CREATE INDEX idx_loose_orders_party_address ON loose_orders(party_address(100)); -- 甲单地址索引，用于甲单地址查询
CREATE INDEX idx_loose_orders_party_total_amount ON loose_orders(party_total_amount); -- 甲单费用金额索引，用于甲单金额查询

-- 项目表索引
CREATE INDEX idx_projects_project_no ON projects(project_no);         -- 项目编号索引，用于项目查询
CREATE INDEX idx_projects_status ON projects(status);                 -- 项目状态索引，用于状态筛选

-- 员工表索引
CREATE INDEX idx_employees_name ON employees(name);                   -- 员工姓名索引，用于姓名查询
CREATE INDEX idx_employees_work_type ON employees(work_type_id);      -- 工种索引，用于工种筛选
CREATE INDEX idx_employees_status ON employees(status);               -- 员工状态索引，用于筛选在职员工

-- 领料申请表索引
CREATE INDEX idx_material_applications_apply_no ON material_applications(apply_no); -- 申请单号索引，用于申请查询
CREATE INDEX idx_material_applications_status ON material_applications(status); -- 申请状态索引，用于状态筛选
CREATE INDEX idx_material_applications_applicant ON material_applications(applicant_id); -- 申请人索引，用于申请人查询
CREATE INDEX idx_material_applications_receiver ON material_applications(receiver_id); -- 接收人索引，用于接收人查询
CREATE INDEX idx_material_applications_date ON material_applications(apply_date); -- 申请日期索引，用于日期范围查询

-- 领料申请明细表索引
CREATE INDEX idx_material_application_items_application ON material_application_items(application_id); -- 申请ID索引，用于关联查询
CREATE INDEX idx_material_application_items_material ON material_application_items(material_id); -- 物料ID索引，用于物料申请查询

-- 结算表索引
CREATE INDEX idx_settlements_settlement_no ON settlements(settlement_no); -- 结算单号索引，用于结算查询
CREATE INDEX idx_settlements_worker_id ON settlements(worker_id);     -- 工人ID索引，用于工人结算查询
CREATE INDEX idx_settlements_status ON settlements(status);           -- 结算状态索引，用于状态筛选

-- ==========================================
-- 8. 视图创建 - 简化复杂查询
-- ==========================================

-- 物料库存视图 - 提供物料库存状态信息
CREATE VIEW v_material_inventory AS
SELECT 
    m.id,                           -- 物料ID
    m.company_code,                 -- 公司物料编码
    m.name,                         -- 物料名称
    m.category,                     -- 物料分类
    m.unit,                         -- 计量单位
    m.price,                        -- 单价
    m.stock_quantity,               -- 当前库存数量
    m.warning_quantity,             -- 预警数量
    CASE 
        WHEN m.stock_quantity <= m.warning_quantity THEN 'warning'    -- 库存预警
        WHEN m.stock_quantity = 0 THEN 'out_of_stock'                -- 库存不足
        ELSE 'normal'                                                      -- 库存正常
    END as stock_status             -- 库存状态
FROM materials m
WHERE m.status = 1;                -- 只显示有效物料

-- 订单统计视图 - 提供订单统计信息
CREATE VIEW v_order_statistics AS
SELECT 
    DATE(created_at) as order_date, -- 订单日期
    order_type,                     -- 订单类型
    status,                         -- 订单状态
    COUNT(*) as order_count         -- 订单数量
FROM loose_orders
GROUP BY DATE(created_at), order_type, status;

-- 员工工作统计视图 - 提供员工工作统计信息
CREATE VIEW v_employee_work_stats AS
SELECT 
    u.id as user_id,                -- 用户ID
    u.real_name,                    -- 真实姓名
    u.role,                         -- 用户角色
    COUNT(lo.id) as total_orders,   -- 总订单数
    COUNT(CASE WHEN lo.status = 'completed' THEN 1 END) as completed_orders,   -- 已完成订单数
    COUNT(CASE WHEN lo.status = 'in_progress' THEN 1 END) as in_progress_orders -- 进行中订单数
FROM users u
LEFT JOIN loose_orders lo ON u.id = lo.assigned_worker_id
WHERE u.role = 'worker'             -- 只统计工人
GROUP BY u.id, u.real_name, u.role;

-- ==========================================
-- 9. 触发器创建 - 自动维护更新时间
-- ==========================================

-- 用户表更新时间触发器 - 自动更新用户表的updated_at字段
CREATE TRIGGER update_users_updated_at 
    AFTER UPDATE ON users
    FOR EACH ROW
    BEGIN
        UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- 物料表更新时间触发器 - 自动更新物料表的updated_at字段
CREATE TRIGGER update_materials_updated_at 
    AFTER UPDATE ON materials
    FOR EACH ROW
    BEGIN
        UPDATE materials SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- 散户订单表更新时间触发器 - 自动更新订单表的updated_at字段
CREATE TRIGGER update_loose_orders_updated_at 
    AFTER UPDATE ON loose_orders
    FOR EACH ROW
    BEGIN
        UPDATE loose_orders SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- 项目表更新时间触发器 - 自动更新项目表的updated_at字段
CREATE TRIGGER update_projects_updated_at 
    AFTER UPDATE ON projects
    FOR EACH ROW
    BEGIN
        UPDATE projects SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- ==========================================
-- 10. 初始数据插入 - 系统基础数据
-- ==========================================

-- 插入默认管理员用户 - 系统初始管理员账户
INSERT INTO users (id, username, password, real_name, role, status) VALUES 
('admin-001', 'admin', '$2b$10$rQZ8K9mN2pL1vX3yJ6hG7t', '系统管理员', 'admin', 1);

-- 插入默认工种 - 系统基础工种配置
INSERT INTO work_types (id, name, description, base_price, status) VALUES 
('wt-001', '安装工', '负责燃气设备安装工作', 150.00, 1),    -- 安装工基础工价150元/天
('wt-002', '维修工', '负责燃气设备维修工作', 180.00, 1),    -- 维修工基础工价180元/天
('wt-003', '安检员', '负责燃气安全检查工作', 120.00, 1);    -- 安检员基础工价120元/天

-- 插入数据字典 - 系统配置数据
INSERT INTO data_dictionary (id, dict_type, dict_key, dict_value, sort_order, status) VALUES 
-- 物料分类字典
('dd-001', 'material_category', '甲料', '甲料', 1, 1),      -- 甲料分类
('dd-002', 'material_category', '乙料', '乙料', 2, 1),      -- 乙料分类
('dd-003', 'material_category', '商品', '商品', 3, 1),      -- 商品分类
('dd-004', 'material_category', '辅料', '辅料', 4, 1),      -- 辅料分类
-- 订单类型字典
('dd-005', 'order_type', '一次挂表', '一次挂表', 1, 1),     -- 一次挂表订单
('dd-006', 'order_type', '二次挂表', '二次挂表', 2, 1),     -- 二次挂表订单
('dd-007', 'order_type', '一次安装', '一次安装', 3, 1),     -- 一次安装订单
('dd-008', 'order_type', '二次安装', '二次安装', 4, 1),     -- 二次安装订单
('dd-009', 'order_type', '售后', '售后', 5, 1),             -- 售后订单
('dd-010', 'order_type', '单项工程', '单项工程', 6, 1);     -- 单项工程订单 