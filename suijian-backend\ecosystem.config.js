module.exports = {
    apps: [{
        name: 'suijian-backend',
        script: 'src/app.js',
        instances: 'max',
        exec_mode: 'cluster',
        env: {
            NODE_ENV: 'development',
            PORT: 3000
        },
        env_production: {
            NODE_ENV: 'production',
            PORT: 3000
        },
        env_staging: {
            NODE_ENV: 'staging',
            PORT: 3000
        },
        error_file: './logs/err.log',
        out_file: './logs/out.log',
        log_file: './logs/combined.log',
        time: true,
        max_memory_restart: '1G',
        watch: false,
        ignore_watch: ['node_modules', 'logs', 'uploads'],
        merge_logs: true,
        log_date_format: 'YYYY-MM-DD HH:mm:ss Z'
    }],

    deploy: {
        production: {
            user: 'node',
            host: 'localhost',
            ref: 'origin/main',
            repo: '**************:your-username/suijian-backend.git',
            path: '/var/www/production',
            'pre-deploy-local': '',
            'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
            'pre-setup': ''
        },
        staging: {
            user: 'node',
            host: 'localhost',
            ref: 'origin/develop',
            repo: '**************:your-username/suijian-backend.git',
            path: '/var/www/staging',
            'pre-deploy-local': '',
            'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env staging',
            'pre-setup': ''
        }
    }
};