const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { logger } = require('../utils/logger');

// 数据库文件路径
const dbPath = path.join(__dirname, '../../database/suijian.db');

// 创建数据库连接
const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        logger.error('数据库连接失败:', err.message);
    } else {
        logger.info('成功连接到SQLite数据库');
        // 启用外键约束
        db.run('PRAGMA foreign_keys = ON');
    }
});

// 执行查询
const query = (sql, params = []) => {
    return new Promise((resolve, reject) => {
        db.all(sql, params, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve(rows);
            }
        });
    });
};

// 执行单行查询
const queryOne = (sql, params = []) => {
    return new Promise((resolve, reject) => {
        db.get(sql, params, (err, row) => {
            if (err) {
                reject(err);
            } else {
                resolve(row);
            }
        });
    });
};

// 执行插入/更新/删除
const execute = (sql, params = []) => {
    return new Promise((resolve, reject) => {
        db.run(sql, params, function(err) {
            if (err) {
                reject(err);
            } else {
                resolve({
                    id: this.lastID,
                    changes: this.changes
                });
            }
        });
    });
};

// 关闭数据库连接
const close = () => {
    return new Promise((resolve) => {
        db.close((err) => {
            if (err) {
                logger.error('关闭数据库连接失败:', err.message);
            } else {
                logger.info('数据库连接已关闭');
            }
            resolve();
        });
    });
};

module.exports = {
    db,
    query,
    queryOne,
    execute,
    close
};