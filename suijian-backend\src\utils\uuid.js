/**
 * UUID生成工具
 */

/**
 * 生成UUID v4
 * @returns {string} UUID字符串
 */
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

/**
 * 生成短UUID（无连字符）
 * @returns {string} 短UUID字符串
 */
function generateShortUUID() {
    return generateUUID().replace(/-/g, '');
}

/**
 * 生成带前缀的ID
 * @param {string} prefix - 前缀
 * @returns {string} 带前缀的ID
 */
function generatePrefixedId(prefix) {
    return `${prefix}-${generateShortUUID()}`;
}

module.exports = {
    generateUUID,
    generateShortUUID,
    generatePrefixedId
};