const express = require('express');
const router = express.Router();
const materialsController = require('../controllers/materialsController');
const { authMiddleware } = require('../middleware/auth');
const Validator = require('../utils/validator');

// 导出物料Excel
router.get('/export', [
    authMiddleware
], materialsController.exportMaterialsExcel);
// 导入物料Excel
router.post('/import', [
    authMiddleware
], materialsController.importMaterialsExcel);
// 导入进度查询
router.get('/import/progress', [authMiddleware], materialsController.getImportProgress);

// 获取物料进出记录
router.get('/records', [
    authMiddleware
], materialsController.getMaterialRecords);

// 获取记录明细
router.get('/records/:id/items', [
    authMiddleware
], materialsController.getRecordItems);

// 新增甲方编码
router.post('/client-code', [
    authMiddleware,
    Validator.clientCodeValidationRules(),
    Validator.handleValidationResult
], materialsController.createClientCode);

// 获取物料列表
router.get('/', [
    authMiddleware,
    Validator.paginationValidationRules(),
    Validator.handleValidationResult
], materialsController.getMaterials);

// 获取物料详情
router.get('/:id', [
    authMiddleware,
    Validator.idValidationRule(),
    Validator.handleValidationResult
], materialsController.getMaterialById);

// 获取物料库存记录
router.get('/:id/stock-records', [
    authMiddleware
], materialsController.getMaterialStockRecords);

// 创建物料
router.post('/', [
    authMiddleware,
    Validator.materialValidationRules(),
    Validator.handleValidationResult
], materialsController.createMaterial);


// 更新物料
router.put('/:id', [
    authMiddleware,
    Validator.idValidationRule(),
    Validator.materialValidationRules(),
    Validator.handleValidationResult
], materialsController.updateMaterial);

// 删除物料
router.delete('/:id', [
    authMiddleware,
    Validator.idValidationRule(),
    Validator.handleValidationResult
], materialsController.deleteMaterial);

// 批量删除物料
router.delete('/', [
    authMiddleware,
    Validator.handleValidationResult
], materialsController.batchDeleteMaterials);

// 批量入库
router.post('/inbound', [
    authMiddleware,
], materialsController.batchInbound);

// 物料退仓
router.post('/return', [
    authMiddleware,
], materialsController.materialReturn);


module.exports = router;