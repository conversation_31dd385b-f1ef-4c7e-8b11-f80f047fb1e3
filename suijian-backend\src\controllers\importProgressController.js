const Response = require('../utils/response');
const { logger } = require('../utils/logger');
const importProgressTracker = require('../utils/importProgressTracker');

/**
 * 获取导入进度
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getImportProgress = async(req, res) => {
    try {
        const { taskId } = req.params;

        if (!taskId) {
            return Response.badRequest(res, '缺少taskId参数');
        }

        const task = importProgressTracker.getTask(taskId);

        if (!task) {
            return Response.notFound(res, '未找到对应的导入任务');
        }

        // 返回标准格式的进度信息
        const progressData = {
            taskId: task.taskId,
            status: task.status,
            total: task.total,
            current: task.current,
            successCount: task.successCount,
            fail: task.fail,
            message: task.message,
            startTime: task.startTime,
            endTime: task.endTime,
            phase: task.phase,
            phaseProgress: task.phaseProgress,
            // 可选：返回错误详情（限制数量）
            errors: task.errors ? task.errors.slice(-5) : [],
            // 可选：返回成功详情（限制数量）
            successDetails: task.successDetails ? task.successDetails.slice(-3) : [],
            // 可选：返回跳过详情（限制数量）
            skipDetails: task.skipDetails ? task.skipDetails.slice(-3) : []
        };

        Response.success(res, progressData, '获取导入进度成功');

    } catch (error) {
        logger.error('获取导入进度失败', error);
        Response.error(res, '获取导入进度失败');
    }
};

/**
 * 获取所有导入任务
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getAllImportTasks = async(req, res) => {
    try {
        const tasks = importProgressTracker.getAllTasks();

        // 格式化任务数据，隐藏敏感信息
        const formattedTasks = tasks.map(task => ({
            taskId: task.taskId,
            status: task.status,
            total: task.total,
            current: task.current,
            successCount: task.successCount,
            fail: task.fail,
            message: task.message,
            startTime: task.startTime,
            endTime: task.endTime,
            phase: task.phase,
            phaseProgress: task.phaseProgress,
            // 不返回详细的错误和成功信息，只返回统计
            errorCount: task.errors ? task.errors.length : 0,
            successDetailCount: task.successDetails ? task.successDetails.length : 0,
            skipDetailCount: task.skipDetails ? task.skipDetails.length : 0
        }));

        Response.success(res, {
            tasks: formattedTasks,
            total: tasks.length,
            stats: importProgressTracker.getStats()
        }, '获取导入任务列表成功');

    } catch (error) {
        logger.error('获取导入任务列表失败', error);
        Response.error(res, '获取导入任务列表失败');
    }
};

/**
 * 删除导入任务
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const deleteImportTask = async(req, res) => {
    try {
        const { taskId } = req.params;

        if (!taskId) {
            return Response.badRequest(res, '缺少taskId参数');
        }

        const deleted = importProgressTracker.deleteTask(taskId);

        if (deleted) {
            Response.success(res, null, '删除导入任务成功');
        } else {
            Response.notFound(res, '未找到对应的导入任务');
        }

    } catch (error) {
        logger.error('删除导入任务失败', error);
        Response.error(res, '删除导入任务失败');
    }
};

/**
 * 获取任务统计信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getImportStats = async(req, res) => {
    try {
        const stats = importProgressTracker.getStats();

        Response.success(res, stats, '获取导入统计信息成功');

    } catch (error) {
        logger.error('获取导入统计信息失败', error);
        Response.error(res, '获取导入统计信息失败');
    }
};

/**
 * 清理所有已完成的任务
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const cleanupAllTasks = async(req, res) => {
    try {
        const tasks = importProgressTracker.getAllTasks();
        let cleanedCount = 0;

        for (const task of tasks) {
            if (task.status === 'done' || task.status === 'error') {
                importProgressTracker.deleteTask(task.taskId);
                cleanedCount++;
            }
        }

        Response.success(res, {
            cleanedCount,
            remainingCount: tasks.length - cleanedCount
        }, `清理完成，已清理${cleanedCount}个任务`);

    } catch (error) {
        logger.error('清理导入任务失败', error);
        Response.error(res, '清理导入任务失败');
    }
};

module.exports = {
    getImportProgress,
    getAllImportTasks,
    deleteImportTask,
    getImportStats,
    cleanupAllTasks
};