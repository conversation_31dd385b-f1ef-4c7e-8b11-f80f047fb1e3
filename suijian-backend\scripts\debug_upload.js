#!/usr/bin/env node

/**
 * 调试文件上传的脚本
 */

const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 创建临时服务器来调试上传
const app = express();
const PORT = 3001;

// 配置multer
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path.join(__dirname, '../uploads/debug');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'debug-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 200 * 1024 * 1024, // 200MB
        files: 1
    }
});

// 添加详细的请求日志中间件
app.use((req, res, next) => {
    console.log('\n=== 请求详情 ===');
    console.log('方法:', req.method);
    console.log('URL:', req.url);
    console.log('Content-Type:', req.headers['content-type']);
    console.log('Content-Length:', req.headers['content-length']);
    console.log('User-Agent:', req.headers['user-agent']);
    console.log('请求头:', JSON.stringify(req.headers, null, 2));
    next();
});

// 测试上传端点
app.post('/debug-upload', upload.single('file'), (req, res) => {
    console.log('\n=== 文件上传详情 ===');
    console.log('req.file:', req.file);
    console.log('req.body:', req.body);
    console.log('req.files:', req.files);

    if (req.file) {
        console.log('文件上传成功:');
        console.log('- 原始文件名:', req.file.originalname);
        console.log('- 存储文件名:', req.file.filename);
        console.log('- 文件大小:', req.file.size);
        console.log('- 文件路径:', req.file.path);
        console.log('- MIME类型:', req.file.mimetype);

        res.json({
            success: true,
            message: '文件上传成功',
            file: {
                originalname: req.file.originalname,
                filename: req.file.filename,
                size: req.file.size,
                path: req.file.path,
                mimetype: req.file.mimetype
            }
        });
    } else {
        console.log('文件上传失败: req.file 为 null');
        res.status(400).json({
            success: false,
            message: '文件上传失败',
            error: 'req.file 为 null'
        });
    }
});

// 健康检查端点
app.get('/health', (req, res) => {
    res.json({
        success: true,
        message: '调试服务器运行正常',
        timestamp: new Date().toISOString()
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🔍 调试服务器运行在端口 ${PORT}`);
    console.log(`📝 上传测试端点: http://localhost:${PORT}/debug-upload`);
    console.log(`🏥 健康检查端点: http://localhost:${PORT}/health`);
    console.log('\n💡 使用方法:');
    console.log('1. 使用Postman或curl测试上传');
    console.log('2. 或者修改前端代码指向这个调试端点');
    console.log('3. 查看控制台输出的详细日志');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭调试服务器...');
    process.exit(0);
});