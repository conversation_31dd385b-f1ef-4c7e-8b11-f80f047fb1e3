# 工程管理系统 - 后端项目目录结构

## 项目根目录
```
engineering-management-backend/
├── src/                          # 源代码目录
│   ├── app.js                    # 应用入口文件
│   ├── config/                   # 配置文件目录
│   │   ├── database.js           # 数据库配置
│   │   ├── auth.js              # 认证配置
│   │   ├── upload.js            # 文件上传配置
│   │   └── index.js             # 配置统一导出
│   ├── models/                   # 数据模型目录
│   │   ├── index.js             # 模型统一导出
│   │   ├── User.js              # 用户模型
│   │   ├── Material.js          # 物料模型
│   │   ├── MaterialRecord.js    # 物料记录模型
│   │   ├── LooseOrder.js        # 散户订单模型
│   │   ├── Project.js           # 工程订单模型
│   │   ├── Employee.js          # 员工模型
│   │   ├── WorkType.js          # 工种模型
│   │   ├── SystemLog.js         # 系统日志模型
│   │   └── UserSession.js       # 用户会话模型
│   ├── routes/                   # 路由目录
│   │   ├── index.js             # 路由统一导出
│   │   ├── auth.js              # 认证路由
│   │   ├── users.js             # 用户管理路由
│   │   ├── materials.js         # 物料管理路由
│   │   ├── looseOrders.js       # 散户订单路由
│   │   ├── projects.js          # 工程订单路由
│   │   ├── employees.js         # 员工管理路由
│   │   ├── dashboard.js         # 仪表板路由
│   │   └── system.js            # 系统设置路由
│   ├── controllers/              # 控制器目录
│   │   ├── authController.js    # 认证控制器
│   │   ├── userController.js    # 用户管理控制器
│   │   ├── materialController.js # 物料管理控制器
│   │   ├── looseOrderController.js # 散户订单控制器
│   │   ├── projectController.js # 工程订单控制器
│   │   ├── employeeController.js # 员工管理控制器
│   │   ├── dashboardController.js # 仪表板控制器
│   │   └── systemController.js  # 系统设置控制器
│   ├── middleware/               # 中间件目录
│   │   ├── auth.js              # 认证中间件
│   │   ├── upload.js            # 文件上传中间件
│   │   ├── validation.js        # 数据验证中间件
│   │   ├── logger.js            # 日志中间件
│   │   └── errorHandler.js      # 错误处理中间件
│   ├── services/                 # 业务逻辑服务目录
│   │   ├── authService.js       # 认证服务
│   │   ├── materialService.js   # 物料服务
│   │   ├── orderService.js      # 订单服务
│   │   ├── projectService.js    # 工程服务
│   │   ├── employeeService.js   # 员工服务
│   │   ├── dashboardService.js  # 仪表板服务
│   │   └── systemService.js     # 系统服务
│   ├── utils/                    # 工具函数目录
│   │   ├── logger.js            # 日志工具
│   │   ├── response.js          # 响应工具
│   │   ├── validator.js         # 验证工具
│   │   ├── excel.js             # Excel处理工具
│   │   ├── pdf.js               # PDF生成工具
│   │   └── date.js              # 日期处理工具
│   ├── uploads/                  # 文件上传目录
│   │   ├── excel/               # Excel文件
│   │   ├── pdf/                 # PDF文件
│   │   └── temp/                # 临时文件
│   └── database/                 # 数据库相关目录
│       ├── migrations/          # 数据库迁移文件
│       ├── seeders/             # 数据库种子文件
│       └── sql/                 # SQL脚本文件
├── tests/                        # 测试文件目录
│   ├── unit/                    # 单元测试
│   ├── integration/             # 集成测试
│   └── fixtures/                # 测试数据
├── docs/                         # 文档目录
│   ├── api/                     # API文档
│   ├── database/                # 数据库文档
│   └── deployment/              # 部署文档
├── scripts/                      # 脚本目录
│   ├── start.sh                 # 启动脚本
│   ├── deploy.sh                # 部署脚本
│   └── backup.sh                # 备份脚本
├── .env                          # 环境变量文件（开发环境）
├── .env.development             # 开发环境配置
├── .env.test                    # 测试环境配置
├── .env.production              # 生产环境配置
├── .env.staging                 # 预发布环境配置
├── .gitignore                   # Git忽略文件
├── package.json                 # 项目依赖配置
├── package-lock.json            # 依赖锁定文件
├── ecosystem.config.js          # PM2配置文件
├── Dockerfile                   # Docker配置文件
├── docker-compose.yml           # Docker Compose配置
├── nginx.conf                   # Nginx配置文件
├── jest.config.js               # Jest测试配置
├── .eslintrc.js                 # ESLint配置
├── .prettierrc                  # Prettier配置
└── README.md                    # 项目说明文档
```

## 环境配置文件详解

### 1. .env (开发环境默认)
```bash
# 应用配置
NODE_ENV=development
PORT=3000
APP_NAME=工程管理系统
APP_VERSION=1.0.0

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=123456
DB_NAME=engineering_management_dev
DB_DIALECT=mysql

# SQLite配置（开发环境可选）
SQLITE_PATH=./database/suijian.db

# JWT配置
JWT_SECRET=your_jwt_secret_key_development
JWT_EXPIRES_IN=24h

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,xlsx,xls,doc,docx

# 日志配置
LOG_LEVEL=debug
LOG_FILE=./logs/app.log

# 跨域配置
CORS_ORIGIN=http://localhost:8080

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# Redis配置（可选，用于会话存储）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 第三方服务配置
ALIYUN_OSS_ACCESS_KEY_ID=
ALIYUN_OSS_ACCESS_KEY_SECRET=
ALIYUN_OSS_BUCKET=
ALIYUN_OSS_REGION=

# 安全配置
BCRYPT_ROUNDS=10
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### 2. .env.development (开发环境)
```bash
# 应用配置
NODE_ENV=development
PORT=3000
APP_NAME=工程管理系统-开发环境
APP_VERSION=1.0.0

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=dev_user
DB_PASSWORD=dev_password
DB_NAME=engineering_management_dev
DB_DIALECT=mysql

# JWT配置
JWT_SECRET=dev_jwt_secret_key_2024
JWT_EXPIRES_IN=24h

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,xlsx,xls,doc,docx

# 日志配置
LOG_LEVEL=debug
LOG_FILE=./logs/app_dev.log

# 跨域配置
CORS_ORIGIN=http://localhost:8080,http://127.0.0.1:8080

# 开发工具配置
ENABLE_SWAGGER=true
ENABLE_MOCK=true
DEBUG=true
```

### 3. .env.test (测试环境)
```bash
# 应用配置
NODE_ENV=test
PORT=3001
APP_NAME=工程管理系统-测试环境
APP_VERSION=1.0.0

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=test_user
DB_PASSWORD=test_password
DB_NAME=engineering_management_test
DB_DIALECT=mysql

# JWT配置
JWT_SECRET=test_jwt_secret_key_2024
JWT_EXPIRES_IN=1h

# 文件上传配置
UPLOAD_PATH=./uploads_test
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,xlsx,xls

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app_test.log

# 跨域配置
CORS_ORIGIN=http://localhost:8080

# 测试配置
ENABLE_SWAGGER=false
ENABLE_MOCK=false
DEBUG=false
```

### 4. .env.staging (预发布环境)
```bash
# 应用配置
NODE_ENV=staging
PORT=3000
APP_NAME=工程管理系统-预发布环境
APP_VERSION=1.0.0

# 数据库配置
DB_HOST=staging-db.example.com
DB_PORT=3306
DB_USER=staging_user
DB_PASSWORD=staging_password
DB_NAME=engineering_management_staging
DB_DIALECT=mysql

# JWT配置
JWT_SECRET=staging_jwt_secret_key_2024
JWT_EXPIRES_IN=12h

# 文件上传配置
UPLOAD_PATH=/var/www/uploads
MAX_FILE_SIZE=20971520
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,xlsx,xls,doc,docx

# 日志配置
LOG_LEVEL=info
LOG_FILE=/var/log/engineering/app_staging.log

# 跨域配置
CORS_ORIGIN=https://staging.example.com

# Redis配置
REDIS_HOST=staging-redis.example.com
REDIS_PORT=6379
REDIS_PASSWORD=staging_redis_password
REDIS_DB=0

# 第三方服务配置
ALIYUN_OSS_ACCESS_KEY_ID=staging_key_id
ALIYUN_OSS_ACCESS_KEY_SECRET=staging_key_secret
ALIYUN_OSS_BUCKET=engineering-staging
ALIYUN_OSS_REGION=cn-hangzhou

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=200
```

### 5. .env.production (生产环境)
```bash
# 应用配置
NODE_ENV=production
PORT=3000
APP_NAME=工程管理系统-生产环境
APP_VERSION=1.0.0

# 数据库配置
DB_HOST=production-db.example.com
DB_PORT=3306
DB_USER=prod_user
DB_PASSWORD=prod_strong_password_2024
DB_NAME=engineering_management_prod
DB_DIALECT=mysql

# JWT配置
JWT_SECRET=prod_jwt_secret_key_2024_very_strong
JWT_EXPIRES_IN=8h

# 文件上传配置
UPLOAD_PATH=/var/www/uploads
MAX_FILE_SIZE=52428800
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,xlsx,xls,doc,docx

# 日志配置
LOG_LEVEL=warn
LOG_FILE=/var/log/engineering/app_prod.log

# 跨域配置
CORS_ORIGIN=https://example.com,https://www.example.com

# Redis配置
REDIS_HOST=production-redis.example.com
REDIS_PORT=6379
REDIS_PASSWORD=prod_redis_strong_password
REDIS_DB=0

# 第三方服务配置
ALIYUN_OSS_ACCESS_KEY_ID=prod_key_id
ALIYUN_OSS_ACCESS_KEY_SECRET=prod_key_secret
ALIYUN_OSS_BUCKET=engineering-production
ALIYUN_OSS_REGION=cn-hangzhou

# 邮件配置
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=email_password

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=500
ENABLE_HTTPS=true
SSL_CERT_PATH=/etc/ssl/certs/example.com.crt
SSL_KEY_PATH=/etc/ssl/private/example.com.key

# 监控配置
ENABLE_MONITORING=true
SENTRY_DSN=https://your-sentry-dsn
```

## 环境切换说明

### 1. 开发环境
```bash
# 使用默认.env文件
npm run dev

# 或指定环境
NODE_ENV=development npm start
```

### 2. 测试环境
```bash
# 使用测试环境配置
NODE_ENV=test npm start

# 运行测试
npm test
```

### 3. 预发布环境
```bash
# 使用预发布环境配置
NODE_ENV=staging npm start

# 使用PM2
pm2 start ecosystem.config.js --env staging
```

### 4. 生产环境
```bash
# 使用生产环境配置
NODE_ENV=production npm start

# 使用PM2
pm2 start ecosystem.config.js --env production

# 使用Docker
docker run -e NODE_ENV=production engineering-backend
```

## 安全注意事项

1. **敏感信息保护**：
   - 生产环境的密码、密钥等敏感信息不应提交到代码仓库
   - 使用环境变量或密钥管理服务存储敏感信息

2. **环境隔离**：
   - 不同环境使用不同的数据库
   - 不同环境使用不同的文件存储路径
   - 不同环境使用不同的第三方服务配置

3. **访问控制**：
   - 生产环境限制CORS来源
   - 生产环境启用HTTPS
   - 生产环境配置适当的速率限制

4. **日志管理**：
   - 生产环境使用适当的日志级别
   - 配置日志轮转和归档
   - 敏感信息不应记录在日志中 