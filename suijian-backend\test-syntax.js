const fs = require('fs');
const path = require('path');

// 要检查的文件列表
const filesToCheck = [
    'src/controllers/employeesController.js',
    'src/controllers/materialsController.js',
    'src/controllers/materialApplicationController.js',
    'src/routes/employees.js',
    'src/routes/materials.js',
    'src/routes/materialApplications.js',
    'src/routes/index.js'
];

console.log('开始检查语法错误...\n');

let hasErrors = false;

filesToCheck.forEach(file => {
    try {
        const filePath = path.join(__dirname, file);
        if (fs.existsSync(filePath)) {
            // 尝试解析文件
            require(filePath);
            console.log(`✅ ${file} - 语法正确`);
        } else {
            console.log(`❌ ${file} - 文件不存在`);
            hasErrors = true;
        }
    } catch (error) {
        console.log(`❌ ${file} - 语法错误: ${error.message}`);
        hasErrors = true;
    }
});

console.log('\n' + (hasErrors ? '❌ 发现语法错误' : '✅ 所有文件语法正确'));