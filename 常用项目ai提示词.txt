一、创建架构设计文档
@UI示意图 @商城小程序的项目需求.txt 
请根据项目需求生成一个项目技术架构设计文档。文件写入：项目架构设计文档.txt
要求：
1、不用做复杂设计，不需要考虑高并发问题。
2、要考虑安全性问题，要能避免常见的针对小程序和网站的网络攻击。
3、架构设计要简洁明了，让不懂技术的人也能看懂。
4、如果有项目的目录结构，可以输出到单独文件中，文件名前缀如：项目目录结构-前端/后端/管理后台

二、设计多环境的配置方案
不同的开发环境应该是有各自的配置文件，这样在部署的时候可以通过调整启动参数或者环境变量来区分程序应该使用哪个环境的配置文件。
请你按上面需求详细的设计该部分。

三、创建数据库建表SQL
请帮我生成数据库建表SQL，数据库名为mall。
写入文件：数据库建表语句.sql
要求：
1、字段要有中文注释
2、为管理后台用户表增加一条数据，用户名为admin，密码的明文为admin，但是应该以MD5密文存储在数据库中。