# Excel导入字段与数据库字段对应关系

## 字段映射表

| Excel字段名称 | 数据库字段名 | 字段类型 | 是否必填 | 说明 |
|---------------|-------------|----------|----------|------|
| 工单号 | `order_no` | VARCHAR(50) | ✅ 必填 | 订单编号，唯一标识 |
| 用户编号 | `customer_code` | VARCHAR(50) | ❌ 可选 | 客户编码 |
| 用户姓名 | `customer_name` | VARCHAR(50) | ✅ 必填 | 客户姓名 |
| 移动电话 | `phone` | VARCHAR(20) | ❌ 可选 | 联系电话 |
| 地址 | `address` | TEXT | ❌ 可选 | 地址信息 |
| 诉求描述 | `appeal_description` | TEXT | ❌ 可选 | 诉求描述 |
| 备注 | `remarks` | TEXT | ❌ 可选 | 备注信息 |
| 费用合计金额 | `total_amount` | DECIMAL(10,2) | ❌ 可选 | 费用合计金额，默认0.00 |

## 甲单字段映射（可选）

| Excel字段名称 | 数据库字段名 | 字段类型 | 是否必填 | 说明 |
|---------------|-------------|----------|----------|------|
| 甲单地址 | `party_address` | TEXT | ❌ 可选 | 甲单地址信息 |
| 甲单诉求描述 | `party_appeal_description` | TEXT | ❌ 可选 | 甲单诉求描述 |
| 甲单费用合计金额 | `party_total_amount` | DECIMAL(10,2) | ❌ 可选 | 甲单费用合计金额，默认0.00 |
| 甲单备注 | `party_remarks` | TEXT | ❌ 可选 | 甲单备注信息 |

## 支持的Excel字段名称变体

### 基础字段
- **工单号**: 工单号、订单号、编号、ID、序号
- **用户编号**: 用户编号、客户编号、客户编码、编号、Code
- **用户姓名**: 用户姓名、客户姓名、姓名、客户名称、Name
- **移动电话**: 移动电话、手机号码、联系电话、电话、Phone
- **地址**: 地址、详细地址、住址、Address
- **诉求描述**: 诉求描述、工单内容、描述、内容、Description
- **备注**: 备注、说明、Remarks、Note
- **费用合计金额**: 费用合计金额、合计金额、总金额、金额、Amount

### 甲单字段
- **甲单地址**: 甲单地址、甲方地址、Party Address
- **甲单诉求描述**: 甲单诉求描述、甲方诉求、Party Description
- **甲单费用合计金额**: 甲单费用合计金额、甲方费用、Party Amount
- **甲单备注**: 甲单备注、甲方备注、Party Remarks

## 数据库表结构

```sql
CREATE TABLE loose_orders (
    id VARCHAR(36) PRIMARY KEY,                    -- 订单ID，UUID格式
    order_no VARCHAR(50) UNIQUE NOT NULL,          -- 订单编号，唯一标识
    customer_name VARCHAR(50) NOT NULL,            -- 客户姓名
    customer_code VARCHAR(50),                     -- 客户编码
    community_name VARCHAR(100),                   -- 小区名称
    building VARCHAR(50),                          -- 楼栋号
    room_number VARCHAR(50),                       -- 房间号
    phone VARCHAR(20),                             -- 联系电话
    contact_person VARCHAR(50),                    -- 联系人
    order_type VARCHAR(50),                        -- 订单类型
    project_name VARCHAR(100),                     -- 项目名称
    address TEXT,                                  -- 地址信息
    appeal_description TEXT,                       -- 诉求描述
    total_amount DECIMAL(10,2) DEFAULT 0.00,       -- 费用合计金额
    batch_id VARCHAR(36),                          -- 批次ID
    party_address TEXT,                            -- 甲单地址
    party_appeal_description TEXT,                 -- 甲单诉求描述
    party_total_amount DECIMAL(10,2) DEFAULT 0.00, -- 甲单费用合计金额
    party_remarks TEXT,                            -- 甲单备注
    status VARCHAR(20) DEFAULT 'pending',          -- 订单状态
    installation_date DATE,                        -- 安装日期
    assigned_worker_id VARCHAR(36),                -- 指派工人ID
    remarks TEXT,                                  -- 备注信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);
```

## 导入逻辑说明

### 1. 字段提取
- 系统会自动识别Excel中的字段名称（支持多种变体）
- 只有工单号是必填字段，其他字段都是可选的
- 如果字段不存在，会使用默认值或空值

### 2. 数据验证
- 工单号必须唯一，重复的工单号会被跳过
- 费用金额字段会自动转换为数字格式
- 非数字的费用金额会被转换为0

### 3. 默认值设置
- `order_type`: 默认为 '普通工单'
- `status`: 默认为 'pending'
- `total_amount`: 默认为 0.00
- `party_total_amount`: 默认为 0.00

### 4. 错误处理
- 缺少工单号列会返回错误
- 数据格式错误会记录在错误日志中
- 重复工单号会跳过并记录

## 示例

### Excel文件格式
| 工单号 | 用户姓名 | 用户编号 | 移动电话 | 地址 | 诉求描述 | 备注 | 费用合计金额 |
|--------|----------|----------|----------|------|----------|------|--------------|
| WO001  | 张三     | C001     | 13800138000 | 北京市朝阳区XX小区 | 燃气管道维修 | 紧急处理 | 150.00 |

### 数据库记录
```sql
INSERT INTO loose_orders (
    id, order_no, customer_name, customer_code, phone, 
    address, appeal_description, remarks, total_amount, 
    order_type, status
) VALUES (
    'uuid-123', 'WO001', '张三', 'C001', '13800138000',
    '北京市朝阳区XX小区', '燃气管道维修', '紧急处理', 150.00,
    '普通工单', 'pending'
);
``` 