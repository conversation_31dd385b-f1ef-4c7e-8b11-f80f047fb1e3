const { execute, query } = require('./src/config/database');
const { v4: uuidv4 } = require('uuid');

async function testSqliteFix() {
    console.log('测试SQLite NOW()函数修复...');

    try {
        // 测试插入数据
        const testId = uuidv4();
        const testOrderNo = `TEST-${Date.now()}`;

        console.log('1. 测试INSERT语句...');
        const now = new Date().toISOString();
        const insertSql = `
            INSERT INTO loose_orders (
                id, order_no, customer_name, customer_code, community_name,
                building, room_number, phone, contact_person, order_type,
                project_name, batch_id, party_address, party_appeal_description, party_total_amount, party_remarks,
                status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        const insertParams = [
            testId, testOrderNo, '测试用户', 'TEST001', '',
            '', '', '13800138000', '', '普通工单',
            '', 'test-batch', '测试地址', '测试诉求', 100.00, '测试备注',
            'pending', now, now
        ];

        await execute(insertSql, insertParams);
        console.log('✓ INSERT语句执行成功');

        // 测试查询数据
        console.log('2. 测试查询数据...');
        const result = await query('SELECT * FROM loose_orders WHERE id = ?', [testId]);
        console.log('✓ 查询成功，找到记录:', result.length);

        if (result.length > 0) {
            console.log('记录详情:', {
                id: result[0].id,
                order_no: result[0].order_no,
                customer_name: result[0].customer_name,
                party_address: result[0].party_address,
                created_at: result[0].created_at,
                updated_at: result[0].updated_at
            });
        }

        // 测试更新数据
        console.log('3. 测试UPDATE语句...');
        const updateNow = new Date().toISOString();
        const updateSql = `
            UPDATE loose_orders SET
                customer_name = '更新后的用户',
                party_address = '更新后的地址',
                updated_at = ?
            WHERE id = ?
        `;

        await execute(updateSql, [updateNow, testId]);
        console.log('✓ UPDATE语句执行成功');

        // 清理测试数据
        console.log('4. 清理测试数据...');
        await execute('DELETE FROM loose_orders WHERE id = ?', [testId]);
        console.log('✓ 测试数据清理完成');

        console.log('\n🎉 所有SQLite修复测试通过！');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('错误详情:', error);
    }
}

// 运行测试
testSqliteFix().then(() => {
    console.log('测试完成');
    process.exit(0);
}).catch(error => {
    console.error('测试异常:', error);
    process.exit(1);
});