<template>
  <div class="performance-setting-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>绩效设置</span>
        </div>
      </template>
      
      <!-- 绩效参数设置 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">绩效参数设置</div>
        </el-col>
        <el-col :span="24">
          <el-radio-group v-model="performanceType" @change="handlePerformanceTypeChange">
            <el-radio-button label="product">商品售卖绩效</el-radio-button>
            <el-radio-button label="order">散户订单绩效</el-radio-button>
          </el-radio-group>
        </el-col>
      </el-row>
      
      <!-- 商品售卖绩效参数 -->
      <el-row :gutter="20" class="form-section" v-if="performanceType === 'product'">
        <el-col :span="24">
          <div class="section-subtitle">商品售卖绩效参数</div>
        </el-col>
        <el-col :span="24">
          <el-table :data="productPerformanceList" border class="performance-table">
            <el-table-column prop="productCategory" label="商品分类" min-width="120" />
            <el-table-column prop="calculationMethod" label="计算方式" min-width="120" />
            <el-table-column prop="performanceRatio" label="绩效比例" min-width="100">
              <template #default="scope">
                {{ scope.row.performanceRatio }}%
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" min-width="80">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'enabled' ? 'success' : 'info'">
                  {{ scope.row.status === 'enabled' ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="120" fixed="right">
              <template #default="scope">
                <el-button type="primary" link @click="editProductPerformance(scope.row)">修改</el-button>
                <el-button 
                  type="primary" 
                  link 
                  @click="toggleProductPerformanceStatus(scope.row)"
                >
                  {{ scope.row.status === 'enabled' ? '禁用' : '启用' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 15px;">
            <el-button type="primary" icon="Plus" @click="addProductPerformance">新增分类</el-button>
            <el-button type="success" @click="saveProductSettings">保存设置</el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 新增商品分类绩效弹窗 -->
      <el-dialog 
        v-model="productDialogVisible" 
        :title="productDialogTitle" 
        width="500"
        @close="handleProductDialogClose"
      >
        <el-form 
          ref="productFormRef" 
          :model="currentProductPerformance" 
          :rules="productPerformanceRules" 
          label-width="120px"
        >
          <el-form-item label="商品分类:" prop="productCategory">
            <el-input v-model="currentProductPerformance.productCategory" placeholder="请输入商品分类" />
          </el-form-item>
          <el-form-item label="计算方式:" prop="calculationMethod">
            <el-select v-model="currentProductPerformance.calculationMethod" placeholder="请选择计算方式" style="width: 100%">
              <el-option label="销售金额比例" value="salesAmountRatio" />
            </el-select>
          </el-form-item>
          <el-form-item label="绩效比例:" prop="performanceRatio">
            <el-input-number 
              v-model="currentProductPerformance.performanceRatio" 
              :min="0" 
              :max="100"
              controls-position="right" 
              style="width: 100%" 
            >
              <template #append>%</template>
            </el-input-number>
          </el-form-item>
          <el-form-item label="状态:" prop="status">
            <el-radio-group v-model="currentProductPerformance.status">
              <el-radio label="enabled">启用</el-radio>
              <el-radio label="disabled">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <p class="explanation">说明: 绩效金额 = 销售金额 × 绩效比例</p>
            <div>
              <el-button @click="productDialogVisible = false">取消</el-button>
              <el-button type="primary" @click="saveProductPerformance">保存</el-button>
            </div>
          </div>
        </template>
      </el-dialog>
      
      <!-- 散户订单绩效参数 -->
      <el-row :gutter="20" class="form-section" v-if="performanceType === 'order'">
        <el-col :span="24">
          <div class="section-subtitle">散户订单绩效参数</div>
        </el-col>
        <el-col :span="24">
          <el-table :data="orderPerformanceList" border class="performance-table">
            <el-table-column prop="orderCategory" label="订单分类" min-width="120" />
            <el-table-column prop="calculationMethod" label="计算方式" min-width="120" />
            <el-table-column prop="performanceAmount" label="绩效金额(元/单)" min-width="120" />
            <el-table-column prop="status" label="状态" min-width="80">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'enabled' ? 'success' : 'info'">
                  {{ scope.row.status === 'enabled' ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="120" fixed="right">
              <template #default="scope">
                <el-button type="primary" link @click="editOrderPerformance(scope.row)">修改</el-button>
                <el-button 
                  type="primary" 
                  link 
                  @click="toggleOrderPerformanceStatus(scope.row)"
                >
                  {{ scope.row.status === 'enabled' ? '禁用' : '启用' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 15px;">
            <el-button type="primary" icon="Plus" @click="addOrderPerformance">新增分类</el-button>
            <el-button type="success" @click="saveOrderSettings">保存设置</el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 新增订单分类绩效弹窗 -->
      <el-dialog 
        v-model="orderDialogVisible" 
        :title="orderDialogTitle" 
        width="500"
        @close="handleOrderDialogClose"
      >
        <el-form 
          ref="orderFormRef" 
          :model="currentOrderPerformance" 
          :rules="orderPerformanceRules" 
          label-width="120px"
        >
          <el-form-item label="订单分类:" prop="orderCategory">
            <el-input v-model="currentOrderPerformance.orderCategory" placeholder="请输入订单分类" />
          </el-form-item>
          <el-form-item label="计算方式:" prop="calculationMethod">
            <el-select v-model="currentOrderPerformance.calculationMethod" placeholder="请选择计算方式" style="width: 100%">
              <el-option label="固定金额" value="fixedAmount" />
              <el-option label="混合计算" value="mixedCalculation" />
              <el-option label="比例计算" value="ratioCalculation" />
            </el-select>
          </el-form-item>
          <el-form-item label="绩效金额:" prop="performanceAmount">
            <el-input v-model="currentOrderPerformance.performanceAmount" placeholder="请输入绩效金额">
              <template #append>元/单</template>
            </el-input>
          </el-form-item>
          <el-form-item label="状态:" prop="status">
            <el-radio-group v-model="currentOrderPerformance.status">
              <el-radio label="enabled">启用</el-radio>
              <el-radio label="disabled">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <div class="explanation">
              <p>计算方式说明:</p>
              <p>1. 固定金额: 每单固定绩效金额</p>
              <p>2. 混合计算: 基础金额+超额部分按比例计算</p>
              <p>3. 比例计算: 按订单金额的一定比例计算</p>
            </div>
            <div>
              <el-button @click="orderDialogVisible = false">取消</el-button>
              <el-button type="primary" @click="saveOrderPerformance">保存</el-button>
            </div>
          </div>
        </template>
      </el-dialog>
      
      <!-- 绩效统计 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">绩效统计</div>
        </el-col>
        <el-col :span="24">
          <el-card class="statistics-card">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="statistic-item">
                  <span class="label">本月商品销售绩效总额:</span>
                  <span>¥12,580</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="statistic-item">
                  <span class="label">本月订单完成绩效总额:</span>
                  <span>¥8,450</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="statistic-item">
                  <span class="label">绩效支出合计:</span>
                  <span>¥21,030</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="statistic-item">
                  <span class="label">占总人工成本比例:</span>
                  <span>15.2%</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 备注信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">备注信息</div>
        </el-col>
        <el-col :span="24">
          <el-form-item label="绩效说明:">
            <el-input
              v-model="remarks.performanceExplanation"
              type="textarea"
              :rows="3"
              placeholder="请输入绩效说明"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="调整记录:">
            <el-card class="record-card">
              <div class="record-item">2024-01-01 调整智能产品绩效比例从7%至8%</div>
              <div class="record-item">2023-12-15 新增单项工程混合计算方式</div>
              <div class="record-item">2023-11-20 调整一次安装绩效从90元至100元</div>
            </el-card>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button type="success" @click="saveAllSettings">保存设置</el-button>
        <el-button @click="restoreDefault">恢复默认</el-button>
        <el-button @click="exportSettings">导出设置</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 绩效类型
const performanceType = ref('product')

// 商品售卖绩效列表
const productPerformanceList = ref([
  {
    id: 1,
    productCategory: '智能产品',
    calculationMethod: 'salesAmountRatio',
    performanceRatio: 8,
    status: 'enabled'
  },
  {
    id: 2,
    productCategory: '照明产品',
    calculationMethod: 'salesAmountRatio',
    performanceRatio: 5,
    status: 'enabled'
  },
  {
    id: 3,
    productCategory: '开关插座',
    calculationMethod: 'salesAmountRatio',
    performanceRatio: 6,
    status: 'enabled'
  },
  {
    id: 4,
    productCategory: '电线电缆',
    calculationMethod: 'salesAmountRatio',
    performanceRatio: 4,
    status: 'enabled'
  }
])

// 散户订单绩效列表
const orderPerformanceList = ref([
  {
    id: 1,
    orderCategory: '一次挂表',
    calculationMethod: 'fixedAmount',
    performanceAmount: '50',
    status: 'enabled'
  },
  {
    id: 2,
    orderCategory: '二次挂表',
    calculationMethod: 'fixedAmount',
    performanceAmount: '40',
    status: 'enabled'
  },
  {
    id: 3,
    orderCategory: '一次安装',
    calculationMethod: 'fixedAmount',
    performanceAmount: '100',
    status: 'enabled'
  },
  {
    id: 4,
    orderCategory: '二次安装',
    calculationMethod: 'fixedAmount',
    performanceAmount: '80',
    status: 'enabled'
  },
  {
    id: 5,
    orderCategory: '售后服务',
    calculationMethod: 'fixedAmount',
    performanceAmount: '60',
    status: 'enabled'
  },
  {
    id: 6,
    orderCategory: '单项工程',
    calculationMethod: 'mixedCalculation',
    performanceAmount: '基础50+超额10%',
    status: 'enabled'
  }
])

// 当前商品绩效
const currentProductPerformance = ref({
  id: 0,
  productCategory: '',
  calculationMethod: 'salesAmountRatio',
  performanceRatio: 0,
  status: 'enabled'
})

// 当前订单绩效
const currentOrderPerformance = ref({
  id: 0,
  orderCategory: '',
  calculationMethod: 'fixedAmount',
  performanceAmount: '',
  status: 'enabled'
})

// 备注信息
const remarks = reactive({
  performanceExplanation: ''
})

// 弹窗控制
const productDialogVisible = ref(false)
const orderDialogVisible = ref(false)

// 表单引用
const productFormRef = ref()
const orderFormRef = ref()

// 对话框标题
const productDialogTitle = computed(() => {
  return currentProductPerformance.value.id ? '修改商品分类绩效' : '新增商品分类绩效'
})

const orderDialogTitle = computed(() => {
  return currentOrderPerformance.value.id ? '修改订单分类绩效' : '新增订单分类绩效'
})

// 表单验证规则
const productPerformanceRules = {
  productCategory: [
    { required: true, message: '请输入商品分类', trigger: 'blur' }
  ],
  calculationMethod: [
    { required: true, message: '请选择计算方式', trigger: 'blur' }
  ],
  performanceRatio: [
    { required: true, message: '请输入绩效比例', trigger: 'blur' }
  ]
}

const orderPerformanceRules = {
  orderCategory: [
    { required: true, message: '请输入订单分类', trigger: 'blur' }
  ],
  calculationMethod: [
    { required: true, message: '请选择计算方式', trigger: 'blur' }
  ],
  performanceAmount: [
    { required: true, message: '请输入绩效金额', trigger: 'blur' }
  ]
}

// 绩效类型变更
const handlePerformanceTypeChange = () => {
  ElMessage.success(`切换到${performanceType.value === 'product' ? '商品售卖' : '散户订单'}绩效`)
}

// 编辑商品绩效
const editProductPerformance = (row: any) => {
  currentProductPerformance.value = { ...row }
  productDialogVisible.value = true
}

// 切换商品绩效状态
const toggleProductPerformanceStatus = (row: any) => {
  const newStatus = row.status === 'enabled' ? 'disabled' : 'enabled'
  ElMessage.success(`已${newStatus === 'enabled' ? '启用' : '禁用'}分类: ${row.productCategory}`)
  row.status = newStatus
}

// 新增商品绩效
const addProductPerformance = () => {
  currentProductPerformance.value = {
    id: 0,
    productCategory: '',
    calculationMethod: 'salesAmountRatio',
    performanceRatio: 0,
    status: 'enabled'
  }
  productDialogVisible.value = true
}

// 保存商品绩效
const saveProductPerformance = () => {
  if (!productFormRef.value) return
  
  productFormRef.value.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('保存成功')
      productDialogVisible.value = false
      console.log('保存商品绩效:', currentProductPerformance.value)
    }
  })
}

// 对话框关闭
const handleProductDialogClose = () => {
  if (productFormRef.value) {
    productFormRef.value.resetFields()
  }
}

// 保存商品设置
const saveProductSettings = () => {
  ElMessage.success('保存商品绩效设置成功')
}

// 编辑订单绩效
const editOrderPerformance = (row: any) => {
  currentOrderPerformance.value = { ...row }
  orderDialogVisible.value = true
}

// 切换订单绩效状态
const toggleOrderPerformanceStatus = (row: any) => {
  const newStatus = row.status === 'enabled' ? 'disabled' : 'enabled'
  ElMessage.success(`已${newStatus === 'enabled' ? '启用' : '禁用'}分类: ${row.orderCategory}`)
  row.status = newStatus
}

// 新增订单绩效
const addOrderPerformance = () => {
  currentOrderPerformance.value = {
    id: 0,
    orderCategory: '',
    calculationMethod: 'fixedAmount',
    performanceAmount: '',
    status: 'enabled'
  }
  orderDialogVisible.value = true
}

// 保存订单绩效
const saveOrderPerformance = () => {
  if (!orderFormRef.value) return
  
  orderFormRef.value.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('保存成功')
      orderDialogVisible.value = false
      console.log('保存订单绩效:', currentOrderPerformance.value)
    }
  })
}

// 对话框关闭
const handleOrderDialogClose = () => {
  if (orderFormRef.value) {
    orderFormRef.value.resetFields()
  }
}

// 保存订单设置
const saveOrderSettings = () => {
  ElMessage.success('保存订单绩效设置成功')
}

// 保存所有设置
const saveAllSettings = () => {
  ElMessage.success('保存所有设置成功')
}

// 恢复默认
const restoreDefault = () => {
  ElMessage.success('恢复默认设置')
}

// 导出设置
const exportSettings = () => {
  ElMessage.success('导出设置')
}
</script>

<style lang="scss" scoped>
.performance-setting-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
    
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #606266;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
    
    .section-subtitle {
      font-size: 14px;
      font-weight: bold;
      color: #606266;
      margin-bottom: 15px;
    }
    
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  
  .performance-table {
    margin-top: 10px;
  }
  
  .statistics-card {
    background-color: #f0f9ff;
    border-color: #b3e0ff;
    
    .statistic-item {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      
      .label {
        font-weight: bold;
        color: #409eff;
      }
    }
  }
  
  .record-card {
    background-color: #f0f9ff;
    border-color: #b3e0ff;
    
    .record-item {
      margin-bottom: 5px;
      color: #606266;
    }
  }
  
  .dialog-footer {
    .explanation {
      text-align: left;
      margin-bottom: 15px;
      color: #606266;
      
      p {
        margin: 5px 0;
      }
    }
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
    }
  }
}
</style>