<template>
  <div class="order-list">
    <el-card class="list-card" body-style="padding:0;margin:0;">
      <!-- 搜索栏 -->
      <div class="search-section">
        <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderNo"
            placeholder="请输入甲方订单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="户名">
          <el-input
            v-model="searchForm.customerName"
            placeholder="请输入户名"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="地址">
          <el-input
            v-model="searchForm.address"
            placeholder="请输入地址"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="电话">
          <el-input
            v-model="searchForm.phone"
            placeholder="请输入电话"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="联系人">
          <el-input
            v-model="searchForm.contactPerson"
            placeholder="请输入联系人"
            clearable
            style="width: 120px"
          />
        </el-form-item>
        <el-form-item label="订单分类">
            <el-select v-model="searchForm.orderType" placeholder="请选择分类" clearable style="width: 150px">
            <el-option label="户内整改" value="户内整改" />
            <el-option label="一次挂表" value="一次挂表" />
            <el-option label="二次挂表" value="二次挂表" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 150px">
            <el-option label="待分派" value="待分派" />
            <el-option label="进行中" value="进行中" />
            <el-option label="已完成" value="已完成" />
            <el-option label="已取消" value="已取消" />
          </el-select>
        </el-form-item>
        <el-form-item label="跟进师傅">
          <el-input
            v-model="searchForm.assignedWorker"
            placeholder="请输入跟进师傅"
            clearable
            style="width: 120px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
      <!-- 操作按钮 -->
      <div class="action-section">
        <div class="left-actions">
          <el-button type="primary" @click="importDialogVisible = true">
            <el-icon><Upload /></el-icon>
            导入Excel
          </el-button>
          <el-button 
            type="success" 
            @click="handleBatchDispatch"
            :disabled="selectedRows.length === 0"
          >
            <el-icon><UserFilled /></el-icon>
            批量派单
          </el-button>
        </div>
    </div>
      <!-- 表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        stripe
        @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
          :row-class-name="getRowClassName"
      >
          <el-table-column type="selection" width="40" fixed="left" />
        <el-table-column prop="orderNo" label="甲方订单号" width="150" />
        <el-table-column prop="customerName" label="户名" width="120" />
        <el-table-column prop="address" label="地址" min-width="200" show-overflow-tooltip />
        <el-table-column prop="phone" label="电话" width="120" />
        <el-table-column prop="contactPerson" label="联系人" width="100" />
        <el-table-column prop="orderType" label="订单分类" width="100">
          <template #default="{ row }">
            <el-tag :type="getOrderTypeTag(row.orderType)">{{ row.orderType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="订单状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.status)">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assignedWorker" label="跟进师傅" width="100" />
        <el-table-column prop="checked" label="是否核对" width="100">
          <template #default="{ row }">
            <el-tag :type="row.checked ? 'success' : 'info'">
              {{ row.checked ? '已核对' : '未核对' }}
            </el-tag>
          </template>
        </el-table-column>
          <el-table-column prop="dispatchTime" label="派单时间" width="150" />
          <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleDispatch(row)">派单</el-button>
              <el-button type="success" size="small" @click="handleEnterWorkOrder(row)">录入工单</el-button>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 分派对话框 -->
    <el-dialog
      v-model="dispatchDialogVisible"
      title="订单分派"
      width="900px"
      top="5vh"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- 订单信息展示 -->
      <div class="order-info">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">甲方订单号:</span>
              <span class="value">{{ dispatchForm.orderNo }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">户名:</span>
              <span class="value">{{ dispatchForm.customerName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">用户编号:</span>
              <span class="value">{{ dispatchForm.userNo }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">小区名称:</span>
              <span class="value">{{ dispatchForm.communityName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">楼橦:</span>
              <span class="value">{{ dispatchForm.building }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">房号:</span>
              <span class="value">{{ dispatchForm.roomNo }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">电话:</span>
              <span class="value">{{ dispatchForm.phone }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">联系人:</span>
              <span class="value">{{ dispatchForm.contactPerson }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">订单分类:</span>
              <span class="value">{{ dispatchForm.orderType }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">订单状态:</span>
              <span class="value">{{ dispatchForm.status }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 师傅选择区域 -->
      <div class="master-selection">
        <div class="section-title">选择师傅</div>
        
        <!-- 搜索区域 -->
        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :span="18">
            <el-input v-model="masterSearch" placeholder="请输入搜索关键词" clearable />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" icon="Search" @click="searchMasters">搜索</el-button>
          </el-col>
        </el-row>
        
        <!-- 师傅列表 -->
        <el-table :data="masterOptions" border height="200" class="master-table">
          <el-table-column prop="name" label="师傅姓名" width="100" />
          <el-table-column prop="workType" label="工种" width="120" />
          <el-table-column prop="dailyWage" label="工价(元/天)" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'">
                {{ scope.row.status === 'active' ? '在职' : '离职' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="taskCount" label="当前任务数" width="120" />
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="scope">
              <el-button type="primary" @click="selectMaster(scope.row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 备注区域 -->
        <div class="remarks-section">
          <el-input
            v-model="dispatchForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSave">保存</el-button>
          <el-button type="success" @click="handleDispatchSubmit">提交</el-button>
          <el-button @click="handlePrint">打印</el-button>
          <el-button @click="dispatchDialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量派单对话框 -->
    <el-dialog
      v-model="batchDispatchDialogVisible"
      title="批量派单"
      width="800px"
      top="5vh"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- 选中订单信息 -->
      <div class="selected-orders-info">
        <div class="section-title">已选订单 ({{ selectedRows.length }}个)</div>
        <el-table :data="selectedRows" border height="200" class="selected-orders-table">
          <el-table-column prop="orderNo" label="订单号" width="150" />
          <el-table-column prop="customerName" label="户名" width="120" />
          <el-table-column prop="address" label="地址" min-width="200" show-overflow-tooltip />
          <el-table-column prop="orderType" label="订单分类" width="100">
            <template #default="{ row }">
              <el-tag :type="getOrderTypeTag(row.orderType)">{{ row.orderType }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTag(row.status)">{{ row.status }}</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 师傅选择区域 -->
      <div class="master-selection">
        <div class="section-title">选择师傅</div>
        
        <!-- 搜索区域 -->
        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :span="18">
            <el-input v-model="batchMasterSearch" placeholder="请输入搜索关键词" clearable />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" icon="Search" @click="searchBatchMasters">搜索</el-button>
          </el-col>
        </el-row>
        
        <!-- 师傅列表 -->
        <el-table :data="batchMasterOptions" border height="200" class="master-table">
          <el-table-column prop="name" label="师傅姓名" width="100" />
          <el-table-column prop="workType" label="工种" width="120" />
          <el-table-column prop="dailyWage" label="工价(元/天)" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'">
                {{ scope.row.status === 'active' ? '在职' : '离职' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="taskCount" label="当前任务数" width="120" />
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="scope">
              <el-button type="primary" @click="selectBatchMaster(scope.row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 备注区域 -->
        <div class="remarks-section">
          <el-input
            v-model="batchDispatchForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="success" @click="handleBatchDispatchSubmit">确认派单</el-button>
          <el-button @click="batchDispatchDialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入Excel弹窗 -->
    <el-dialog v-model="importDialogVisible" title="导入工单Excel" width="400px">
      <el-upload
        class="upload-demo"
        drag
        :action="importExcelUrl"
        :show-file-list="false"
        :before-upload="beforeImportExcel"
        :on-success="handleImportSuccess"
        :on-error="handleImportError"
        :headers="uploadHeaders"
        :disabled="importing || importDone"
        name="file"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <div v-if="importing || importDone" style="margin-top: 20px;">
        <el-progress :percentage="importProgressPercent" :status="importProgressStatus" :text-inside="true" :stroke-width="20">
          <span v-if="importProgressStatus==='success'">{{importProgressMessage}}</span>
          <span v-else-if="importProgressStatus==='exception'">{{importProgressMessage}}</span>
          <span v-else>{{importProgressMessage}}</span>
        </el-progress>
      </div>
      <template #footer>
        <el-button @click="handleImportDialogClose">{{ importDone ? '完成' : '取消' }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { Upload, UserFilled, Check } from '@element-plus/icons-vue'
import request from '@/utils/request'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  orderNo: '',
  customerName: '',
  address: '',
  phone: '',
  contactPerson: '',
  orderType: '',
  status: '',
  assignedWorker: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)
const selectedRows = ref([])

// 当前高亮的行
const highlightedRow = ref(null)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 分派对话框
const dispatchDialogVisible = ref(false)
const dispatchForm = reactive({
  orderNo: '',
  customerName: '',
  userNo: '',
  communityName: '',
  building: '',
  roomNo: '',
  phone: '',
  contactPerson: '',
  orderType: '',
  status: '',
  assignedWorker: '',
  estimatedDays: 1,
  startDate: '',
  remark: ''
})

// 批量派单对话框
const batchDispatchDialogVisible = ref(false)
const batchDispatchForm = reactive({
  assignedWorker: '',
  remark: ''
})

// 导入Excel相关
const importDialogVisible = ref(false)
const importExcelUrl = '/api/loose-orders/import' // 后端接口路径
const uploadHeaders = {
  Authorization: 'Bearer ' + (localStorage.getItem('auth_token') || '')
}

const importing = ref(false)
const importDone = ref(false)
const importTaskId = ref('')
const importProgressPercent = ref(0)
const importProgressStatus = ref<'success'|'exception'|'active'>('active')
const importProgressMessage = ref('')
let importProgressTimer: any = null

// 师傅搜索
const masterSearch = ref('')
const batchMasterSearch = ref('')

// 师傅选项
const masterOptions = ref([
  {
    id: 1,
    name: '李师傅',
    workType: '电工',
    dailyWage: 300,
    status: 'active',
    taskCount: 2
  },
  {
    id: 2,
    name: '王师傅',
    workType: '水工',
    dailyWage: 280,
    status: 'active',
    taskCount: 1
  },
  {
    id: 3,
    name: '张师傅',
    workType: '安装工',
    dailyWage: 320,
    status: 'active',
    taskCount: 3
  },
  {
    id: 4,
    name: '赵师傅',
    workType: '电工',
    dailyWage: 300,
    status: 'active',
    taskCount: 0
  }
])

const batchMasterOptions = ref([
  {
    id: 1,
    name: '李师傅',
    workType: '电工',
    dailyWage: 300,
    status: 'active',
    taskCount: 2
  },
  {
    id: 2,
    name: '王师傅',
    workType: '水工',
    dailyWage: 280,
    status: 'active',
    taskCount: 1
  },
  {
    id: 3,
    name: '张师傅',
    workType: '安装工',
    dailyWage: 320,
    status: 'active',
    taskCount: 3
  },
  {
    id: 4,
    name: '赵师傅',
    workType: '电工',
    dailyWage: 300,
    status: 'active',
    taskCount: 0
  }
])

const dispatchRules = {
  assignedWorker: [{ required: true, message: '请选择分派师傅', trigger: 'change' }],
  estimatedDays: [{ required: true, message: '请输入预计用时', trigger: 'blur' }],
  startDate: [{ required: true, message: '请选择开始时间', trigger: 'change' }]
}

// 获取订单分类标签类型
const getOrderTypeTag = (type: string) => {
  const types = {
    '户内整改': 'warning',
    '一次挂表': 'success',
    '二次挂表': 'primary',
    '安装': 'primary',
    '维修': 'warning',
    '改造': 'success',
    '水电安装': 'primary',
    '二次安装': 'warning',
    '售后': 'info'
  }
  return types[type] || 'info'
}

// 获取状态标签类型
const getStatusTag = (status: string) => {
  const types = {
    '待分派': 'info',
    '进行中': 'warning',
    '已完成': 'success',
    '已取消': 'danger'
  }
  return types[status] || 'info'
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    orderNo: '',
    customerName: '',
    address: '',
    phone: '',
    contactPerson: '',
    orderType: '',
    status: '',
    assignedWorker: ''
  })
  handleSearch()
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 新增
const handleAdd = () => {
  ElMessage.info('新增订单功能待实现')
}

// 编辑
const handleEdit = (row: any) => {
  ElMessage.info('编辑订单功能待实现')
}

// 查看
const handleView = (row: any) => {
  ElMessage.info('查看订单详情功能待实现')
}

// 执行
const handleExecute = (row: any) => {
  // 跳转到订单执行页面并传递订单信息
  const orderInfo = {
    orderNo: row.orderNo,
    customerName: row.customerName,
    userNo: row.userNo,
    communityName: row.communityName,
    building: row.building,
    roomNo: row.roomNo,
    address: row.address,
    phone: row.phone,
    contactPerson: row.contactPerson,
    orderType: row.orderType,
    status: row.status,
    assignedWorker: row.assignedWorker,
    totalAmount: row.totalAmount,
    estimatedDays: row.estimatedDays,
    startDate: row.startDate,
    endDate: row.endDate
  }
  
  // 使用路由跳转并传递参数
  router.push({
    name: 'OrderExecute',
    query: { orderInfo: JSON.stringify(orderInfo) }
  })
}

// 分派
const handleDispatch = (row: any) => {
  Object.assign(dispatchForm, {
    orderNo: row.orderNo,
    customerName: row.customerName,
    userNo: row.userNo || 'YH' + row.orderNo.slice(-6),
    communityName: row.communityName || row.address?.split('区')[0] + '区',
    building: row.building || row.address?.split('区')[1]?.split('号')[0] + '栋',
    roomNo: row.roomNo || row.address?.split('号')[1]?.split('号')[0] || '101',
    phone: row.phone,
    contactPerson: row.contactPerson,
    orderType: row.orderType,
    status: row.status,
    assignedWorker: '',
    estimatedDays: 1,
    startDate: '',
    remark: ''
  })
  dispatchDialogVisible.value = true
}

// 录入工单
const handleEnterWorkOrder = (row: any) => {
  router.push({ name: 'OrderAssign', query: { id: row.id } })
}

// 搜索师傅
const searchMasters = () => {
  ElMessage.success('搜索师傅')
  console.log('搜索关键词:', masterSearch.value)
}

// 选择师傅
const selectMaster = (row: any) => {
  dispatchForm.assignedWorker = row.id
  ElMessage.success(`已选择师傅: ${row.name}`)
}

// 批量派单
const handleBatchDispatch = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要派单的订单')
    return
  }
  batchDispatchDialogVisible.value = true
}

// 批量派单搜索师傅
const searchBatchMasters = () => {
  ElMessage.success('搜索师傅')
  console.log('批量派单搜索关键词:', batchMasterSearch.value)
}

// 选择批量派单师傅
const selectBatchMaster = (row: any) => {
  batchDispatchForm.assignedWorker = row.id
  ElMessage.success(`已选择师傅: ${row.name}`)
}

// 保存
const handleSave = () => {
  ElMessage.success('保存成功')
  console.log('保存数据:', dispatchForm)
}

// 批量派单保存
const handleBatchDispatchSubmit = async () => {
  if (!batchDispatchForm.assignedWorker) {
    ElMessage.warning('请选择批量派单师傅')
    return
  }
  
  ElMessage.success('批量派单成功')
  batchDispatchDialogVisible.value = false
  loadData() // 刷新表格
}

// 删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除该订单吗？', '提示', {
      type: 'warning'
    })
    ElMessage.success('删除成功')
    loadData()
  } catch {
    // 用户取消
  }
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能待实现')
}

// 分派提交
const handleDispatchSubmit = async () => {
  if (!dispatchForm.assignedWorker) {
    ElMessage.warning('请选择分派师傅')
    return
  }
  
  ElMessage.success('分派成功')
  dispatchDialogVisible.value = false
  loadData()
}

// 打印
const handlePrint = () => {
  ElMessage.success('开始打印')
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

// 行点击
const handleRowClick = (row: any) => {
  highlightedRow.value = row.id
}

// 获取行类名
const getRowClassName = ({ row }: { row: any }) => {
  if (highlightedRow.value === row.id) {
    return 'highlighted-row'
  }
  return ''
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 调用真实API
    const response = await request.get('/api/loose-orders', {
      params: {
        page: pagination.page,
        pageSize: pagination.pageSize,
        ...searchForm
      }
    })
    
    if (response.success) {
      tableData.value = response.data.list || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 导入Excel相关函数
const beforeImportExcel = (file: File) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件')
  }
  return isExcel
}

const handleImportSuccess = (response: any) => {
  if (response && response.success && response.data && response.data.taskId) {
    importTaskId.value = response.data.taskId
    importing.value = true
    importDone.value = false
    importProgressPercent.value = 0
    importProgressStatus.value = 'active'
    importProgressMessage.value = '正在处理...'
    pollImportProgress()
  } else {
    ElMessage.error(response.message || '导入失败')
  }
}

const handleImportError = (error: any) => {
  console.error('导入失败:', error)
  ElMessage.error('导入失败，请检查文件格式')
}

const pollImportProgress = async () => {
  if (!importTaskId.value) return
  try {
    const res = await request.get('/api/import-progress/' + importTaskId.value)
    if (res && res.success && res.data) {
      const { total, current, successCount, fail, status, message, phaseProgress } = res.data
      if (total > 0) {
        importProgressPercent.value = phaseProgress || Math.floor((current / total) * 100)
      } else {
        importProgressPercent.value = 0
      }
      if (status === 'done') {
        importProgressPercent.value = 100
        importProgressStatus.value = 'success'
        importProgressMessage.value = message || '导入完成'
        importing.value = false
        importDone.value = true
        importTaskId.value = ''
        clearTimeout(importProgressTimer)
        loadData()
        ElMessage.success(importProgressMessage.value)
      } else if (status === 'error') {
        importProgressStatus.value = 'exception'
        importProgressMessage.value = message || '导入失败'
        importing.value = false
        importDone.value = true
        importTaskId.value = ''
        clearTimeout(importProgressTimer)
        ElMessage.error(importProgressMessage.value)
      } else {
        if (total === 0) {
          importProgressMessage.value = '正在解析文件...'
        } else {
          importProgressMessage.value = `已处理 ${current}/${total}，成功${successCount}条，失败${fail}条...`
        }
        importProgressStatus.value = 'active'
        importProgressTimer = setTimeout(pollImportProgress, 1000)
      }
    } else {
      importProgressStatus.value = 'exception'
      importProgressMessage.value = res.message || '进度查询失败'
      importing.value = false
      importDone.value = true
      importTaskId.value = ''
      clearTimeout(importProgressTimer)
      ElMessage.error(importProgressMessage.value)
    }
  } catch (error: any) {
    importProgressStatus.value = 'exception'
    importProgressMessage.value = error.message || '进度查询失败'
    importing.value = false
    importDone.value = true
    importTaskId.value = ''
    clearTimeout(importProgressTimer)
    ElMessage.error(importProgressMessage.value)
  }
}

const handleImportDialogClose = () => {
  if (importing.value) {
    ElMessageBox.confirm('正在导入中，确定要取消吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      clearTimeout(importProgressTimer)
      importDialogVisible.value = false
      importing.value = false
      importDone.value = false
      importTaskId.value = ''
      importProgressPercent.value = 0
      importProgressStatus.value = 'active'
      importProgressMessage.value = ''
    })
  } else {
    importDialogVisible.value = false
    if (importDone.value) {
      // 重置状态
      importing.value = false
      importDone.value = false
      importTaskId.value = ''
      importProgressPercent.value = 0
      importProgressStatus.value = 'active'
      importProgressMessage.value = ''
    }
  }
}

onMounted(() => {
  loadData()
})
</script> 

<style lang="scss" scoped>
.order-list {
  padding: 0;
  min-height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-bottom: 60px;
}

.list-card {
  flex: 1;
  border: none;
  box-shadow: none;
  background: #fff;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.search-section {
  padding: 0;
  border-bottom: 1px solid #e4e7ed;
  flex-shrink: 0;
}

  .search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
  }

.action-section {
  padding: 15px 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  .left-actions {
    display: flex;
    gap: 10px;
  }
  .right-actions {
    display: flex;
    gap: 10px;
    }
  }

  .table-container {
  width: 100%;
  min-width: 0;
  flex: 1 1 0%;
  padding: 0 20px;
  box-sizing: border-box;
}
:deep(.el-table) {
  width: 100%;
  /* min-width: 1400px; */
}

.pagination-section {
  padding: 15px 20px;
  border-top: 1px solid #e4e7ed;
      display: flex;
      justify-content: center;
  flex-shrink: 0;
  background: #fff;
  z-index: 1;
  position: relative;
  margin-bottom: 60px;
}

// 分派弹窗样式
.order-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;

  .info-item {
    display: flex;
    margin-bottom: 10px;
    line-height: 1.6;

    .label {
      width: 100px;
      font-weight: bold;
      color: #606266;
    }

    .value {
      flex: 1;
      color: #303133;
    }
  }
}

.master-selection {
  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #606266;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }

  .master-table {
    margin-bottom: 20px;
  }

  .remarks-section {
    margin-top: 15px;
  }
}

.dialog-footer {
  text-align: center;

  .el-button {
    margin: 0 5px;
  }
}

// 高亮行样式
:deep(.el-table__body tr.highlighted-row td) {
  background-color: #e6f7ff !important;
}

:deep(.el-table__body tr.highlighted-row.el-table__row--striped td) {
  background-color: #e6f7ff !important;
}

// 批量派单弹窗样式
.selected-orders-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 6px;
  border: 1px solid #b3d8ff;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #1890ff;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #b3d8ff;
  }

  .selected-orders-table {
    margin-bottom: 0;
  }
}

.batch-master-selection {
  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #606266;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }

  .master-table {
    margin-bottom: 20px;
  }

  .remarks-section {
    margin-top: 15px;
  }
}
</style> 