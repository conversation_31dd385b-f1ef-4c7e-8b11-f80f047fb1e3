<template>
  <div class="material-apply-container">
    <div class="form-container">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请单号" prop="applyNo">
              <el-input v-model="form.applyNo" placeholder="系统自动生成" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请日期" prop="applyDate">
              <el-date-picker
                v-model="form.applyDate"
                type="date"
                placeholder="选择申请日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请人" prop="applicant">
              <el-select
                v-model="form.applicant"
                placeholder="请选择申请人"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="item in applicantOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申领用途" prop="purpose">
              <el-select
                v-model="form.purpose"
                placeholder="请选择申领用途"
                style="width: 100%"
              >
                <el-option label="安检" value="安检" />
                <el-option label="维修" value="维修" />
                <el-option label="工程" value="工程" />
                <el-option label="商品售卖" value="商品售卖" />
                <el-option label="其它" value="其它" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关联订单" prop="orderNo">
              <el-select
                v-model="form.orderNo"
                placeholder="请选择关联订单"
                filterable
                style="width: 100%"
                :disabled="form.purpose !== '工程' && form.purpose !== '商品售卖'"
              >
                <el-option
                  v-for="item in orderOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        

      </el-form>
      
      <!-- 物料清单 -->
      <div class="material-list">
        <div class="list-header">
          <div class="header-actions">
            <el-button type="primary" @click="handleAddMaterial">
              添加物料
              <span v-if="materialList.length > 0" class="material-count">({{ materialList.length }})</span>
            </el-button>
            <el-button 
              v-if="materialList.length > 0" 
              type="danger" 
              size="small" 
              @click="handleClearMaterials"
            >
              清空清单
            </el-button>
          </div>
        </div>
        
                <div class="table-container">
          <el-table 
            v-if="materialList.length > 0"
            :data="materialList" 
            border 
            stripe
            v-loading="false"
            style="width: 100%;"
          >
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="companyCode" label="物料编码" width="150" />
            <el-table-column prop="name" label="物料名称"  />
            <el-table-column prop="clientCodes" label="甲料编码" width="120" />
            <el-table-column prop="specification" label="规格" width="150" />
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column prop="stockQuantity" label="库存" width="80" />
            <el-table-column prop="quantity" label="申请数量" width="150">
              <template #default="{ row }">
                <el-input-number
                  v-model="row.quantity"
                  :min="1"
                  :max="row.stockQuantity || 999999"
                  size="small"
                  style="width: 80px"
                  @change="handleQuantityChange"
                />
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="100">
              <template #default="{ $index }">
                <el-button type="danger" size="small" @click="handleRemoveMaterial($index)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 空状态提示 -->
          <div v-else class="empty-state">
            <el-empty description="暂无物料">
              <el-button type="primary" @click="handleAddMaterial">添加物料</el-button>
            </el-empty>
          </div>
        </div>
        
        <!-- 统一备注区域 -->
        <div v-if="materialList.length > 0" class="remarks-section">
          <el-form-item label="备注：" style="margin-bottom: 16px;">
            <el-input 
              v-model="unifiedRemarks" 
              type="textarea" 
              :rows="2" 
              placeholder="请输入备注，所有物料共用" 
              @input="handleUnifiedRemarksChange"
            />
          </el-form-item>
        </div>

      </div>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button type="success" @click="handleSaveAndPrint">保存并打印</el-button>
      </div>
    </div>
    
    <!-- 物料选择组件 -->
    <MaterialSelector
      v-model="materialDialogVisible"
      title="选择物料"
      :selected-ids="selectedMaterialIds"
      @selection-change="handleMaterialSelectionChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, watchEffect } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'
import MaterialSelector from '@/components/MaterialSelector.vue'

// 表单数据
const form = reactive({
  applyNo: '',
  applyDate: new Date(),
  applicant: '',
  purpose: '',
  orderNo: ''
})

// 申请人选项
const applicantOptions = ref([])

// 订单选项
const orderOptions = ref([
  { value: 'DD001', label: 'DD001 - 某小区燃气安装工程' },
  { value: 'DD002', label: 'DD002 - 某商场燃气管道改造' },
  { value: 'DD003', label: 'DD003 - 某工厂燃气设备安装' },
  { value: 'DD004', label: 'DD004 - 某住宅楼燃气入户工程' },
  { value: 'DD005', label: 'DD005 - 某商业街燃气管道铺设' }
])

// 表单验证规则
const rules = {
  applyDate: [{ required: true, message: '请选择申请日期', trigger: 'change' }],
  applicant: [{ required: true, message: '请选择申请人', trigger: 'change' }],
  purpose: [{ required: true, message: '请选择申领用途', trigger: 'change' }]
}

// 物料清单
const materialList = ref([])

// 物料选择对话框
const materialDialogVisible = ref(false)

// 统一备注
const unifiedRemarks = ref('')

// 表单引用
const formRef = ref()

// localStorage 键名
const MATERIAL_LIST_KEY = 'materialApply_materialList'
const FORM_DATA_KEY = 'materialApply_formData'
const REMARKS_KEY = 'materialApply_unifiedRemarks'

// 计算已选物料的ID列表
const selectedMaterialIds = computed(() => materialList.value.map(m => m.id))

// 保存物料清单到 localStorage
const saveMaterialList = () => {
  try {
    localStorage.setItem(MATERIAL_LIST_KEY, JSON.stringify(materialList.value))
  } catch (error) {
    console.error('保存物料清单失败:', error)
  }
}

// 保存统一备注到 localStorage
const saveUnifiedRemarks = () => {
  try {
    localStorage.setItem(REMARKS_KEY, unifiedRemarks.value)
  } catch (error) {
    console.error('保存统一备注失败:', error)
  }
}

// 从 localStorage 恢复物料清单
const restoreMaterialList = () => {
  try {
    const saved = localStorage.getItem(MATERIAL_LIST_KEY)
    if (saved) {
      materialList.value = JSON.parse(saved)
    }
  } catch (error) {
    console.error('恢复物料清单失败:', error)
    materialList.value = []
  }
}

// 从 localStorage 恢复统一备注
const restoreUnifiedRemarks = () => {
  try {
    const saved = localStorage.getItem(REMARKS_KEY)
    if (saved) {
      unifiedRemarks.value = saved
    }
  } catch (error) {
    console.error('恢复统一备注失败:', error)
    unifiedRemarks.value = ''
  }
}

// 保存表单数据到 localStorage
const saveFormData = () => {
  try {
    const formData = {
      applyDate: form.applyDate,
      applicant: form.applicant,
      purpose: form.purpose,
      orderNo: form.orderNo
    }
    localStorage.setItem(FORM_DATA_KEY, JSON.stringify(formData))
  } catch (error) {
    console.error('保存表单数据失败:', error)
  }
}

// 从 localStorage 恢复表单数据
const loadFormData = () => {
  try {
    const saved = localStorage.getItem(FORM_DATA_KEY)
    if (saved) {
      const formData = JSON.parse(saved)
      Object.assign(form, formData)
    }
  } catch (error) {
    console.error('恢复表单数据失败:', error)
  }
}

// 清除所有保存的数据
const clearSavedData = () => {
  try {
    localStorage.removeItem(MATERIAL_LIST_KEY)
    localStorage.removeItem(FORM_DATA_KEY)
    localStorage.removeItem(REMARKS_KEY)
  } catch (error) {
    console.error('清除保存数据失败:', error)
  }
}

// 加载员工列表
const loadEmployeesList = async () => {
  try {
    const response = await request.get('/api/employees/list', {
      params: {
        page: 1,
        pageSize: 1000, // 获取所有员工
        status: 1 // 只获取在职员工
      }
    })
    
    if (response.success) {
      const employees = response.data.list || []
      // 转换员工数据为选项格式
      const employeeOptions = employees.map((employee: any) => ({
        value: employee.id,
        label: `${employee.name} - ${employee.phone || '无电话'}`
      }))
      
      applicantOptions.value = employeeOptions
    } else {
      ElMessage.error('加载员工列表失败')
    }
  } catch (error) {
    console.error('加载员工列表失败:', error)
    ElMessage.error('加载员工列表失败')
  }
}

// 生成申请单号
const generateApplyNo = () => {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  form.applyNo = `LY${year}${month}${day}${random}`
}

// 添加物料
const handleAddMaterial = () => {
  materialDialogVisible.value = true
}

// 清空物料清单
const handleClearMaterials = () => {
  ElMessageBox.confirm('确定要清空所有物料吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    materialList.value = []
    // 自动保存物料清单
    saveMaterialList()
    ElMessage.success('已清空物料清单')
  }).catch(() => {
    // 用户取消
  })
}

// 删除物料
const handleRemoveMaterial = (index: number) => {
  materialList.value.splice(index, 1)
  // 自动保存物料清单
  saveMaterialList()
}

// 处理数量变化
const handleQuantityChange = () => {
  console.log('数量变化，当前物料清单:', materialList.value)
  // 自动保存物料清单
  saveMaterialList()
}

// 处理统一备注变化
const handleUnifiedRemarksChange = () => {
  console.log('统一备注变化:', unifiedRemarks.value)
  // 自动保存统一备注
  saveUnifiedRemarks()
}

// 处理物料选择变化（实时同步）
const handleMaterialSelectionChange = (materials: any[]) => {
  console.log('物料选择变化:', materials)
  
  // 保留现有物料的数量和备注信息
  const existingMaterials = materialList.value.reduce((acc, item) => {
    acc[item.id] = {
      quantity: item.quantity,
      remarks: item.remarks || ''
    }
    return acc
  }, {})
  
  // 更新物料清单，保持数量和备注信息
  materialList.value = materials.map(material => ({
    ...material,
    quantity: existingMaterials[material.id]?.quantity || 1,
    remarks: existingMaterials[material.id]?.remarks || ''
  }))
  
  console.log('更新后的物料清单:', materialList.value)
  
  // 自动保存物料清单
  saveMaterialList()
}

// 监听物料选择器弹窗状态
watch(materialDialogVisible, (newVal, oldVal) => {
  if (oldVal && !newVal) {
    // 弹窗关闭时检查物料清单
    if (materialList.value.length === 0) {
      ElMessage.info('您还没有选择任何物料，请点击"添加物料"按钮选择物料')
    }
  }
})

// 保存
const handleSave = async () => {
  // 验证表单
  try {
    console.log('开始验证表单，当前表单数据:', form)
    await formRef.value?.validate()
    console.log('表单验证通过')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请完善表单信息')
    return
  }
  
  // 验证物料清单
  if (materialList.value.length === 0) {
    ElMessage.warning('请至少添加一个物料')
    return
  }
  
  // 验证物料数量
  const invalidMaterials = materialList.value.filter(item => !item.quantity || item.quantity <= 0)
  if (invalidMaterials.length > 0) {
    ElMessage.warning('请检查物料申请数量')
    return
  }
  
  // 验证库存
  const insufficientMaterials = materialList.value.filter(item => 
    item.quantity > (item.stockQuantity || 0)
  )
  if (insufficientMaterials.length > 0) {
    ElMessage.warning('部分物料库存不足，请检查申请数量')
    return
  }
  
  try {
    // 准备提交数据
    const submitData = {
      applyNo: form.applyNo,
      applyDate: form.applyDate ? new Date(form.applyDate).toISOString().split('T')[0] : '',
      applicantId: form.applicant,
      purpose: form.purpose,
      orderNo: form.orderNo || null,
      remarks: unifiedRemarks.value,
      materials: materialList.value.map(item => ({
        materialId: item.id,
        quantity: item.quantity,
        remarks: item.remarks || ''
      }))
    }
    
    // 调用API保存
    const response = await request.post('/api/material-applications', submitData)
    
    if (response.success) {
      ElMessage.success('领料申请保存成功')
      materialDialogVisible.value = false // 关闭物料选择器弹窗
      clearSavedData() // 清除本地存储的数据
      handleReset()
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error) {
    console.error('保存领料申请失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

// 保存并打印
const handleSaveAndPrint = async () => {
  // 验证表单
  try {
    await formRef.value?.validate()
  } catch (error) {
    ElMessage.error('请完善表单信息')
    return
  }
  
  // 验证物料清单
  if (materialList.value.length === 0) {
    ElMessage.warning('请至少添加一个物料')
    return
  }
  
  // 验证物料数量
  const invalidMaterials = materialList.value.filter(item => !item.quantity || item.quantity <= 0)
  if (invalidMaterials.length > 0) {
    ElMessage.warning('请检查物料申请数量')
    return
  }
  
  // 验证库存
  const insufficientMaterials = materialList.value.filter(item => 
    item.quantity > (item.stockQuantity || 0)
  )
  if (insufficientMaterials.length > 0) {
    ElMessage.warning('部分物料库存不足，请检查申请数量')
    return
  }
  
  try {
    // 准备提交数据
    const submitData = {
      applyNo: form.applyNo,
      applyDate: form.applyDate ? new Date(form.applyDate).toISOString().split('T')[0] : '',
      applicantId: form.applicant,
      purpose: form.purpose,
      orderNo: form.orderNo || null,
      remarks: unifiedRemarks.value,
      materials: materialList.value.map(item => ({
        materialId: item.id,
        quantity: item.quantity,
        remarks: item.remarks || ''
      }))
    }
    
    // 调用API保存
    const response = await request.post('/api/material-applications', submitData)
    
    if (response.success) {
      ElMessage.success('领料申请保存成功')
      ElMessage.info('打印功能待实现')
      materialDialogVisible.value = false // 关闭物料选择器弹窗
      clearSavedData() // 清除本地存储的数据
      handleReset()
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error) {
    console.error('保存领料申请失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

// 重置
const handleReset = () => {
  Object.assign(form, {
    applyNo: '',
    applyDate: new Date(),
    applicant: '',
    purpose: '',
    orderNo: ''
  })
  materialList.value = []
  unifiedRemarks.value = ''
  generateApplyNo()
  // 清除本地存储的数据
  clearSavedData()
}

onMounted(() => {
  generateApplyNo()
  loadEmployeesList()
  
  // 恢复保存的数据
  loadFormData()
  restoreMaterialList()
  restoreUnifiedRemarks()
  
  console.log('页面初始化完成，已恢复保存的数据')
  console.log('当前时间戳:', Date.now()) // 添加时间戳强制重新编译
})

// 监听表单数据变化，自动保存
watchEffect(() => {
  // 延迟保存，避免频繁保存
  const timer = setTimeout(() => {
    saveFormData()
  }, 1000)
  
  return () => clearTimeout(timer)
})
</script>

<style lang="scss" scoped>
.material-apply-container {
  display: flex;
  flex-direction: column;
}

.form-container {
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.material-list {
  display: flex;
  flex-direction: column;
  margin-top: 20px;
  
  .list-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 20px;
    
    .header-actions {
      display: flex;
      gap: 10px;
      align-items: center;
    }
  }
  
  .table-container {
    display: flex;
    flex-direction: column;
  }
  
  .el-table {
    display: flex;
    flex-direction: column;
    
    :deep(.el-table__body-wrapper) {
      overflow-y: auto;
    }
    
    :deep(.el-table__header-wrapper) {
      flex-shrink: 0;
    }
    
    :deep(.el-table__inner-wrapper) {
      display: flex;
      flex-direction: column;
    }
  }
  

}

.form-actions {
  margin-top: 20px;
  text-align: center;
  
  .el-button {
    margin: 0 10px;
  }
}

.material-search {
  margin-bottom: 20px;
}

.material-count {
  margin-left: 5px;
  font-size: 12px;
  opacity: 0.8;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px 0;
}

.remarks-section {
  margin-top: 20px;
}
</style>