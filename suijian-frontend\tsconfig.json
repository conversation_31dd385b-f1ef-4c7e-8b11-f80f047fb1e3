{"include": ["env.d.ts", "src/**/*", "src/**/*.vue"], "compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["element-plus/global", "node"], "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve"}, "references": [{"path": "./tsconfig.node.json"}]}