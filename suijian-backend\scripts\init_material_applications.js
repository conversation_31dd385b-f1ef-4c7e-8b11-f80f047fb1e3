const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 数据库文件路径
const dbPath = path.join(__dirname, '../database/suijian.db');

// 检查数据库文件是否存在
if (!fs.existsSync(dbPath)) {
    console.error('数据库文件不存在:', dbPath);
    process.exit(1);
}

// 创建数据库连接
const db = new sqlite3.Database(dbPath);

// SQL文件路径
const sqlFilePath = path.join(__dirname, '../database/create_material_applications.sql');

// 读取SQL文件
const sql = fs.readFileSync(sqlFilePath, 'utf8');

console.log('开始创建领料申请相关表...');

// 执行SQL语句
db.exec(sql, (err) => {
    if (err) {
        console.error('创建表失败:', err);
        process.exit(1);
    } else {
        console.log('领料申请相关表创建成功！');

        // 验证表是否创建成功
        db.all("SELECT name FROM sqlite_master WHERE type='table' AND name IN ('material_applications', 'material_application_items')", (err, rows) => {
            if (err) {
                console.error('验证表创建失败:', err);
            } else {
                console.log('已创建的表:', rows.map(row => row.name));
            }
            db.close();
        });
    }
});