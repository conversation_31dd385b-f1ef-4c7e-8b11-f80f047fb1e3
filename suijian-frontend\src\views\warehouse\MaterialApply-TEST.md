# 物料选择器测试说明

## 测试场景

### 场景1：搜索后重新打开弹窗
1. 打开领料申请页面
2. 点击"添加物料"
3. 在搜索框中搜索特定物料（如"注塑"）
4. 选择搜索结果中的物料
5. 关闭弹窗
6. 再次点击"添加物料"
7. **预期结果**：应该能看到之前选择的物料仍然被选中

### 场景2：跨页面选择
1. 打开物料选择器
2. 在第一页选择几个物料
3. 翻到第二页，再选择几个物料
4. 关闭弹窗
5. 重新打开弹窗
6. **预期结果**：所有页面选择的物料都应该保持选中状态

### 场景3：搜索后跨页面选择
1. 打开物料选择器
2. 搜索特定物料并选择
3. 清空搜索，翻到其他页面选择物料
4. 关闭弹窗
5. 重新打开弹窗
6. **预期结果**：搜索选择的物料和其他页面选择的物料都应该保持选中状态

## 修复内容

### 1. 选择状态恢复逻辑优化
- 当当前页面没有找到已选择的物料时，从 `targetSelectedIds` 重建完整的 `selectedMaterials`
- 确保跨页面的选择状态能够正确恢复

### 2. 弹窗关闭时不清空选择
- 保持 `initialSelectedIds` 不被清空
- 确保重新打开弹窗时能正确恢复选择状态

### 3. 搜索后选择状态保持
- 优化了搜索后重新打开弹窗的选择恢复逻辑
- 确保搜索选择的物料在重新打开时仍然保持选中

## 调试信息

在浏览器控制台中可以看到详细的调试信息：
- 弹窗打开时的 `selectedIds`
- 选择状态恢复过程
- 当前页面物料数量和选中数量
- 其他页面选择数量

## 验证方法

1. 打开浏览器开发者工具的控制台
2. 按照测试场景操作
3. 观察控制台输出的调试信息
4. 验证选择状态是否正确恢复 