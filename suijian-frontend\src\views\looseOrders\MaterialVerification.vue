<template>
  <div class="material-verification">
    <!-- 周期选择区域 -->
    <el-card class="period-card">
      <div class="period-section">
        <div class="period-row">
          <span class="period-label">月份选择:</span>
          <el-date-picker
            v-model="selectedMonth"
            type="month"
            placeholder="选择月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            @change="handleMonthChange"
          />
        </div>
        <div class="period-row">
          <span class="period-label">周期选择:</span>
          <div class="period-buttons">
            <el-button 
              v-for="period in periods" 
              :key="period.key"
              :type="selectedPeriod === period.key ? 'primary' : 'default'"
              @click="handlePeriodChange(period.key)"
            >
              {{ period.label }}
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 状态显示区域 -->
    <el-card class="status-card">
      <div class="status-section">
        <div class="status-row">
          <span class="status-label">当前状态:</span>
          <div class="status-tags">
            <el-tag 
              v-for="status in statusList" 
              :key="status.key"
              :type="getStatusType(status.key)"
              :effect="currentStatus === status.key ? 'dark' : 'plain'"
            >
              {{ status.label }}
            </el-tag>
          </div>
        </div>
        <div class="status-row">
          <span class="status-label">状态说明:</span>
          <span class="status-desc">未核对：未与甲方核对 | 未平料：未与仓库平料</span>
        </div>
        <div class="action-buttons">
          <el-button type="success" @click="handleDownloadExcel">
            <el-icon><Download /></el-icon>
            下载Excel
          </el-button>
          <el-button type="primary" @click="handleUploadExcel">
            <el-icon><Upload /></el-icon>
            上传核对Excel
          </el-button>
          <el-button type="warning" @click="handleConfirmArchive">
            <el-icon><Check /></el-icon>
            确认平帐归档
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 标签页区域 -->
    <el-card class="tabs-card">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="未核对" name="unverified">
          <div class="tab-content">
            <el-table :data="tableData" style="width: 100%" border v-loading="loading">
              <el-table-column prop="id" label="序号" width="80" />
              <el-table-column prop="orderNo" label="工单号" width="120" />
              <el-table-column prop="customerName" label="客户姓名" width="100" />
              <el-table-column prop="address" label="地址" min-width="200" />
              <el-table-column prop="orderType" label="工单类型" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.orderType === '安检' ? 'success' : 'warning'">
                    {{ row.orderType }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间" width="120" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getStatusType(row.status)">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                  <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
                  <el-button type="success" size="small" @click="handleVerify(row)">核对</el-button>
                  <el-button type="info" size="small" @click="handleDownload(row)">下载</el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <!-- 分页 -->
            <div class="pagination-section">
              <el-pagination
                v-model:current-page="pagination.page"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="pagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="未平料" name="unbalanced">
          <div class="tab-content">
            <el-table :data="tableData" style="width: 100%" border v-loading="loading">
              <el-table-column prop="id" label="序号" width="80" />
              <el-table-column prop="orderNo" label="工单号" width="120" />
              <el-table-column prop="customerName" label="客户姓名" width="100" />
              <el-table-column prop="address" label="地址" min-width="200" />
              <el-table-column prop="orderType" label="工单类型" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.orderType === '安检' ? 'success' : 'warning'">
                    {{ row.orderType }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间" width="120" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getStatusType(row.status)">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                  <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
                  <el-button type="warning" size="small" @click="handleBalance(row)">平料</el-button>
                  <el-button type="info" size="small" @click="handleDownload(row)">下载</el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <!-- 分页 -->
            <div class="pagination-section">
              <el-pagination
                v-model:current-page="pagination.page"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="pagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="已平料" name="balanced">
          <div class="tab-content">
            <el-table :data="tableData" style="width: 100%" border v-loading="loading">
              <el-table-column prop="id" label="序号" width="80" />
              <el-table-column prop="orderNo" label="工单号" width="120" />
              <el-table-column prop="customerName" label="客户姓名" width="100" />
              <el-table-column prop="address" label="地址" min-width="200" />
              <el-table-column prop="orderType" label="工单类型" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.orderType === '安检' ? 'success' : 'warning'">
                    {{ row.orderType }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间" width="120" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getStatusType(row.status)">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                  <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
                  <el-button type="info" size="small" @click="handleDetail(row)">详情</el-button>
                  <el-button type="info" size="small" @click="handleDownload(row)">下载</el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <!-- 分页 -->
            <div class="pagination-section">
              <el-pagination
                v-model:current-page="pagination.page"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="pagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="全部" name="all">
          <div class="tab-content">
            <el-table :data="tableData" style="width: 100%" border v-loading="loading">
              <el-table-column prop="id" label="序号" width="80" />
              <el-table-column prop="orderNo" label="工单号" width="120" />
              <el-table-column prop="customerName" label="客户姓名" width="100" />
              <el-table-column prop="address" label="地址" min-width="200" />
              <el-table-column prop="orderType" label="工单类型" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.orderType === '安检' ? 'success' : 'warning'">
                    {{ row.orderType }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间" width="120" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getStatusType(row.status)">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                  <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
                  <el-button v-if="row.status === 'unverified'" type="success" size="small" @click="handleVerify(row)">核对</el-button>
                  <el-button v-if="row.status === 'unbalanced'" type="warning" size="small" @click="handleBalance(row)">平料</el-button>
                  <el-button v-if="row.status === 'balanced'" type="info" size="small" @click="handleDetail(row)">详情</el-button>
                  <el-button type="info" size="small" @click="handleDownload(row)">下载</el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <!-- 分页 -->
            <div class="pagination-section">
              <el-pagination
                v-model:current-page="pagination.page"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="pagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 上传Excel弹窗 -->
    <el-dialog v-model="uploadDialogVisible" title="上传核对Excel" width="500px">
      <el-upload
        class="upload-demo"
        drag
        action="#"
        :auto-upload="false"
        :on-change="handleFileChange"
        accept=".xlsx,.xls"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只能上传 xlsx/xls 文件
          </div>
        </template>
      </el-upload>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleUploadConfirm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, Upload, UploadFilled, Check } from '@element-plus/icons-vue'
import request from '@/utils/request'

// 选中的月份
const selectedMonth = ref('')

// 选中的周期
const selectedPeriod = ref('1-7')

// 当前状态
const currentStatus = ref('unverified')

// 活跃标签页
const activeTab = ref('unverified')

// 加载状态
const loading = ref(false)

// 上传弹窗
const uploadDialogVisible = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 周期选项
const periods = [
  { key: '1-7', label: '1-7' },
  { key: '8-15', label: '8-15' },
  { key: '16-23', label: '16-23' },
  { key: '24-end', label: '24-月底' }
]

// 状态列表
const statusList = [
  { key: 'unverified', label: '未核对' },
  { key: 'unbalanced', label: '未平料' },
  { key: 'balanced', label: '已平料' }
]

// 移除mock数据，使用真实API

// 表格数据
const tableData = ref([])

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    // 调用真实API
    const response = await request.get('/api/loose-orders/material-verification', {
      params: {
        page: pagination.page,
        pageSize: pagination.pageSize,
        month: selectedMonth.value,
        period: selectedPeriod.value,
        status: activeTab.value === 'all' ? undefined : activeTab.value
      }
    })
    
    if (response.success) {
      tableData.value = response.data.list || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 月份改变
const handleMonthChange = (value: string) => {
  selectedMonth.value = value
  fetchData()
}

// 周期改变
const handlePeriodChange = (period: string) => {
  selectedPeriod.value = period
  fetchData()
}

// 标签页点击
const handleTabClick = () => {
  pagination.page = 1
  fetchData()
}

// 下载Excel
const handleDownloadExcel = () => {
  ElMessage.success('下载Excel成功')
}

// 上传Excel
const handleUploadExcel = () => {
  uploadDialogVisible.value = true
}

// 文件改变
const handleFileChange = (file: any) => {
  console.log('选择的文件:', file)
}

// 确认上传
const handleUploadConfirm = () => {
  ElMessage.success('上传成功')
  uploadDialogVisible.value = false
  fetchData()
}

// 查看
const handleView = (row: any) => {
  ElMessage.info(`查看工单: ${row.orderNo}`)
}

// 核对
const handleVerify = (row: any) => {
  ElMessageBox.confirm('确定要核对这个工单吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('核对成功')
    fetchData()
  })
}

// 平料
const handleBalance = (row: any) => {
  ElMessageBox.confirm('确定要平料这个工单吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('平料成功')
    fetchData()
  })
}

// 确认平帐归档
const handleConfirmArchive = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要确认平帐归档吗？此操作将完成当前月份的平帐归档，操作后不可撤销。', 
      '确认平帐归档', 
      {
        confirmButtonText: '确定归档',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )
    
    // 显示加载状态
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('平帐归档确认成功')
    fetchData()
  } catch (error) {
    // 用户取消操作，不需要显示错误信息
    if (error !== 'cancel') {
      ElMessage.error('平帐归档确认失败')
    }
  } finally {
    loading.value = false
  }
}

// 详情
const handleDetail = (row: any) => {
  ElMessage.info(`查看详情: ${row.orderNo}`)
}

// 下载
const handleDownload = (row: any) => {
  ElMessage.success(`下载工单: ${row.orderNo}`)
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  fetchData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchData()
}

// 获取状态类型
const getStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    unverified: 'warning',
    unbalanced: 'danger',
    balanced: 'success'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    unverified: '未核对',
    unbalanced: '未平料',
    balanced: '已平料'
  }
  return statusMap[status] || status
}

// 初始化
onMounted(() => {
  // 设置默认月份为当前月份
  const now = new Date()
  selectedMonth.value = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
  fetchData()
})
</script>

<style scoped lang="scss">
.material-verification {
  padding: 20px;
  min-height: calc(100vh - 200px);
  overflow: hidden;
  padding-bottom: 60px;

  .period-card {
    margin-bottom: 20px;
    border: none;
    box-shadow: none;
    background: #fff;

    .period-section {
      .period-row {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }

        .period-label {
          width: 100px;
          font-weight: bold;
          color: #303133;
        }

        .period-buttons {
          display: flex;
          gap: 10px;
        }
      }
    }
  }

  .status-card {
    margin-bottom: 20px;
    border: none;
    box-shadow: none;
    background: #fff;

    .status-section {
      .status-row {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }

        .status-label {
          width: 100px;
          font-weight: bold;
          color: #303133;
        }

        .status-tags {
          display: flex;
          gap: 10px;
        }

        .status-desc {
          color: #606266;
          font-size: 14px;
        }

        .action-buttons {
          display: flex;
          gap: 10px;
        }
      }
    }
  }

  .tabs-card {
    border: none;
    box-shadow: none;
    background: #fff;
    display: flex;
    flex-direction: column;
    min-height: 0;

    .tab-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
    }

    .pagination-section {
      padding: 20px 0;
      border-top: 1px solid #ebeef5;
      background: #fff;
      z-index: 10;
      position: relative;
      margin-bottom: 20px;
      display: flex;
      justify-content: center;
    }
  }

  :deep(.el-table) {
    min-width: 1400px;
    width: 100%;
  }
}
</style> 