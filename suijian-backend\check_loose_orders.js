const { query } = require('./src/config/database');

async function checkLooseOrders() {
    try {
        console.log('检查loose_orders表...');

        // 检查表是否存在
        const tableCheck = await query("SELECT name FROM sqlite_master WHERE type='table' AND name='loose_orders'");
        console.log('表存在:', tableCheck.length > 0);

        if (tableCheck.length > 0) {
            // 获取表结构
            const structure = await query("PRAGMA table_info(loose_orders)");
            console.log('表结构:', structure);

            // 获取数据总数
            const countResult = await query("SELECT COUNT(*) as total FROM loose_orders");
            console.log('数据总数:', countResult[0].total);

            // 获取前5条数据
            const sampleData = await query("SELECT * FROM loose_orders LIMIT 5");
            console.log('示例数据:', sampleData);
        }

    } catch (error) {
        console.error('检查失败:', error);
    }
}

checkLooseOrders();