#!/usr/bin/env node

/**
 * 测试Excel文件格式修复
 */

const fs = require('fs');
const path = require('path');
const ExcelJS = require('exceljs');

/**
 * 创建测试Excel文件
 */
function createTestExcelFile(filePath) {
    console.log(`📁 创建测试Excel文件: ${filePath}`);

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('散户订单');

    // 添加表头
    const headers = [
        '工单号', '工单类型', '工单状态', '工单来源', '工单标题',
        '工单内容', '创建人', '创建时间'
    ];
    worksheet.addRow(headers);

    // 添加测试数据
    const testData = [
        ['WO001', '维修', '待处理', '系统', '设备故障', '设备无法启动', '张三', '2025-08-08'],
        ['WO002', '安装', '进行中', '项目', '新设备安装', '安装新设备', '李四', '2025-08-08'],
        ['WO003', '维护', '已完成', '系统', '定期维护', '完成定期维护', '王五', '2025-08-08']
    ];

    testData.forEach(row => {
        worksheet.addRow(row);
    });

    // 确保目录存在
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }

    // 保存文件
    return workbook.xlsx.writeFile(filePath);
}

/**
 * 测试Excel文件读取
 */
async function testExcelReading(filePath) {
    console.log('\n🧪 测试Excel文件读取...');

    try {
        const ExcelUtil = require('../src/utils/excel');

        // 测试读取工作簿
        console.log('📖 读取工作簿...');
        const workbook = await ExcelUtil.readWorkbook(filePath);
        console.log('✅ 工作簿读取成功');
        console.log('工作表数量:', workbook.worksheets.length);

        // 测试读取工作表
        console.log('📋 读取工作表...');
        const worksheet = workbook.worksheets[0];
        console.log('工作表名称:', worksheet.name);

        // 测试转换为JSON
        console.log('🔄 转换为JSON...');
        const data = ExcelUtil.sheet_to_json(worksheet, { header: 1 });
        console.log('✅ JSON转换成功');
        console.log('数据行数:', data.length);
        console.log('第一行数据:', data[0]);

        return true;
    } catch (error) {
        console.error('❌ Excel文件读取失败:', error.message);
        return false;
    }
}

/**
 * 测试文件格式验证
 */
async function testFileValidation() {
    console.log('\n🔍 测试文件格式验证...');

    // 测试无效文件
    const invalidFilePath = path.join(__dirname, 'invalid_file.txt');
    fs.writeFileSync(invalidFilePath, 'This is not an Excel file');

    try {
        const ExcelUtil = require('../src/utils/excel');
        await ExcelUtil.readWorkbook(invalidFilePath);
        console.log('❌ 应该抛出错误但没有');
    } catch (error) {
        console.log('✅ 正确捕获了文件格式错误:', error.message);
    }

    // 清理测试文件
    if (fs.existsSync(invalidFilePath)) {
        fs.unlinkSync(invalidFilePath);
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🧪 Excel文件格式修复测试');
    console.log('='.repeat(50));

    // 创建测试Excel文件
    const testFilePath = path.join(__dirname, 'test_excel_fix.xlsx');
    await createTestExcelFile(testFilePath);
    console.log('✅ 测试Excel文件创建完成');

    // 测试Excel文件读取
    const readingSuccess = await testExcelReading(testFilePath);
    if (!readingSuccess) {
        console.error('❌ Excel文件读取测试失败');
        process.exit(1);
    }

    // 测试文件格式验证
    await testFileValidation();

    // 清理测试文件
    if (fs.existsSync(testFilePath)) {
        fs.unlinkSync(testFilePath);
        console.log('🧹 清理测试文件完成');
    }

    console.log('\n✅ 所有测试通过！');
    console.log('💡 现在可以正常上传Excel文件了');
}

// 运行测试
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { createTestExcelFile, testExcelReading, testFileValidation };