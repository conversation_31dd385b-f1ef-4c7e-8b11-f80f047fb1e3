const express = require('express');
const router = express.Router();
const communitiesController = require('../controllers/communitiesController');
const { authMiddleware } = require('../middleware/auth');

// 获取小区列表
router.get('/', [authMiddleware], communitiesController.getCommunities);

// 创建小区
router.post('/', [authMiddleware], communitiesController.createCommunity);

// 更新小区
router.put('/:id', [authMiddleware], communitiesController.updateCommunity);

// 删除小区
router.delete('/:id', [authMiddleware], communitiesController.deleteCommunity);

// 获取小区详情
router.get('/:id', [authMiddleware], communitiesController.getCommunityById);

// 测试地址解析
router.post('/test-parse', [authMiddleware], communitiesController.testAddressParse);

// 批量导入小区
router.post('/import', [authMiddleware], communitiesController.importCommunities);

module.exports = router;