<template>
  <div class="project-progress-container">
    <el-card class="main-card">
      
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
        <!-- 工程选择 -->
        <el-row :gutter="20" class="form-section">
          <el-col :span="24">
            <div class="section-title">
              <el-icon><Tools /></el-icon>
              工程选择
            </div>
          </el-col>
          <el-col :span="24">
            <el-form-item label="选择工程:" prop="projectId">
              <el-select 
                v-model="formData.projectId" 
                placeholder="请选择要推进的工程" 
                style="width: 100%"
                @change="onProjectChange"
              >
                <el-option
                  v-for="project in projectList"
                  :key="project.id"
                  :label="`${project.name} (${project.status})`"
                  :value="project.id"
                  :disabled="project.status !== '在建'"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 进度信息 -->
        <el-row :gutter="20" class="form-section" v-if="selectedProject">
          <el-col :span="24">
            <div class="section-title">
              <el-icon><Document /></el-icon>
              当前工程信息
            </div>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工程名称:">
              <el-input v-model="selectedProject.name" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="当前状态:">
              <el-tag :type="getStatusType(selectedProject.status)">
                {{ selectedProject.status }}
              </el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="当前进度:">
              <el-progress :percentage="selectedProject.progress" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开始时间:">
              <el-input v-model="selectedProject.startDate" readonly />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 推进信息 -->
        <el-row :gutter="20" class="form-section" v-if="selectedProject">
          <el-col :span="24">
            <div class="section-title">
              <el-icon><EditPen /></el-icon>
              推进信息
            </div>
          </el-col>
          <el-col :span="12">
            <el-form-item label="完成进度:" prop="completionProgress">
              <el-input-number
                v-model="formData.completionProgress"
                :min="selectedProject.progress"
                :max="100"
                :step="5"
                style="width: 100%"
              />
              <span style="margin-left: 10px;">%</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="完成情况:" prop="completionDescription">
              <el-input
                v-model="formData.completionDescription"
                type="textarea"
                :rows="4"
                placeholder="请详细描述本次推进完成的工作内容、质量情况等"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="存在问题:" prop="existingIssues">
              <el-input
                v-model="formData.existingIssues"
                type="textarea"
                :rows="3"
                placeholder="请描述当前存在的问题、困难或需要协调解决的事项（如无问题可留空）"
                maxlength="300"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 人员列表 -->
        <el-row :gutter="20" class="form-section" v-if="selectedProject">
          <el-col :span="24">
            <div class="section-title">
              <el-icon><User /></el-icon>
              人员列表
            </div>
          </el-col>
          <el-col :span="24">
            <el-table :data="formData.personnelList" border style="width: 100%">
              <el-table-column prop="workType" label="工种" width="150">
                <template #default="{ row, $index }">
                  <el-select v-model="row.workType" placeholder="选择工种">
                    <el-option label="项目经理" value="项目经理" />
                    <el-option label="技术员" value="技术员" />
                    <el-option label="施工员" value="施工员" />
                    <el-option label="安全员" value="安全员" />
                    <el-option label="质检员" value="质检员" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="dailyWage" label="工价(元/天)" width="150">
                <template #default="{ row, $index }">
                  <el-input-number v-model="row.dailyWage" :min="0" :step="50" />
                </template>
              </el-table-column>
              <el-table-column prop="days" label="天数" width="120">
                <template #default="{ row, $index }">
                  <el-input-number v-model="row.days" :min="0" :step="0.5" />
                </template>
              </el-table-column>
              <el-table-column prop="totalAmount" label="总金额" width="150">
                <template #default="{ row }">
                  ¥{{ (row.dailyWage * row.days).toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template #default="{ $index }">
                  <el-button type="danger" size="small" @click="removePersonnel($index)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 10px;">
              <el-button type="primary" @click="addPersonnel">添加人员</el-button>
            </div>
          </el-col>
        </el-row>

        <!-- 物料使用列表 -->
        <el-row :gutter="20" class="form-section" v-if="selectedProject">
          <el-col :span="24">
            <div class="section-title">
              <el-icon><Box /></el-icon>
              物料使用列表
            </div>
          </el-col>
          <el-col :span="24">
            <el-table :data="formData.materialList" border style="width: 100%">
              <el-table-column prop="companyMaterialCode" label="公司物料编码" width="120">
                <template #default="{ row }">
                  <span>{{ row.companyMaterialCode }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="materialCode" label="甲料编码" width="120">
                <template #default="{ row }">
                  <span>{{ row.materialCode }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="materialName" label="物料名称" width="150">
                <template #default="{ row }">
                  <span>{{ row.materialName }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="model" label="型号" width="120">
                <template #default="{ row }">
                  <span>{{ row.model }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="specification" label="规格" width="120">
                <template #default="{ row }">
                  <span>{{ row.specification }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="unit" label="单位" width="80">
                <template #default="{ row }">
                  <span>{{ row.unit }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="数量" width="120">
                <template #default="{ row }">
                  <el-input-number v-model="row.quantity" :min="0" :step="1" style="width: 100%" />
                </template>
              </el-table-column>
              <el-table-column prop="laborPrice" label="人工单价" width="120">
                <template #default="{ row }">
                  <el-input-number v-model="row.laborPrice" :min="0" :step="0.01" :precision="2" style="width: 100%" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template #default="{ $index }">
                  <el-button type="danger" size="small" @click="removeMaterial($index)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 10px;">
              <el-button type="primary" @click="addMaterial">添加物料</el-button>
            </div>
          </el-col>
        </el-row>

        <!-- 其它费用列表 -->
        <el-row :gutter="20" class="form-section" v-if="selectedProject">
          <el-col :span="24">
            <div class="section-title">
              <el-icon><Money /></el-icon>
              其它费用列表
            </div>
          </el-col>
          <el-col :span="24">
            <el-table :data="formData.otherCostList" border style="width: 100%">
              <el-table-column prop="name" label="名称" width="200">
                <template #default="{ row }">
                  <span>{{ row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="数量" width="120">
                <template #default="{ row }">
                  <el-input-number v-model="row.quantity" :min="0" :step="1" style="width: 100%" />
                </template>
              </el-table-column>
              <el-table-column prop="unitPrice" label="单价" width="120">
                <template #default="{ row }">
                  <el-input-number v-model="row.unitPrice" :min="0" :step="0.01" :precision="2" style="width: 100%" />
                </template>
              </el-table-column>
              <el-table-column prop="totalPrice" label="总价" width="120">
                <template #default="{ row }">
                  <span>¥{{ (row.quantity * row.unitPrice).toFixed(2) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="remarks" label="备注" min-width="150">
                <template #default="{ row }">
                  <span>{{ row.remarks }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template #default="{ $index }">
                  <el-button type="danger" size="small" @click="removeOtherCost($index)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 10px;">
              <el-button type="primary" @click="addOtherCost">添加费用</el-button>
            </div>
          </el-col>
        </el-row>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button 
            type="primary" 
            size="large"
            :loading="submitLoading"
            @click="handleSubmit"
          >
            <el-icon><Check /></el-icon>
            提交推进
          </el-button>
          <el-button 
            type="success" 
            size="large"
            @click="handleSave"
          >
            <el-icon><DocumentAdd /></el-icon>
            保存草稿
          </el-button>
          <el-button 
            size="large"
            @click="handleCancel"
          >
            <el-icon><Close /></el-icon>
            取消
          </el-button>
        </div>
      </el-form>
    </el-card>

    <!-- 物料选择弹窗 -->
    <el-dialog v-model="materialDialogVisible" title="选择物料" width="800px">
      <el-table :data="availableMaterials" border style="width: 100%" @row-click="selectMaterial">
        <el-table-column prop="companyMaterialCode" label="公司物料编码" width="120" />
        <el-table-column prop="materialCode" label="甲料编码" width="120" />
        <el-table-column prop="materialName" label="物料名称" width="150" />
        <el-table-column prop="model" label="型号" width="120" />
        <el-table-column prop="specification" label="规格" width="120" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="selectMaterial(row)">
              选择
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="materialDialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 其它费用弹窗 -->
    <el-dialog v-model="otherCostDialogVisible" title="添加其它费用" width="500px">
      <el-form :model="otherCostForm" :rules="otherCostRules" ref="otherCostFormRef" label-width="80px">
        <el-form-item label="名称:" prop="name">
          <el-input v-model="otherCostForm.name" placeholder="请输入费用名称" />
        </el-form-item>
        <el-form-item label="数量:" prop="quantity">
          <el-input-number v-model="otherCostForm.quantity" :min="0" :step="1" style="width: 100%" />
        </el-form-item>
        <el-form-item label="单价:" prop="unitPrice">
          <el-input-number v-model="otherCostForm.unitPrice" :min="0" :step="0.01" :precision="2" style="width: 100%" />
        </el-form-item>
        <el-form-item label="总价:">
          <span>¥{{ (otherCostForm.quantity * otherCostForm.unitPrice).toFixed(2) }}</span>
        </el-form-item>
        <el-form-item label="备注:" prop="remarks">
          <el-input v-model="otherCostForm.remarks" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="otherCostDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddOtherCost">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  Tools,
  Document,
  EditPen,
  User,
  Box,
  Money,
  Check,
  DocumentAdd,
  Close
} from '@element-plus/icons-vue'

// 表单引用
const formRef = ref<FormInstance>()
const submitLoading = ref(false)

// 项目列表
const projectList = ref([
  { id: 1, name: '阳光小区A栋', status: '在建', progress: 65, startDate: '2024-01-01' },
  { id: 2, name: '花园广场项目', status: '在建', progress: 45, startDate: '2024-01-05' },
  { id: 3, name: '绿城花园D区', status: '在建', progress: 75, startDate: '2023-12-01' },
  { id: 4, name: '商业中心B区', status: '暂停', progress: 30, startDate: '2023-12-15' },
  { id: 5, name: '住宅楼C座', status: '完成', progress: 100, startDate: '2023-11-01' }
])

// 选中的项目
const selectedProject = ref<any>(null)

// 表单数据
const formData = reactive({
  projectId: '',
  completionProgress: 0,
  completionDescription: '',
  existingIssues: '',
  personnelList: [] as any[],
  materialList: [] as any[],
  otherCostList: [] as any[]
})

// 表单验证规则
const formRules: FormRules = {
  projectId: [
    { required: true, message: '请选择工程', trigger: 'change' }
  ],
  completionProgress: [
    { required: true, message: '请输入完成进度', trigger: 'blur' }
  ],
  completionDescription: [
    { required: true, message: '请输入完成情况描述', trigger: 'blur' },
    { min: 10, max: 500, message: '描述长度在 10 到 500 个字符', trigger: 'blur' }
  ]
}

// 获取状态类型
const getStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '未开始': 'info',
    '在建': 'warning',
    '暂停': 'danger',
    '完成': 'success'
  }
  return statusMap[status] || 'info'
}

// 项目变化处理
const onProjectChange = (projectId: string) => {
  selectedProject.value = projectList.value.find(p => p.id === Number(projectId))
  if (selectedProject.value) {
    formData.completionProgress = selectedProject.value.progress
  }
}

// 添加人员
const addPersonnel = () => {
  formData.personnelList.push({
    workType: '',
    dailyWage: 0,
    days: 0
  })
}

// 删除人员
const removePersonnel = (index: number) => {
  formData.personnelList.splice(index, 1)
}

// 弹窗控制
const materialDialogVisible = ref(false)
const otherCostDialogVisible = ref(false)
const otherCostFormRef = ref<FormInstance>()

// 可选择的物料列表
const availableMaterials = ref([
  {
    companyMaterialCode: 'CM001',
    materialCode: 'JL001',
    materialName: '燃气表',
    model: 'G2.5',
    specification: 'DN20',
    unit: '个'
  },
  {
    companyMaterialCode: 'CM002',
    materialCode: 'JL002',
    materialName: '镀锌管件',
    model: 'ZG-001',
    specification: '20*1.5',
    unit: '个'
  },
  {
    companyMaterialCode: 'CM003',
    materialCode: 'JL003',
    materialName: '波纹管',
    model: 'BW-15',
    specification: 'DN15*500',
    unit: 'm'
  },
  {
    companyMaterialCode: 'CM004',
    materialCode: 'JL004',
    materialName: '阀门',
    model: 'FM-25',
    specification: 'DN25',
    unit: '个'
  }
])

// 其它费用表单
const otherCostForm = reactive({
  name: '',
  quantity: 1,
  unitPrice: 0,
  remarks: ''
})

// 其它费用验证规则
const otherCostRules: FormRules = {
  name: [
    { required: true, message: '请输入费用名称', trigger: 'blur' }
  ],
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' }
  ],
  unitPrice: [
    { required: true, message: '请输入单价', trigger: 'blur' }
  ]
}

// 添加物料
const addMaterial = () => {
  materialDialogVisible.value = true
}

// 选择物料
const selectMaterial = (material: any) => {
  formData.materialList.push({
    companyMaterialCode: material.companyMaterialCode,
    materialCode: material.materialCode,
    materialName: material.materialName,
    model: material.model,
    specification: material.specification,
    unit: material.unit,
    quantity: 1,
    laborPrice: 0
  })
  materialDialogVisible.value = false
  ElMessage.success('物料添加成功')
}

// 删除物料
const removeMaterial = (index: number) => {
  formData.materialList.splice(index, 1)
}

// 添加其它费用
const addOtherCost = () => {
  // 重置表单
  Object.assign(otherCostForm, {
    name: '',
    quantity: 1,
    unitPrice: 0,
    remarks: ''
  })
  otherCostDialogVisible.value = true
}

// 确认添加其它费用
const confirmAddOtherCost = async () => {
  if (!otherCostFormRef.value) return

  try {
    await otherCostFormRef.value.validate()

    formData.otherCostList.push({
      name: otherCostForm.name,
      quantity: otherCostForm.quantity,
      unitPrice: otherCostForm.unitPrice,
      remarks: otherCostForm.remarks
    })

    otherCostDialogVisible.value = false
    ElMessage.success('费用添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 删除其它费用
const removeOtherCost = (index: number) => {
  formData.otherCostList.splice(index, 1)
}

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    const submitData = {
      ...formData,
      id: Date.now(),
      submitTime: new Date().toISOString()
    }
    
    console.log('提交推进数据:', submitData)
    ElMessage.success('工程推进提交成功')
    
    // 更新项目进度
    if (selectedProject.value) {
      selectedProject.value.progress = formData.completionProgress
    }
    
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单填写是否正确')
  } finally {
    submitLoading.value = false
  }
}

// 保存草稿
const handleSave = () => {
  ElMessage.success('草稿保存成功')
  console.log('保存草稿:', formData)
}

// 取消
const handleCancel = () => {
  formRef.value?.resetFields()
  selectedProject.value = null
  ElMessage.info('已取消')
}

onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.project-progress-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .breadcrumb {
    margin-bottom: 20px;
  }
  
  .main-card {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .card-header {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .form-section {
    margin-bottom: 30px;
    
    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: bold;
      color: #409eff;
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 2px solid #e4e7ed;
    }
  }
  
  .form-actions {
    text-align: center;
    padding: 30px 0;
    border-top: 1px solid #ebeef5;
    margin-top: 20px;
    
    .el-button {
      margin: 0 12px;
      padding: 12px 24px;
      
      .el-icon {
        margin-right: 6px;
      }
    }
  }
}
</style>
