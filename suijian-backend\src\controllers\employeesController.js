const { query, execute } = require('../config/database');
const Response = require('../utils/response');
const { generatePrefixedId } = require('../utils/uuid');

/**
 * 获取员工列表
 */
const getEmployees = async(req, res) => {
    try {
        const { page = 1, pageSize = 20, name = '', phone = '', idCard = '', status = '' } = req.query;
        let where = 'WHERE 1=1';
        const params = [];
        if (name) {
            where += ' AND name LIKE ?';
            params.push(`%${name}%`);
        }
        if (phone) {
            where += ' AND phone LIKE ?';
            params.push(`%${phone}%`);
        }
        if (idCard) {
            where += ' AND id_card LIKE ?';
            params.push(`%${idCard}%`);
        }
        if (status !== '') {
            where += ' AND status = ?';
            params.push(status);
        }
        const offset = (page - 1) * pageSize;
        // 获取总数
        const countSql = `SELECT COUNT(*) as total FROM employees ${where}`;
        const countResult = await query(countSql, params);
        const total = countResult[0] && countResult[0].total ? countResult[0].total : 0;
        // 获取列表
        const listSql = `SELECT * FROM employees ${where} ORDER BY created_at DESC LIMIT ? OFFSET ?`;
        const listParams = [...params, parseInt(pageSize), offset];
        const list = await query(listSql, listParams);
        Response.success(res, { list, total });
    } catch (error) {
        console.error('获取员工列表失败:', error);
        Response.serverError(res, '获取员工列表失败');
    }
};

/**
 * 新增员工
 */
const createEmployee = async(req, res) => {
    try {
        const { name, dailyWage, phone, idCard, status, hireDate, remark } = req.body;

        // 验证必填字段
        if (!name || !phone) {
            return Response.error(res, '请填写员工姓名和联系电话', 400);
        }

        // 检查员工姓名是否已存在
        const existingSql = 'SELECT id FROM employees WHERE name = ? AND status = 1';
        const existing = await query(existingSql, [name]);
        if (existing.length > 0) {
            return Response.error(res, '员工姓名已存在', 400);
        }

        // 生成员工ID
        const employeeId = generatePrefixedId('emp');

        // 插入新员工
        const insertSql = `
      INSERT INTO employees (id, name, base_salary, phone, id_card, status, entry_date, remarks, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
    `;

        const result = await execute(insertSql, [
            employeeId,
            name,
            dailyWage,
            phone,
            idCard || '',
            status || 1,
            hireDate || null,
            remark || ''
        ]);

        Response.success(res, { id: employeeId }, '新增员工成功');
    } catch (error) {
        console.error('新增员工失败:', error);
        Response.serverError(res, '新增员工失败');
    }
};

/**
 * 更新员工
 */
const updateEmployee = async(req, res) => {
    try {
        const { id } = req.params;
        const { name, dailyWage, phone, idCard, status, hireDate, remark } = req.body;

        // 检查员工是否存在
        const existingSql = 'SELECT id FROM employees WHERE id = ?';
        const existing = await query(existingSql, [id]);
        if (existing.length === 0) {
            return Response.error(res, '员工不存在', 404);
        }

        // 检查姓名是否重复（排除自己）
        if (name) {
            const duplicateSql = 'SELECT id FROM employees WHERE name = ? AND id != ? AND status = 1';
            const duplicate = await query(duplicateSql, [name, id]);
            if (duplicate.length > 0) {
                return Response.error(res, '员工姓名已存在', 400);
            }
        }

        // 更新员工信息
        const updateSql = `
      UPDATE employees 
      SET name = ?, base_salary = ?, phone = ?, id_card = ?, status = ?, entry_date = ?, remarks = ?, updated_at = datetime('now')
      WHERE id = ?
    `;

        await execute(updateSql, [
            name,
            dailyWage,
            phone,
            idCard || '',
            status,
            hireDate || null,
            remark || '',
            id
        ]);

        Response.success(res, null, '更新员工成功');
    } catch (error) {
        console.error('更新员工失败:', error);
        Response.serverError(res, '更新员工失败');
    }
};

module.exports = {
    getEmployees,
    createEmployee,
    updateEmployee
};