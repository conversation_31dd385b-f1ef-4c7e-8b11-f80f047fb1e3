# MaterialSelector 物料选择组件

## 功能特性

- ✅ 支持多选/单选物料
- ✅ 支持搜索和分页
- ✅ 支持按分类筛选
- ✅ 支持最大选择数量限制
- ✅ 支持已选物料自动勾选
- ✅ 支持跨页选择保持状态
- ✅ 防抖搜索优化性能

## 使用方法

### 1. 导入组件

```vue
<script setup lang="ts">
import MaterialSelector from '@/components/MaterialSelector.vue'
</script>
```

### 2. 基础使用

```vue
<template>
  <div>
    <el-button @click="showSelector = true">选择物料</el-button>
    
    <MaterialSelector
      v-model="showSelector"
      title="选择物料"
      @confirm="handleConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import MaterialSelector from '@/components/MaterialSelector.vue'

const showSelector = ref(false)

const handleConfirm = (materials: any[]) => {
  console.log('选中的物料:', materials)
}
</script>
```

### 3. 高级使用

```vue
<template>
  <div>
    <el-button @click="showSelector = true">选择甲料</el-button>
    
    <MaterialSelector
      v-model="showSelector"
      title="选择甲料"
      category="甲料"
      :selected-ids="selectedIds"
      :max-select="5"
      @confirm="handleConfirm"
      @selection-change="handleSelectionChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import MaterialSelector from '@/components/MaterialSelector.vue'

const showSelector = ref(false)
const selectedMaterials = ref([])

// 已选物料的ID列表
const selectedIds = computed(() => selectedMaterials.value.map(m => m.id))

const handleConfirm = (materials: any[]) => {
  selectedMaterials.value = materials
  console.log('确认选择:', materials)
}

const handleSelectionChange = (materials: any[]) => {
  console.log('选择变化:', materials)
}
</script>
```

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | boolean | - | 控制弹窗显示/隐藏 |
| title | string | '选择物料' | 弹窗标题 |
| searchPlaceholder | string | '请输入物料名称或编码搜索' | 搜索框占位符 |
| confirmButtonText | string | '确定' | 确认按钮文本 |
| category | string | '' | 物料分类筛选 |
| multiple | boolean | true | 是否支持多选 |
| selectedIds | string[] | [] | 已选物料的ID列表 |
| maxSelect | number | 0 | 最大选择数量（0表示无限制） |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | boolean | 弹窗显示状态变化 |
| confirm | materials[] | 确认选择时触发 |
| selection-change | materials[] | 选择变化时触发 |

## 使用场景

### 1. 甲料入库
```vue
<MaterialSelector
  v-model="showSelector"
  title="选择甲料"
  category="甲料"
  :selected-ids="selectedMaterialIds"
  @confirm="handleMaterialConfirm"
/>
```

### 2. 领料申请
```vue
<MaterialSelector
  v-model="showSelector"
  title="选择物料"
  :selected-ids="selectedMaterialIds"
  @confirm="handleMaterialConfirm"
/>
```

### 3. 物料退货
```vue
<MaterialSelector
  v-model="showSelector"
  title="选择退货物料"
  :selected-ids="selectedMaterialIds"
  @confirm="handleMaterialConfirm"
/>
```

### 4. 限制选择数量
```vue
<MaterialSelector
  v-model="showSelector"
  title="选择物料"
  :max-select="3"
  @confirm="handleMaterialConfirm"
/>
```

## 注意事项

1. **API接口**: 组件依赖 `/api/materials` 接口获取物料列表
2. **数据格式**: 物料数据应包含 `id`, `companyCode`, `name`, `clientCodes`, `specification`, `stockQuantity`, `unit` 等字段
3. **权限控制**: 确保用户有访问物料列表的权限
4. **性能优化**: 搜索功能已实现防抖，避免频繁请求

## 样式定制

组件使用 Element Plus 的样式，可以通过 CSS 变量进行定制：

```css
:root {
  --el-dialog-width: 1100px;
  --el-table-border-color: #ebeef5;
}
``` 