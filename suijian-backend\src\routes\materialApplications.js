const express = require('express');
const router = express.Router();
const materialApplicationController = require('../controllers/materialApplicationController');
const { authMiddleware } = require('../middleware/auth');

// 所有路由都需要认证
router.use(authMiddleware);

// 创建领料申请
router.post('/', materialApplicationController.createMaterialApplication);

// 获取领料申请列表
router.get('/', materialApplicationController.getMaterialApplications);

// 获取领料申请详情
router.get('/:id', materialApplicationController.getMaterialApplicationById);

// 批准领料申请并出库
router.put('/:id/approve', materialApplicationController.approveApplication);

// 删除领料申请
router.delete('/:id', materialApplicationController.deleteMaterialApplication);

// 获取物料记录中的申请记录
router.get('/records/list', materialApplicationController.getApplicationRecords);

module.exports = router;