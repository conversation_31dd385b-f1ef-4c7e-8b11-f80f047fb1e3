# 领料申请界面 - MaterialSelector 集成说明

## 功能概述

领料申请界面已成功集成 MaterialSelector 组件，实现了物料选择与物料清单的实时同步功能。

## 主要功能特性

### 1. 实时同步选择状态
- 使用 `@selection-change` 事件实现实时同步
- 支持跨页面保持选择状态
- 自动恢复已选物料的数量和备注信息

### 2. 物料清单管理
- 显示物料编码、名称、甲料编码、规格、单位、库存等信息
- 支持修改申请数量和添加备注
- 实时计算小计金额
- 支持删除单个物料和清空整个清单

### 3. 数据验证
- 验证物料申请数量不能为空或小于等于0
- 验证申请数量不能超过库存数量
- 验证表单必填项完整性

### 4. 用户体验优化
- 空状态提示和引导
- 物料数量显示在按钮和标题中
- 操作确认对话框
- 友好的错误提示

## 技术实现

### 组件集成
```vue
<MaterialSelector
  v-model="materialDialogVisible"
  title="选择物料"
  :selected-ids="selectedMaterialIds"
  @selection-change="handleMaterialSelectionChange"
/>
```

### 数据同步逻辑
```javascript
// 处理物料选择变化（实时同步）
const handleMaterialSelectionChange = (materials: any[]) => {
  // 保留现有物料的数量和备注信息
  const existingMaterials = materialList.value.reduce((acc, item) => {
    acc[item.id] = {
      quantity: item.quantity,
      remarks: item.remarks || ''
    }
    return acc
  }, {})
  
  // 更新物料清单，保持数量和备注信息
  materialList.value = materials.map(material => ({
    ...material,
    quantity: existingMaterials[material.id]?.quantity || 1,
    remarks: existingMaterials[material.id]?.remarks || ''
  }))
}
```

### 计算属性
```javascript
// 计算已选物料的ID列表
const selectedMaterialIds = computed(() => materialList.value.map(m => m.id))

// 计算总金额
const totalAmount = computed(() => {
  return materialList.value.reduce((total, item) => {
    return total + (item.price * item.quantity)
  }, 0)
})
```

## 使用流程

1. **打开物料选择器**：点击"添加物料"按钮
2. **选择物料**：在弹窗中搜索和选择需要的物料
3. **实时同步**：选择变化会立即同步到物料清单
4. **编辑数量**：在清单中修改申请数量和备注
5. **保存申请**：完成表单填写后保存申请单

## 注意事项

1. 物料选择器支持跨页面保持选择状态
2. 数量和备注信息会在重新打开选择器时自动恢复
3. 删除物料后，重新选择该物料会重置数量和备注
4. 保存时会进行完整的数据验证

## 扩展功能

- 支持按物料类别筛选（如甲料、乙料等）
- 支持设置最大选择数量限制
- 支持自定义搜索占位符
- 支持自定义弹窗标题

## 相关文件

- 主文件：`src/views/warehouse/MaterialApply.vue`
- 组件文件：`src/components/MaterialSelector.vue`
- 样式文件：内联样式 