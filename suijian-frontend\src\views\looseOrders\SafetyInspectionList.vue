<template>
  <div class="safety-inspection-list">
    <el-card class="list-card" body-style="padding:0;margin:0;">
      <!-- 搜索栏 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="客户编号">
            <el-input
              v-model="searchForm.customerNumber"
              placeholder="请输入客户编号"
              clearable
            />
          </el-form-item>
          <el-form-item label="用户姓名">
            <el-input
              v-model="searchForm.userName"
              placeholder="请输入用户姓名"
              clearable
            />
          </el-form-item>
          <el-form-item label="安检员">
            <el-input
              v-model="searchForm.inspector"
              placeholder="请输入安检员姓名"
              clearable
            />
          </el-form-item>
          <el-form-item label="小区">
            <el-input
              v-model="searchForm.community"
              placeholder="请输入小区名称"
              clearable
            />
          </el-form-item>
          <el-form-item label="电话">
            <el-input
              v-model="searchForm.phone"
              placeholder="请输入电话号码"
              clearable
            />
          </el-form-item>
          <el-form-item label="派单时间">
            <el-date-picker
              v-model="searchForm.dispatchTime"
              type="date"
              placeholder="选择派单时间"
              clearable
            />
          </el-form-item>
          <el-form-item label="安检时间">
            <el-date-picker
              v-model="searchForm.inspectionTime"
              type="date"
              placeholder="选择安检时间"
              clearable
            />
          </el-form-item>
          <el-form-item label="是否已派单">
            <el-select
              v-model="searchForm.isDispatched"
              placeholder="请选择派单状态"
              clearable
              style="width: 150px"
            >
              <el-option label="未派单" value="false" />
              <el-option label="已派单" value="true" />
            </el-select>
          </el-form-item>
          <el-form-item label="入户情况">
            <el-select
              v-model="searchForm.entryStatus"
              placeholder="请选择入户情况"
              clearable
              style="width: 150px"
            >
              <el-option label="未安检" value="not_checked" />
              <el-option label="正常入户" value="normal_entry" />
              <el-option label="需整改" value="need_repair" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <div class="left-actions">
        <el-button type="primary" @click="handleImportExcel">
          <el-icon><Upload /></el-icon>
          导入Excel
        </el-button>
          <el-button 
            type="success" 
            @click="handleBatchDispatch"
            :disabled="selectedRows.length === 0"
          >
            <el-icon><UserFilled /></el-icon>
            批量派单
          </el-button>
          <el-button 
            type="warning" 
            @click="handleBatchNormalEntry"
            :disabled="selectedRows.length === 0"
          >
            <el-icon><Check /></el-icon>
            批量正常入户
          </el-button>
        </div>

      </div>

      <!-- 表格 -->
      <div class="table-container">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
          :row-class-name="getRowClassName"
        >
          <el-table-column type="selection" width="45" fixed="left" />
          <el-table-column prop="dispatchTime" label="派单时间" width="150" />
          <el-table-column prop="customerNumber" label="客户编号" width="120" />
        <el-table-column prop="userName" label="用户姓名" width="120" />
          <el-table-column prop="phone" label="电话" width="130" />
          <el-table-column prop="community" label="小区" min-width="150" />
          <el-table-column prop="address" label="地址" min-width="200" />
          <el-table-column prop="inspector" label="安检员" width="100" />
          <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
          <el-table-column prop="entryStatus" label="入户情况" width="120">
          <template #default="{ row }">
              <el-tag :type="getEntryStatusType(row.entryStatus)" size="small">
                {{ getEntryStatusText(row.entryStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
              <div style="display: flex; gap: 5px; justify-content: flex-end;">
                <el-button type="primary" size="small" @click="handleDispatch(row)">
                  派单
            </el-button>
                <el-button type="success" size="small" @click="handleEnterWorkOrder(row)">
                  录入安检单
            </el-button>
              </div>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>



    <!-- 派单对话框 -->
    <el-dialog
      v-model="dispatchDialogVisible"
      title="派单"
      width="60%"
      :before-close="handleCloseDispatch"
    >
      <div v-if="currentDispatch">
        <!-- 基本信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="客户编号">{{ currentDispatch.customerNumber }}</el-descriptions-item>
            <el-descriptions-item label="用户姓名">{{ currentDispatch.userName }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ currentDispatch.phone }}</el-descriptions-item>
            <el-descriptions-item label="小区名称">{{ currentDispatch.community }}</el-descriptions-item>
            <el-descriptions-item label="详细地址">{{ currentDispatch.address }}</el-descriptions-item>
            <el-descriptions-item label="派单时间">{{ formatDate(currentDispatch.dispatchTime) }}</el-descriptions-item>
            <el-descriptions-item label="安检原因">{{ currentDispatch.inspectionReason }}</el-descriptions-item>
            <el-descriptions-item label="入户情况">
              <el-tag :type="getEntryStatusType(currentDispatch.entryStatus)" size="small">
                {{ getEntryStatusText(currentDispatch.entryStatus) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 选择安检员 -->
        <el-card class="info-card" shadow="never" style="margin-top: 20px;">
          <template #header>
            <div class="card-header">
              <span>选择安检员</span>
            </div>
          </template>
          <el-form :model="dispatchForm" label-width="100px">
            <el-form-item label="选择安检员" required>
              <el-select
                v-model="dispatchForm.inspector"
                placeholder="请选择安检员"
                style="width: 100%"
                filterable
              >
                <el-option
                  v-for="inspector in inspectorList"
                  :key="inspector.id"
                  :label="inspector.name"
                  :value="inspector.id"
                >
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>{{ inspector.name }}</span>
                    <span style="color: #999; font-size: 12px;">{{ inspector.phone }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="备注">
              <el-input
                v-model="dispatchForm.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入派单备注信息"
              />
            </el-form-item>
          </el-form>
        </el-card>
          </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseDispatch">取消</el-button>
          <el-button type="primary" @click="handleConfirmDispatch" :loading="dispatchLoading">
            确认派单
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量派单对话框 -->
    <el-dialog
      v-model="batchDispatchDialogVisible"
      title="批量派单"
      width="80%"
      :before-close="handleCloseBatchDispatch"
      :close-on-click-modal="false"
      top="5vh"
    >
      <div class="batch-dispatch-content">
        <!-- 派单表单 -->
        <el-form :model="batchDispatchForm" label-width="120px">
          <el-form-item label="选择安检员" required>
            <el-select
              v-model="batchDispatchForm.inspector"
              placeholder="请选择安检员"
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="inspector in inspectorList"
                :key="inspector.id"
                :label="inspector.name"
                :value="inspector.id"
              >
                <div style="display: flex; justify-content: space-between; align-items: center;">
                  <span>{{ inspector.name }}</span>
                  <span style="color: #999; font-size: 12px;">{{ inspector.phone }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="batchDispatchForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入派单备注信息"
            />
          </el-form-item>
        </el-form>

        <!-- 预览派单列表 -->
        <div class="preview-section">
          <h4>预览派单列表（共{{ selectedRows.length }}条）</h4>
          <div class="preview-list">
            <el-table :data="selectedRows" border style="width: 100%" max-height="300">
              <el-table-column prop="customerNumber" label="客户编号" width="120" />
              <el-table-column prop="userName" label="用户姓名" width="120" />
              <el-table-column prop="phone" label="电话" width="130" />
              <el-table-column prop="community" label="小区" min-width="150" />
              <el-table-column prop="address" label="地址" min-width="200" />
              <el-table-column prop="entryStatus" label="入户情况" width="120">
                <template #default="{ row }">
                  <el-tag :type="getEntryStatusType(row.entryStatus)" size="small">
                    {{ getEntryStatusText(row.entryStatus) }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
                </div>
          </div>
        </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseBatchDispatch">取消</el-button>
          <el-button type="primary" @click="handleConfirmBatchDispatch" :loading="batchDispatchLoading">
            确认批量派单
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量正常入户对话框 -->
    <el-dialog
      v-model="batchNormalEntryDialogVisible"
      title="批量正常入户"
      width="60%"
      :before-close="handleCloseBatchNormalEntry"
    >
      <div class="batch-normal-entry-content">
        <el-form :model="batchNormalEntryForm" label-width="100px">
          <el-form-item label="备注">
            <el-input
              v-model="batchNormalEntryForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-form>

        <div class="preview-section">
          <h4>预览列表（共{{ selectedRows.length }}条）</h4>
          <div class="preview-list">
            <el-table :data="selectedRows" border style="width: 100%" max-height="300">
              <el-table-column prop="customerNumber" label="客户编号" width="120" />
              <el-table-column prop="userName" label="用户姓名" width="120" />
              <el-table-column prop="phone" label="电话" width="130" />
              <el-table-column prop="community" label="小区" min-width="150" />
              <el-table-column prop="address" label="地址" min-width="200" />
              <el-table-column prop="entryStatus" label="当前状态" width="120">
                <template #default="{ row }">
                  <el-tag :type="getEntryStatusType(row.entryStatus)" size="small">
                    {{ getEntryStatusText(row.entryStatus) }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseBatchNormalEntry">取消</el-button>
          <el-button type="primary" @click="handleConfirmBatchNormalEntry" :loading="batchNormalEntryLoading">
            确认批量正常入户
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Upload, 
  UserFilled, 
  Check, 
  Plus, 
  View, 
  Edit, 
  Delete,
  Refresh
} from '@element-plus/icons-vue'
import request from '@/utils/request'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  customerNumber: '',
  userName: '',
  inspector: '',
  community: '',
  phone: '',
  dispatchTime: '',
  inspectionTime: '',
  isDispatched: '',
  entryStatus: ''
})

// 选中的行
const selectedRows = ref([])

// 当前高亮的行
const highlightedRow = ref(null)

// 批量派单对话框
const batchDispatchDialogVisible = ref(false)
const batchDispatchLoading = ref(false)
const previewAssignments = ref([])

// 批量派单表单
const batchDispatchForm = reactive({
  inspector: '',
  remark: '',
  dispatchMode: 'same',
  autoStrategy: 'balance'
})

// 批量正常入户对话框
const batchNormalEntryDialogVisible = ref(false)
const batchNormalEntryLoading = ref(false)

// 批量正常入户表单
const batchNormalEntryForm = reactive({
  remark: ''
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 表格数据
const tableData = ref([])
const loading = ref(false)



// 派单对话框
const dispatchDialogVisible = ref(false)
const currentDispatch = ref(null)
const dispatchLoading = ref(false)
const inspectorList = ref([])

// 派单表单
const dispatchForm = reactive({
  inspector: '',
  remark: ''
})

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      customerNumber: searchForm.customerNumber,
      userName: searchForm.userName,
      inspector: searchForm.inspector,
      community: searchForm.community,
      phone: searchForm.phone,
      dispatchTime: searchForm.dispatchTime,
      inspectionTime: searchForm.inspectionTime,
      isDispatched: searchForm.isDispatched,
      entryStatus: searchForm.entryStatus,
    };
    
    // 移除空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })
    
    const response = await request.get('/api/safety/inspection/orders', { params })
    
    if (response.code === 200) {
      tableData.value = response.data.list
      pagination.total = response.data.total
    } else {
      ElMessage.error(response.message || '获取数据失败')
    }
  } catch (error) {
    ElMessage.error('获取数据失败')
    console.error('获取安检单列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取安检员列表
const fetchInspectorList = async () => {
  try {
    const response = await request.get('/api/employees/inspectors')
    
    if (response.code === 200) {
      inspectorList.value = response.data
    } else {
      ElMessage.error(response.message || '获取安检员列表失败')
    }
  } catch (error) {
    ElMessage.error('获取安检员列表失败')
    console.error('获取安检员列表失败:', error)
  }
}

// 保存状态到sessionStorage
const saveState = () => {
  const state = {
    searchForm: { ...searchForm },
    pagination: { ...pagination },
    selectedRows: selectedRows.value,
    highlightedRow: highlightedRow.value
  }
  sessionStorage.setItem('safetyInspectionListState', JSON.stringify(state))
}

// 从sessionStorage恢复状态
const restoreState = () => {
  const savedState = sessionStorage.getItem('safetyInspectionListState')
  if (savedState) {
    try {
      const state = JSON.parse(savedState)
      Object.assign(searchForm, state.searchForm)
      Object.assign(pagination, state.pagination)
      selectedRows.value = state.selectedRows || []
      highlightedRow.value = state.highlightedRow
      fetchData()
    } catch (error) {
      console.error('恢复状态失败:', error)
    }
  }
}

// 页面激活时恢复状态
onActivated(() => {
  restoreState()
})

// 页面卸载时保存状态
onMounted(() => {
  fetchData()
  // 监听页面卸载事件
  window.addEventListener('beforeunload', saveState)
  fetchInspectorList()
})

onUnmounted(() => {
  window.removeEventListener('app-refresh', handleAppRefresh)
})

// 搜索
const handleSearch = async () => {
  pagination.currentPage = 1
  await fetchData()
  saveState()
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.currentPage = 1
  selectedRows.value = []
  fetchData()
  saveState()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  fetchData()
  saveState()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  fetchData()
  saveState()
}

// 选择改变
const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows
  saveState()
}

// 行点击
const handleRowClick = (row: any) => {
  highlightedRow.value = row.id
}

// 获取行类名
const getRowClassName = ({ row }: { row: any }) => {
  if (highlightedRow.value === row.id) {
    return 'highlighted-row'
  }
  return ''
}





// 派单
const handleDispatch = (row: any) => {
  currentDispatch.value = row
  dispatchDialogVisible.value = true
}

// 确认派单
const handleConfirmDispatch = async () => {
  if (!dispatchForm.inspector) {
    ElMessage.error('请选择安检员')
    return
  }
  
  try {
    dispatchLoading.value = true
    
    const selectedInspector = inspectorList.value.find(item => item.id === dispatchForm.inspector)
    
    const response = await request.post('/api/safety/inspection/orders/dispatch', {
      orderId: currentDispatch.value.id,
      inspectorId: dispatchForm.inspector,
      inspectorName: selectedInspector.name,
      remark: dispatchForm.remark
    })
    
    if (response.code === 200) {
      ElMessage.success('派单成功')
      handleCloseDispatch()
      fetchData()
    } else {
      ElMessage.error(response.message || '派单失败')
    }
  } catch (error) {
    ElMessage.error('派单失败')
    console.error('派单失败:', error)
  } finally {
    dispatchLoading.value = false
  }
}

// 关闭派单对话框
const handleCloseDispatch = () => {
  dispatchDialogVisible.value = false
  currentDispatch.value = null
  dispatchForm.inspector = ''
  dispatchForm.remark = ''
}

// 批量派单
const handleBatchDispatch = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要派单的安检单')
    return
  }
  
  // 检查是否有已派单的订单
  const dispatchedOrders = selectedRows.value.filter(row => row.isDispatched)
  
  if (dispatchedOrders.length > 0) {
    ElMessageBox.confirm(
      `选中的${selectedRows.value.length}条安检单中有${dispatchedOrders.length}条已派单，是否覆盖派单？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      batchDispatchForm.dispatchMode = 'same'
      batchDispatchForm.inspector = ''
      batchDispatchForm.autoStrategy = 'balance'
      batchDispatchForm.remark = ''
      batchDispatchDialogVisible.value = true
    }).catch(() => {
      // 用户取消
    })
  } else {
    batchDispatchForm.dispatchMode = 'same'
    batchDispatchForm.inspector = ''
    batchDispatchForm.autoStrategy = 'balance'
    batchDispatchForm.remark = ''
    batchDispatchDialogVisible.value = true
  }
}

// 确认批量派单
const handleConfirmBatchDispatch = async () => {
  if (!batchDispatchForm.inspector) {
    ElMessage.error('请选择安检员')
    return
  }
  
  try {
    batchDispatchLoading.value = true
    
    const selectedInspector = inspectorList.value.find(item => item.id === batchDispatchForm.inspector)
    
    const response = await request.post('/api/safety/inspection/orders/batch-dispatch', {
      orderIds: selectedRows.value.map(row => row.id),
      inspectorId: batchDispatchForm.inspector,
      inspectorName: selectedInspector.name,
      remark: batchDispatchForm.remark
    })
    
    if (response.code === 200) {
      ElMessage.success('批量派单成功')
      handleCloseBatchDispatch()
      selectedRows.value = []
      fetchData()
    } else {
      ElMessage.error(response.message || '批量派单失败')
    }
  } catch (error) {
    ElMessage.error('批量派单失败')
    console.error('批量派单失败:', error)
  } finally {
    batchDispatchLoading.value = false
  }
}

// 关闭批量派单对话框
const handleCloseBatchDispatch = () => {
  batchDispatchDialogVisible.value = false
  batchDispatchForm.inspector = ''
  batchDispatchForm.remark = ''
}

// 批量正常入户
const handleBatchNormalEntry = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要设置为正常入户的安检单')
    return
  }
  
  batchNormalEntryForm.remark = ''
  batchNormalEntryDialogVisible.value = true
}

// 确认批量正常入户
const handleConfirmBatchNormalEntry = async () => {
  try {
    batchNormalEntryLoading.value = true
    
    const response = await request.post('/api/safety/inspection/orders/batch-normal-entry', {
      orderIds: selectedRows.value.map(row => row.id),
      remark: batchNormalEntryForm.remark
    })
    
    if (response.code === 200) {
      ElMessage.success('批量正常入户成功')
      handleCloseBatchNormalEntry()
      selectedRows.value = []
      fetchData()
    } else {
      ElMessage.error(response.message || '批量正常入户失败')
    }
  } catch (error) {
    ElMessage.error('批量正常入户失败')
    console.error('批量正常入户失败:', error)
  } finally {
    batchNormalEntryLoading.value = false
  }
}

// 关闭批量正常入户对话框
const handleCloseBatchNormalEntry = () => {
  batchNormalEntryDialogVisible.value = false
  batchNormalEntryForm.remark = ''
}

// 录入安检单
const handleEnterWorkOrder = (row: any) => {
  router.push({ name: 'SafetyInspectionEntry', query: { id: row.id } })
}



// 导入Excel
const handleImportExcel = () => {
  ElMessage.info('导入Excel功能开发中...')
}

// 格式化日期 - 显示北京时间
const formatDate = (date: string) => {
  if (!date) return ''
  
  // 创建日期对象并添加8小时转换为北京时间
  const dateObj = new Date(date)
  dateObj.setHours(dateObj.getHours() + 8)
  
  return dateObj.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未知'
}

// 获取状态类型
const getStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    'pending': 'warning',
    'processing': 'primary',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取入户情况文本
const getEntryStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'not_checked': '未安检',
    'normal_entry': '正常入户',
    'need_repair': '需整改'
  }
  return statusMap[status] || '未安检'
}

// 获取入户情况类型
const getEntryStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    'not_checked': 'info',
    'normal_entry': 'success',
    'need_repair': 'warning'
  }
  return typeMap[status] || 'info'
}

// 刷新事件监听
const handleAppRefresh = () => {
  fetchData()
}
</script>

<style lang="scss" scoped>
.safety-inspection-list {
  padding: 0;
  min-height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-bottom: 60px;
}

.list-card {
  flex: 1;
  border: none;
  box-shadow: none;
  background: #fff;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.search-section {
  padding: 0;
  border-bottom: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
}

.action-section {
  padding: 15px 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  
  .left-actions {
    display: flex;
    gap: 10px;
  }
  
  .right-actions {
    display: flex;
    gap: 10px;
  }
}

.table-container {
  width: 100%;
  min-width: 0;
  flex: 1 1 0%;
  padding: 0 20px;
  box-sizing: border-box;
}
:deep(.el-table) {
  width: 100%;
  /* min-width: 1400px; */
}

.pagination-section {
  padding: 15px 20px;
  border-top: 1px solid #e4e7ed;
  display: flex;
  justify-content: center;
  flex-shrink: 0;
  background: #fff;
  z-index: 1;
  position: relative;
  margin-bottom: 60px;
}

.info-card {
  margin-bottom: 20px;
  
  .card-header {
  font-weight: bold;
  }
}

.batch-dispatch-content,
.batch-normal-entry-content {
  .preview-section {
    margin-top: 20px;
    
    h4 {
      margin-bottom: 15px;
      color: #606266;
    }
    
    .preview-list {
      border: 1px solid #e4e7ed;
      border-radius: 4px;
    }
  }
}

// 高亮行样式
:deep(.el-table__body tr.highlighted-row td) {
  background-color: #e6f7ff !important;
}

:deep(.el-table__body tr.highlighted-row.el-table__row--striped td) {
  background-color: #e6f7ff !important;
}
</style> 