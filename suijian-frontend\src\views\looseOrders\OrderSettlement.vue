<template>
  <div class="order-settlement">
    <el-card class="settlement-card">
      
      <div class="month-selector">
        <el-form :inline="true">
          <el-form-item label="结算周期">
            <el-date-picker
              v-model="searchForm.periodRange"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              format="YYYY-MM"
              value-format="YYYY-MM"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="success" @click="handleExportSettlement">导出结算书</el-button>
            <el-button type="warning" @click="handleConfirmArchive">确定平帐归档</el-button>
            <el-button type="primary" @click="handleImportMaterialPriceExcel">导入甲方材料价格表Excel</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <el-card class="settlement-card">
      
      <!-- 模块标签页 -->
      <div class="module-tabs">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="封面" name="cover">
            <div class="tab-content">
              <div class="settlement-cover">
                <div class="cover-header">
                  <el-select
                    v-model="coverData.companyName"
                    filterable
                    allow-create
                    clearable
                    placeholder="请输入或选择公司名称"
                    class="company-name-input"
                    style="width: 100%;"
                  >
                    <el-option
                      v-for="item in companyNameOptions"
                      :key="item"
                      :label="item"
                      :value="item"
                    />
                  </el-select>
                  <h2 class="document-title">工单结算书</h2>
                </div>
                
                <div class="cover-content">
                  <div class="left-column">
                    <div class="form-row">
                      <span class="label">项目名称:</span>
                      <span class="value">{{ coverData.projectName }}</span>
                    </div>
                    <div class="form-row">
                      <span class="label">CEA编码:</span>
                      <el-input v-model="coverData.ceaCode" placeholder="请输入CEA编码" clearable style="max-width: 300px;" />
                    </div>
                    <div class="form-row">
                      <span class="label">送审人工费:</span>
                      <span class="value">{{ coverData.submittedLaborCost }}元</span>
                    </div>
                    <div class="form-row">
                      <span class="label">二审造价:</span>
                      <span class="value">{{ coverData.secondReviewCost }}元</span>
                    </div>
                    <div class="form-row">
                      <span class="label">结算编号:</span>
                      <span class="value">{{ coverData.settlementNumber }}</span>
                    </div>
                    <div class="form-row">
                      <span class="label">应付金额:</span>
                      <span class="value">{{ coverData.amountPayableChinese }}</span>
                      <span class="currency-symbol">¥: {{ coverData.amountPayable }}元</span>
                    </div>
                    <div class="form-row">
                      <span class="label">施工单位:</span>
                      <el-select
                        v-model="coverData.constructionUnit"
                        filterable
                        allow-create
                        clearable
                        placeholder="请输入或选择施工单位"
                        style="max-width: 300px; width: 100%;"
                      >
                        <el-option
                          v-for="item in constructionUnitOptions"
                          :key="item"
                          :label="item"
                          :value="item"
                        />
                      </el-select>
                    </div>
                    <div class="form-row">
                      <span class="label">建设单位:</span>
                      <el-select
                        v-model="coverData.clientUnit"
                        filterable
                        allow-create
                        clearable
                        placeholder="请输入或选择建设单位"
                        style="max-width: 300px; width: 100%;"
                      >
                        <el-option
                          v-for="item in clientUnitOptions"
                          :key="item"
                          :label="item"
                          :value="item"
                        />
                      </el-select>
                    </div>
                  </div>
                  
                  <div class="right-column">
                    <div class="form-row">
                      <span class="label">总造价:</span>
                      <span class="value">{{ coverData.totalCost }}元</span>
                    </div>
                    <div class="form-row">
                      <span class="label">人工结算:</span>
                      <span class="value">{{ coverData.laborSettlement }}元</span>
                    </div>
                    <div class="form-row">
                      <span class="label">材料结算:</span>
                      <span class="value">{{ coverData.materialSettlement }}元</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="应付施工单位金额" name="payable-amount">
            <div class="tab-content">
              <el-table :data="payableAmountData" style="width: 100%" border show-summary :summary-method="getPayableAmountSummary" v-loading="loading">
                <el-table-column prop="serialNo" label="序号" width="60" align="center" />
                <el-table-column prop="projectName" label="单项工程名称" width="200" />
                <el-table-column label="工程安装人工费(元)" align="center">
                  <el-table-column prop="preMeterLaborCost" label="表前安装人工费" width="120" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.preMeterLaborCost) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="indoorLaborCost" label="户内安装人工费" width="120" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.indoorLaborCost) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="laborSubtotal" label="小计(元)" width="120" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.laborSubtotal) }}
                    </template>
                  </el-table-column>
                </el-table-column>
                <el-table-column prop="installedHouseholds" label="安装户数" width="100" align="center" />
                <el-table-column label="工程安装材料费(元)" align="center">
                  <el-table-column prop="gasMeterCost" label="煤气表" width="100" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.gasMeterCost) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="postMeterCost" label="表后" width="100" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.postMeterCost) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="fittingsCost" label="管件" width="100" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.fittingsCost) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="materialSubtotal" label="小计(元)" width="120" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.materialSubtotal) }}
                    </template>
                  </el-table-column>
                </el-table-column>
                <el-table-column prop="actualReceivedAmount" label="实际领用金额(元)" width="150" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.actualReceivedAmount) }}
                  </template>
                </el-table-column>
                <el-table-column prop="overReceivedAmount" label="超领金额(元)" width="120" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.overReceivedAmount) }}
                  </template>
                </el-table-column>
                <el-table-column prop="totalProjectCost" label="工程总造价(元)" width="150" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.totalProjectCost) }}
                  </template>
                </el-table-column>
                <el-table-column prop="payableAmount" label="应付金额(元)" width="120" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.payableAmount) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="表前" name="pre-meter">
            <div class="tab-content">
              <el-table :data="preMeterData" style="width: 100%" border show-summary :summary-method="getPreMeterSummary" v-loading="loading">
                <el-table-column prop="serialNo" label="序号" width="60" align="center" />
                <el-table-column prop="projectName" label="单项工程名称" width="200" />
                
                <!-- 甲供材料 -->
                <el-table-column label="甲供材料" align="center">
                  <!-- 气表 -->
                  <el-table-column label="气表" align="center">
                    <el-table-column label="机械表接头(个)" align="center">
                      <el-table-column prop="mechanicalConnectorReceived" label="实领" width="80" align="center" />
                      <el-table-column prop="mechanicalConnectorConsumed" label="实耗" width="80" align="center" />
                      <el-table-column prop="mechanicalConnectorPrice" label="单价(元/个)" width="100" align="center">
                        <template #default="{ row }">
                          ¥{{ formatNumber(row.mechanicalConnectorPrice) }}
                        </template>
                      </el-table-column>
                    </el-table-column>
                    <el-table-column label="千嘉表(块)" align="center">
                      <el-table-column prop="qianjiaMeterReceived" label="实领" width="80" align="center" />
                      <el-table-column prop="qianjiaMeterConsumed" label="实耗" width="80" align="center" />
                      <el-table-column prop="qianjiaMeterPrice" label="单价(元/块)" width="100" align="center">
                        <template #default="{ row }">
                          ¥{{ formatNumber(row.qianjiaMeterPrice) }}
                        </template>
                      </el-table-column>
                    </el-table-column>
                  </el-table-column>
                  
                  <!-- 表后 -->
                  <el-table-column label="表后" align="center">
                    <el-table-column label="灶前阀(个)" align="center">
                      <el-table-column prop="stoveFrontValveReceived" label="实领" width="80" align="center" />
                      <el-table-column prop="stoveFrontValveConsumed" label="实耗" width="80" align="center" />
                      <el-table-column prop="stoveFrontValvePrice" label="单价(元/个)" width="100" align="center">
                        <template #default="{ row }">
                          ¥{{ formatNumber(row.stoveFrontValvePrice) }}
                        </template>
                      </el-table-column>
                    </el-table-column>
                    <el-table-column label="涂覆钢管(米)" align="center">
                      <el-table-column prop="coatedSteelPipeReceived" label="实领" width="80" align="center" />
                      <el-table-column prop="coatedSteelPipeConsumed" label="实耗" width="80" align="center" />
                      <el-table-column prop="coatedSteelPipePrice" label="单价(元/米)" width="100" align="center">
                        <template #default="{ row }">
                          ¥{{ formatNumber(row.coatedSteelPipePrice) }}
                        </template>
                      </el-table-column>
                    </el-table-column>
                    <el-table-column label="波纹管(米)" align="center">
                      <el-table-column prop="corrugatedPipeReceived" label="实领" width="80" align="center" />
                      <el-table-column prop="corrugatedPipeConsumed" label="实耗" width="80" align="center" />
                      <el-table-column prop="corrugatedPipePrice" label="单价(元/米)" width="100" align="center">
                        <template #default="{ row }">
                          ¥{{ formatNumber(row.corrugatedPipePrice) }}
                        </template>
                      </el-table-column>
                    </el-table-column>
                    <el-table-column label="预制短管(米)" align="center">
                      <el-table-column prop="prefabricatedPipeReceived" label="实领" width="80" align="center" />
                      <el-table-column prop="prefabricatedPipeConsumed" label="实耗" width="80" align="center" />
                      <el-table-column prop="prefabricatedPipePrice" label="单价(元/米)" width="100" align="center">
                        <template #default="{ row }">
                          ¥{{ formatNumber(row.prefabricatedPipePrice) }}
                        </template>
                      </el-table-column>
                    </el-table-column>
                  </el-table-column>
                </el-table-column>
                
                <!-- 表前安装甲供材料金额 -->
                <el-table-column label="表前安装甲供材料金额(元)" align="center">
                  <el-table-column prop="preMeterMaterialsReceivedAmount" label="实领" width="120" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.preMeterMaterialsReceivedAmount) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="preMeterMaterialsConsumedAmount" label="实耗" width="120" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.preMeterMaterialsConsumedAmount) }}
                    </template>
                  </el-table-column>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="户内" name="indoor">
            <div class="tab-content">
              <el-table :data="indoorData" style="width: 100%" border show-summary :summary-method="getIndoorSummary" v-loading="loading">
                <el-table-column prop="serialNo" label="序号" width="60" align="center" />
                <el-table-column prop="projectName" label="单项工程名称" width="200" />
                
                <!-- 甲供材料-户内 -->
                <el-table-column label="甲供材料-户内" align="center">
                  <el-table-column label="灶前阀(个)" align="center">
                    <el-table-column prop="stoveFrontValveReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="stoveFrontValveConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="stoveFrontValvePrice" label="单价(元/个)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.stoveFrontValvePrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                  
                  <el-table-column label="涂覆钢管(米)" align="center">
                    <el-table-column prop="coatedSteelPipeReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="coatedSteelPipeConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="coatedSteelPipePrice" label="单价(元/米)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.coatedSteelPipePrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                  
                  <el-table-column label="波纹管(米)" align="center">
                    <el-table-column prop="corrugatedPipeReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="corrugatedPipeConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="corrugatedPipePrice" label="单价(元/米)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.corrugatedPipePrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                  
                  <el-table-column label="输送波纹管防护钢板(直板)" align="center">
                    <el-table-column prop="protectionPlateStraightReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="protectionPlateStraightConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="protectionPlateStraightPrice" label="单价(元/个)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.protectionPlateStraightPrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                  
                  <el-table-column label="输送波纹管防护钢板(外、侧弯)" align="center">
                    <el-table-column prop="protectionPlateBendReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="protectionPlateBendConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="protectionPlateBendPrice" label="单价(元/个)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.protectionPlateBendPrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                </el-table-column>
                
                <!-- 户内安装甲供材料金额 -->
                <el-table-column label="户内安装甲供材料金额(元)" align="center">
                  <el-table-column prop="indoorMaterialsReceivedAmount" label="实领" width="120" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.indoorMaterialsReceivedAmount) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="indoorMaterialsConsumedAmount" label="实耗" width="120" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.indoorMaterialsConsumedAmount) }}
                    </template>
                  </el-table-column>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="超领材料" name="over-received">
            <div class="tab-content">
              <el-table :data="overReceivedData" style="width: 100%" border show-summary :summary-method="getOverReceivedSummary" v-loading="loading">
                <el-table-column prop="serialNo" label="序号" width="60" align="center" />
                <el-table-column prop="materialName" label="材料名称" width="200" />
                <el-table-column prop="specification" label="规格" width="150" />
                <el-table-column prop="unit" label="单位" width="80" align="center" />
                
                <!-- 损耗率以内 -->
                <el-table-column prop="overQuantityWithinRate" label="超领量(损耗率以内)" width="150" align="center" />
                <el-table-column prop="unitPriceWithinRate" label="含税材料单价(元)(损耗率以内)" width="180" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.unitPriceWithinRate) }}
                  </template>
                </el-table-column>
                
                <!-- 损耗率以外 -->
                <el-table-column prop="overQuantityBeyondRate" label="超领量(损耗率以外)" width="150" align="center" />
                <el-table-column prop="unitPriceBeyondRate" label="含税材料单价(元)(损耗率以外)" width="180" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.unitPriceBeyondRate) }}
                  </template>
                </el-table-column>
                
                <el-table-column prop="totalPrice" label="合价(元)" width="120" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.totalPrice) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="管件（总）" name="fittings-total">
            <div class="tab-content">
              <el-table :data="fittingsTotalData" style="width: 100%" border show-summary :summary-method="getFittingsTotalSummary" v-loading="loading">
                <el-table-column prop="serialNo" label="序号" width="60" align="center" />
                <el-table-column prop="projectName" label="单项工程名称" width="150" />
                
                <!-- 甲供材料-户内 -->
                <el-table-column label="甲供材料-户内" align="center">
                  <el-table-column label="波纹管快速外螺纹接头(个)" align="center">
                    <el-table-column prop="corrugatedConnectorReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="corrugatedConnectorConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="corrugatedConnectorPrice" label="单价(元/个)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.corrugatedConnectorPrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                  
                  <el-table-column label="镀锌90°弯头(个)" align="center">
                    <el-table-column prop="galvanizedElbow90Received" label="实领" width="80" align="center" />
                    <el-table-column prop="galvanizedElbow90Consumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="galvanizedElbow90Price" label="单价(元/个)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.galvanizedElbow90Price) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                  
                  <el-table-column label="镀锌六角外丝(个)" align="center">
                    <el-table-column prop="galvanizedHexOuterReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="galvanizedHexOuterConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="galvanizedHexOuterPrice" label="单价(元/个)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.galvanizedHexOuterPrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                  
                  <el-table-column label="镀锌内丝(个)" align="center">
                    <el-table-column prop="galvanizedInnerReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="galvanizedInnerConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="galvanizedInnerPrice" label="单价(元/个)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.galvanizedInnerPrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                  
                  <el-table-column label="镀锌内外丝弯头(个)" align="center">
                    <el-table-column prop="galvanizedInnerOuterElbowReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="galvanizedInnerOuterElbowConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="galvanizedInnerOuterElbowPrice" label="单价(元/个)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.galvanizedInnerOuterElbowPrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                  
                  <el-table-column label="镀锌三通(个)" align="center">
                    <el-table-column prop="galvanizedTeeReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="galvanizedTeeConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="galvanizedTeePrice" label="单价(元/个)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.galvanizedTeePrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                  
                  <el-table-column label="不锈钢管卡架(钢管用)(个)" align="center">
                    <el-table-column prop="stainlessSteelClampReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="stainlessSteelClampConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="stainlessSteelClampPrice" label="单价(元/个)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.stainlessSteelClampPrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                  
                  <el-table-column label="定制镀锌补芯(个)" align="center">
                    <el-table-column prop="customGalvanizedBushingReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="customGalvanizedBushingConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="customGalvanizedBushingPrice" label="单价(元/个)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.customGalvanizedBushingPrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                  
                  <el-table-column label="表具波纹管(个)" align="center">
                    <el-table-column prop="meterCorrugatedPipeReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="meterCorrugatedPipeConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="meterCorrugatedPipePrice" label="单价(元/个)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.meterCorrugatedPipePrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                  
                  <el-table-column label="燃气表托架(个)" align="center">
                    <el-table-column prop="gasMeterBracketReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="gasMeterBracketConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="gasMeterBracketPrice" label="单价(元/个)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.gasMeterBracketPrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                  
                  <el-table-column label="防盗气锁卡(个)" align="center">
                    <el-table-column prop="antiTheftGasLockReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="antiTheftGasLockConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="antiTheftGasLockPrice" label="单价(元/个)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.antiTheftGasLockPrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                  
                  <el-table-column label="镀锌管堵(个)" align="center">
                    <el-table-column prop="galvanizedPlugReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="galvanizedPlugConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="galvanizedPlugPrice" label="单价(元/个)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.galvanizedPlugPrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                  
                  <el-table-column label="输送波纹管防护钢板（直板）(个)" align="center">
                    <el-table-column prop="protectionPlateStraightReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="protectionPlateStraightConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="protectionPlateStraightPrice" label="单价(元/个)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.protectionPlateStraightPrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                  
                  <el-table-column label="输送波纹管防护钢板（外、侧弯）(个)" align="center">
                    <el-table-column prop="protectionPlateBendReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="protectionPlateBendConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="protectionPlateBendPrice" label="单价(元/个)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.protectionPlateBendPrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                  
                  <el-table-column label="定中装饰盖(个)" align="center">
                    <el-table-column prop="decorativeCoverReceived" label="实领" width="80" align="center" />
                    <el-table-column prop="decorativeCoverConsumed" label="实耗" width="80" align="center" />
                    <el-table-column prop="decorativeCoverPrice" label="单价(元/个)" width="100" align="center">
                      <template #default="{ row }">
                        ¥{{ formatNumber(row.decorativeCoverPrice) }}
                      </template>
                    </el-table-column>
                  </el-table-column>
                </el-table-column>
                
                <el-table-column prop="actualMaterialCost" label="实际耗用甲供材料金额-户内（元）" width="200" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.actualMaterialCost) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="领料数及单价表" name="material-quantity-price">
            <div class="tab-content">
              <el-table :data="materialQuantityPriceData" style="width: 100%" border v-loading="loading">
                <el-table-column prop="materialCode" label="材料编码" width="120" />
                <el-table-column prop="materialName" label="材料名称" width="150" />
                <el-table-column prop="specification" label="规格型号" width="120" />
                <el-table-column prop="unit" label="单位" width="80" />
                <el-table-column prop="quantity" label="领料数量" width="100" />
                <el-table-column prop="unitPrice" label="单价" width="100">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.unitPrice) }}
                  </template>
                </el-table-column>
                <el-table-column prop="totalAmount" label="总金额" width="120">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.totalAmount) }}
                  </template>
                </el-table-column>
                <el-table-column prop="supplier" label="供应商" width="120" />
                <el-table-column prop="receiveDate" label="领料日期" width="120" />
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="其他增量工程" name="other-incremental">
            <div class="tab-content">
              <div class="empty-content">
                <el-empty description="暂无数据" />
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="挂表管件" name="meter-fittings">
            <div class="tab-content">
              <el-table :data="meterFittingsData" style="width: 100%" border show-summary :summary-method="getMeterFittingsSummary" v-loading="loading">
                <el-table-column prop="serialNo" label="序号" width="60" align="center" />
                
                <!-- 户主信息 -->
                <el-table-column label="户主信息" align="center">
                  <el-table-column prop="customerName" label="姓名" width="100" align="center" />
                  <el-table-column prop="customerCode" label="用户编号" width="120" align="center" />
                  <el-table-column prop="customerAddress" label="住址" width="200" align="center" />
                </el-table-column>
                
                <el-table-column prop="dispatchTime" label="派单时间" width="120" align="center" />
                <el-table-column prop="installTime" label="安装时间" width="120" align="center" />
                
                <!-- 管件统计 -->
                <el-table-column prop="corrugatedQuickConnector" label="波纹管快速外螺纹接头" width="120" align="center" />
                <el-table-column prop="galvanized90Elbow" label="镀锌90°弯头" width="100" align="center" />
                <el-table-column prop="galvanizedHexMaleThread" label="镀锌六角外丝" width="80" align="center" />
                <el-table-column prop="galvanizedFemaleThread" label="镀锌内丝" width="80" align="center" />
                <el-table-column prop="galvanizedMaleFemaleElbow" label="镀锌内外丝弯头" width="100" align="center" />
                <el-table-column prop="galvanizedTee" label="镀锌三通" width="80" align="center" />
                <el-table-column prop="stainlessSteelClamp" label="不锈钢管卡架(钢管用)" width="140" align="center" />
                <el-table-column prop="customGalvanizedBushing" label="定制镀锌补芯" width="100" align="center" />
                <el-table-column prop="meterCorrugatedPipe" label="表具波纹管" width="100" align="center" />
                <el-table-column prop="gasMeterBracket" label="燃气表托架" width="100" align="center" />
                <el-table-column prop="antiTheftGasLock" label="防盗气锁卡" width="100" align="center" />
                <el-table-column prop="galvanizedPlug" label="镀锌管堵" width="100" align="center" />
                <el-table-column prop="protectionPlateStraight" label="输送波纹管防护钢板（直板）" width="180" align="center" />
                <el-table-column prop="protectionPlateBend" label="输送波纹管防护钢板（外、侧弯）" width="200" align="center" />
                <el-table-column prop="decorativeCover" label="定中装饰盖" width="100" align="center" />
                <el-table-column prop="actualMaterialCost" label="实际耗用甲供材料金额-户内（元）" width="200" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.actualMaterialCost) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="零星管件" name="loose-fittings">
            <div class="tab-content">
              <el-table :data="looseFittingsData" style="width: 100%" border show-summary :summary-method="getLooseFittingsSummary" v-loading="loading">
                <el-table-column prop="serialNo" label="序号" width="60" align="center" />

                <!-- 户主信息 -->
                <el-table-column label="户主信息" align="center">
                  <el-table-column prop="customerName" label="姓名" width="100" align="center" />
                  <el-table-column prop="customerCode" label="编号" width="120" align="center" />
                  <el-table-column label="详细地址" align="center">
                    <el-table-column prop="fullAddress" label="详细地址 汇总" width="180" align="center" />
                    <el-table-column prop="communityName" label="小区名称" width="120" align="center" />
                    <el-table-column prop="buildingNo" label="楼橦" width="80" align="center" />
                    <el-table-column prop="roomNo" label="房号" width="80" align="center" />
                  </el-table-column>
                </el-table-column>

                <el-table-column prop="dispatchDate" label="派单日期" width="120" align="center" />
                <el-table-column prop="constructionDate" label="施工日期" width="120" align="center" />

                <!-- 管件统计 -->
                <el-table-column prop="corrugatedQuickConnector" label="波纹管快速外螺纹接头" width="120" align="center" />
                <el-table-column prop="galvanized90Elbow" label="镀锌90°弯头" width="100" align="center" />
                <el-table-column prop="galvanizedHexMaleThread" label="镀锌六角外丝" width="80" align="center" />
                <el-table-column prop="galvanizedFemaleThread" label="镀锌内丝" width="80" align="center" />
                <el-table-column prop="galvanizedMaleFemaleElbow" label="镀锌内外丝弯头" width="100" align="center" />
                <el-table-column prop="galvanizedTee" label="镀锌三通" width="80" align="center" />
                <el-table-column prop="stainlessSteelClamp" label="不锈钢管卡架(钢管用)" width="140" align="center" />
                <el-table-column prop="customGalvanizedBushing" label="定制镀锌补芯" width="100" align="center" />
                <el-table-column prop="meterCorrugatedPipe" label="表具波纹管" width="100" align="center" />
                <el-table-column prop="gasMeterBracket" label="燃气表托架" width="100" align="center" />
                <el-table-column prop="antiTheftGasLock" label="防盗气锁卡" width="100" align="center" />
                <el-table-column prop="galvanizedPlug" label="镀锌管堵" width="100" align="center" />
                <el-table-column prop="protectionPlateStraight" label="输送波纹管防护钢板（直板）" width="180" align="center" />
                <el-table-column prop="protectionPlateBend" label="输送波纹管防护钢板（外、侧弯）" width="200" align="center" />
                <el-table-column prop="decorativeCover" label="定中装饰盖" width="100" align="center" />
                <el-table-column prop="actualMaterialCost" label="实际耗用甲供材料金额-户内（元）" width="200" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.actualMaterialCost) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="二次安装管件" name="secondary-fittings">
            <div class="tab-content">
              <el-table :data="secondaryFittingsData" style="width: 100%" border show-summary :summary-method="getSecondaryFittingsSummary" v-loading="loading">
                <el-table-column prop="serialNo" label="序号" width="60" align="center" />
                
                <!-- 户主信息 -->
                <el-table-column label="户主信息" align="center">
                  <el-table-column prop="customerName" label="姓名" width="100" align="center" />
                  <el-table-column prop="customerCode" label="编号" width="120" align="center" />
                  <el-table-column label="详细地址" align="center">
                    <el-table-column prop="communityName" label="小区名称" width="120" align="center" />
                    <el-table-column prop="buildingNo" label="楼橦" width="80" align="center" />
                    <el-table-column prop="roomNo" label="房号" width="80" align="center" />
                  </el-table-column>
                </el-table-column>
                
                <el-table-column prop="dispatchDate" label="派单日期" width="120" align="center" />
                <el-table-column prop="constructionDate" label="施工日期" width="120" align="center" />
                
                <!-- 管件统计 -->
                <el-table-column prop="customGalvanizedBushing" label="定制镀锌补芯" width="100" align="center" />
                <el-table-column prop="meterCorrugatedPipe" label="表具波纹管" width="100" align="center" />
                <el-table-column prop="gasMeterBracket" label="燃气表托架" width="100" align="center" />
                <el-table-column prop="antiTheftGasLock" label="防盗气锁卡" width="100" align="center" />
                <el-table-column prop="actualMaterialCost" label="实际耗用甲供材料金额-户内（元）" width="200" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.actualMaterialCost) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="管件统计表" name="fittings-statistics">
            <div class="tab-content">
              <el-table :data="fittingsStatisticsData" style="width: 100%" border show-summary :summary-method="getFittingsStatisticsSummary" v-loading="loading">
                <el-table-column prop="serialNo" label="序号" width="60" align="center" />
                <el-table-column prop="projectName" label="单项工程名称" width="200" />
                <el-table-column prop="corrugatedConnector" label="波纹管接头（个）" width="100" align="center" />
                <el-table-column prop="galvanized90Elbow" label="镀锌90°弯头（个）" width="120" align="center" />
                <el-table-column prop="galvanizedHexMaleThread" label="镀锌六角外丝（个）" width="120" align="center" />
                <el-table-column prop="galvanizedFemaleThread" label="镀锌内丝（个）" width="100" align="center" />
                <el-table-column prop="galvanizedMaleFemaleElbow" label="镀锌内外丝弯头（个）" width="140" align="center" />
                <el-table-column prop="galvanizedTee" label="镀锌三通（个）" width="100" align="center" />
                <el-table-column prop="stainlessSteelClamp" label="不锈钢管卡架(钢管用)（个）" width="160" align="center" />
                <el-table-column prop="customGalvanizedBushing" label="定制镀锌补芯（个）" width="120" align="center" />
                <el-table-column prop="corrugatedShortPipe" label="波纹管短管（个）" width="120" align="center" />
                <el-table-column prop="meterCorrugatedPipe" label="表具波纹管（个）" width="120" align="center" />
                <el-table-column prop="gasMeterBracket" label="燃气表托架（个）" width="120" align="center" />
                <el-table-column prop="antiTheftGasLock" label="防盗气锁卡（个）" width="120" align="center" />
                <el-table-column prop="galvanizedPlug" label="镀锌管堵（个）" width="100" align="center" />
                <el-table-column prop="protectionPlateStraight" label="输送波纹管防护钢板（直板）（个）" width="180" align="center" />
                <el-table-column prop="protectionPlateBend" label="输送波纹管防护钢板（外、侧弯）（个）" width="200" align="center" />
                <el-table-column prop="decorativeCover" label="定中装饰盖（个）" width="120" align="center" />
                <el-table-column prop="actualMaterialCost" label="实际耗用甲供材料金额-户内（元）" width="200" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.actualMaterialCost) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="明装及暗埋(半月板" name="exposed-buried-half-moon">
            <div class="tab-content">
              <el-table :data="exposedBuriedHalfMoonData" style="width: 100%" border show-summary :summary-method="getExposedBuriedHalfMoonSummary" v-loading="loading">
                <el-table-column prop="serialNo" label="序号" width="60" align="center" />

                <!-- 户主信息 -->
                <el-table-column label="户主信息" align="center">
                  <el-table-column prop="customerName" label="姓名" width="100" align="center" />
                  <el-table-column prop="customerCode" label="用户编号" width="120" align="center" />
                  <el-table-column label="住址" align="center">
                    <el-table-column prop="communityName" label="小区名称" width="120" align="center" />
                    <el-table-column prop="buildingNo" label="楼橦" width="80" align="center" />
                    <el-table-column prop="roomNo" label="房号" width="80" align="center" />
                  </el-table-column>
                </el-table-column>

                <el-table-column prop="dispatchTime" label="派单时间" width="120" align="center" />
                <el-table-column prop="installTime" label="安装时间" width="120" align="center" />

                <!-- 煤气表信息 -->
                <el-table-column label="煤气表信息" align="center">
                  <el-table-column prop="hasMeterBox" label="有/无 表箱" width="100" align="center" />
                  <el-table-column prop="preMeterValve" label="表前阀" width="80" align="center" />
                </el-table-column>

                <!-- 包干价 -->
                <el-table-column label="包干价（元）" align="center">
                  <el-table-column prop="preMeterPrice" label="表前" width="100" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.preMeterPrice) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="indoorPrice" label="户内" width="100" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.indoorPrice) }}
                    </template>
                  </el-table-column>
                </el-table-column>

                <el-table-column prop="stoveFrontValveQty" label="灶前阀数量（个）" width="130" align="center" />

                <!-- 表前管道耗材 -->
                <el-table-column label="表前管道耗材（米）" align="center">
                  <el-table-column prop="preMeterSteelPipe" label="钢管" width="80" align="center" />
                  <el-table-column prop="preMeterCoatedSteelPipe" label="涂覆钢管" width="100" align="center" />
                  <el-table-column prop="preMeterCorrugatedPipe" label="波纹管" width="80" align="center" />
                </el-table-column>

                <!-- 户内管道耗材 -->
                <el-table-column label="户内管道耗材（米）" align="center">
                  <el-table-column prop="indoorSteelPipe" label="钢管" width="80" align="center" />
                  <el-table-column prop="indoorCoatedSteelPipe" label="涂覆钢管" width="100" align="center" />
                  <el-table-column prop="indoorCorrugatedPipe" label="波纹管" width="80" align="center" />
                </el-table-column>

                <el-table-column prop="mechanicalMeter" label="机械表" width="80" align="center" />

                <!-- 表前阀 -->
                <el-table-column label="表前阀" align="center">
                  <el-table-column prop="lowPressureRegulator" label="低低压调压器（个）" width="140" align="center" />
                  <el-table-column prop="corrugatedConnectLong" label="波纹连接管（1.2m,1m）" width="160" align="center" />
                  <el-table-column prop="corrugatedConnectShort" label="波纹连接管（0.8m,0.5m）" width="160" align="center" />
                  <el-table-column prop="prefabShort12" label="预制短管12cm" width="120" align="center" />
                  <el-table-column prop="prefabShort18" label="预制短管18cm" width="120" align="center" />
                </el-table-column>

                <el-table-column prop="preMeterInstallCost" label="表前安装成本（元）" width="140" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.preMeterInstallCost) }}
                  </template>
                </el-table-column>
                <el-table-column prop="indoorInstallCost" label="户内安装成本（元）" width="140" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.indoorInstallCost) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="明装及暗埋（未用半月板保护）" name="exposed-buried-no-protection">
            <div class="tab-content">
              <el-table :data="exposedBuriedNoProtectionData" style="width: 100%" border show-summary :summary-method="getExposedBuriedNoProtectionSummary" v-loading="loading">
                <el-table-column prop="serialNo" label="序号" width="60" align="center" />

                <!-- 户主信息 -->
                <el-table-column label="户主信息" align="center">
                  <el-table-column label="住址 汇总" align="center">
                    <el-table-column prop="communityName" label="小区名称" width="120" align="center" />
                    <el-table-column prop="buildingNo" label="楼橦" width="80" align="center" />
                    <el-table-column prop="roomNo" label="房号" width="80" align="center" />
                  </el-table-column>
                </el-table-column>

                <el-table-column prop="dispatchTime" label="派单时间" width="120" align="center" />
                <el-table-column prop="installTime" label="安装时间" width="120" align="center" />

                <!-- 煤气表信息 -->
                <el-table-column label="煤气表信息" align="center">
                  <el-table-column prop="hasMeterBox" label="有/无 表箱" width="100" align="center" />
                  <el-table-column prop="preMeterValve" label="表前阀" width="80" align="center" />
                </el-table-column>

                <!-- 包干价 -->
                <el-table-column label="包干价（元）" align="center">
                  <el-table-column prop="preMeterPrice" label="表前" width="100" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.preMeterPrice) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="indoorPrice" label="户内" width="100" align="center">
                    <template #default="{ row }">
                      ¥{{ formatNumber(row.indoorPrice) }}
                    </template>
                  </el-table-column>
                </el-table-column>

                <el-table-column prop="stoveFrontValveQty" label="灶前阀数量（个）" width="130" align="center" />

                <!-- 表前管道耗材 -->
                <el-table-column label="表前管道耗材（米）" align="center">
                  <el-table-column prop="preMeterSteelPipe" label="钢管" width="80" align="center" />
                  <el-table-column prop="preMeterCoatedSteelPipe" label="涂覆钢管" width="100" align="center" />
                  <el-table-column prop="preMeterCorrugatedPipe" label="波纹管" width="80" align="center" />
                </el-table-column>

                <!-- 户内管道耗材 -->
                <el-table-column label="户内管道耗材（米）" align="center">
                  <el-table-column prop="indoorSteelPipe" label="钢管" width="80" align="center" />
                  <el-table-column prop="indoorCoatedSteelPipe" label="涂覆钢管" width="100" align="center" />
                  <el-table-column prop="indoorCorrugatedPipe" label="波纹管" width="80" align="center" />
                </el-table-column>

                <el-table-column prop="mechanicalMeter" label="机械表" width="80" align="center" />

                <!-- 表前阀 -->
                <el-table-column label="表前阀" align="center">
                  <el-table-column prop="lowPressureRegulator" label="低低压调压器（个）" width="140" align="center" />
                  <el-table-column prop="corrugatedConnectLong" label="波纹连接管（1.2m,1m）" width="160" align="center" />
                  <el-table-column prop="corrugatedConnectShort" label="波纹连接管（0.8m,0.5m）" width="160" align="center" />
                  <el-table-column prop="prefabShort12" label="预制短管12cm" width="120" align="center" />
                  <el-table-column prop="prefabShort18" label="预制短管18cm" width="120" align="center" />
                </el-table-column>

                <el-table-column prop="preMeterInstallCost" label="表前安装成本（元）" width="140" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.preMeterInstallCost) }}
                  </template>
                </el-table-column>
                <el-table-column prop="indoorInstallCost" label="户内安装成本（元）" width="140" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.indoorInstallCost) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="零星安装（半月板" name="loose-installation-half-moon">
            <div class="tab-content">
              <el-table :data="looseInstallationHalfMoonData" style="width: 100%" border show-summary :summary-method="getLooseInstallationHalfMoonSummary" v-loading="loading">
                <!-- 户主信息 -->
                <el-table-column label="户主信息" align="center">
                  <el-table-column prop="customerName" label="姓名" width="100" align="center" />
                  <el-table-column prop="customerCode" label="编号" width="120" align="center" />
                  <el-table-column label="详细地址" align="center">
                    <el-table-column prop="communityName" label="小区名称" width="120" align="center" />
                    <el-table-column prop="buildingNo" label="楼橦" width="80" align="center" />
                    <el-table-column prop="roomNo" label="房号" width="80" align="center" />
                  </el-table-column>
                </el-table-column>

                <el-table-column prop="dispatchDate" label="派单日期" width="120" align="center" />
                <el-table-column prop="constructionDate" label="施工日期" width="120" align="center" />
                <el-table-column prop="firePointQty" label="火点数量（个）" width="120" align="center" />
                <el-table-column prop="stoveFrontValveQty" label="灶前阀数量（个）" width="130" align="center" />

                <!-- 安装项目 -->
                <el-table-column label="安装项目" align="center">
                  <el-table-column prop="gasMeterBox" label="燃气表箱(个)" width="120" align="center" />
                  <el-table-column prop="preMeterValve" label="表前阀(个)" width="100" align="center" />
                  <el-table-column prop="lowPressureRegulator" label="低低压调压器" width="120" align="center" />
                  <el-table-column prop="gasMeter" label="燃气表（个）" width="120" align="center" />
                </el-table-column>

                <el-table-column prop="packagePrice" label="包干价（元）" width="120" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.packagePrice) }}
                  </template>
                </el-table-column>

                <!-- 管道材料耗用统计 -->
                <el-table-column label="管道材料耗用统计（米）" align="center">
                  <el-table-column prop="dn15SteelPipe" label="DN15钢管" width="100" align="center" />
                  <el-table-column prop="dn15CoatedSteelPipe" label="DN15涂覆钢管" width="130" align="center" />
                  <el-table-column prop="corrugatedPipe" label="波纹管" width="100" align="center" />
                </el-table-column>

                <el-table-column prop="corrugatedConnectStove" label="波纹管连接（灶）" width="140" align="center" />
                <el-table-column prop="corrugatedConnectHeat" label="波纹管连接（热）" width="140" align="center" />
                <el-table-column prop="prefabShort12" label="预制短管12cm" width="120" align="center" />
                <el-table-column prop="prefabShort18" label="预制短管18cm" width="120" align="center" />
                <el-table-column prop="singleHouseholdCost" label="单户户内零星安装工程造价（元）" width="200" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.singleHouseholdCost) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="零星安装（未用半月板保护）" name="loose-installation-no-protection">
            <div class="tab-content">
              <el-table :data="looseInstallationNoProtectionData" style="width: 100%" border show-summary :summary-method="getLooseInstallationNoProtectionSummary" v-loading="loading">
                <el-table-column prop="serialNo" label="序号" width="60" align="center" />

                <!-- 户主信息 -->
                <el-table-column label="户主信息" align="center">
                  <el-table-column prop="customerName" label="姓名" width="100" align="center" />
                  <el-table-column prop="customerCode" label="编号" width="120" align="center" />
                  <el-table-column label="详细地址" align="center">
                    <el-table-column prop="communityName" label="小区名称" width="120" align="center" />
                    <el-table-column prop="buildingNo" label="楼橦" width="80" align="center" />
                    <el-table-column prop="roomNo" label="房号" width="80" align="center" />
                  </el-table-column>
                </el-table-column>

                <el-table-column prop="dispatchDate" label="派单日期" width="120" align="center" />
                <el-table-column prop="constructionDate" label="施工日期" width="120" align="center" />
                <el-table-column prop="firePointQty" label="火点数量（个）" width="120" align="center" />
                <el-table-column prop="stoveFrontValveQty" label="灶前阀数量（个）" width="130" align="center" />

                <!-- 安装项目 -->
                <el-table-column label="安装项目" align="center">
                  <el-table-column prop="gasMeterBox" label="燃气表箱(个)" width="120" align="center" />
                  <el-table-column prop="preMeterValve" label="表前阀(个)" width="100" align="center" />
                  <el-table-column prop="lowPressureRegulator" label="低低压调压器" width="120" align="center" />
                  <el-table-column prop="gasMeter" label="燃气表（个）" width="120" align="center" />
                </el-table-column>

                <el-table-column prop="packagePrice" label="包干价（元）" width="120" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.packagePrice) }}
                  </template>
                </el-table-column>

                <!-- 管道材料耗用统计 -->
                <el-table-column label="管道材料耗用统计（米）" align="center">
                  <el-table-column prop="dn15SteelPipe" label="DN15钢管" width="100" align="center" />
                  <el-table-column prop="dn15CoatedSteelPipe" label="DN15涂覆钢管" width="130" align="center" />
                  <el-table-column prop="corrugatedPipe" label="波纹管" width="100" align="center" />
                </el-table-column>

                <el-table-column prop="corrugatedConnectStove" label="波纹管连接（灶）" width="140" align="center" />
                <el-table-column prop="corrugatedConnectHeat" label="波纹管连接（热）" width="140" align="center" />
                <el-table-column prop="prefabShort12" label="预制短管12cm" width="120" align="center" />
                <el-table-column prop="prefabShort18" label="预制短管18cm" width="120" align="center" />
                <el-table-column prop="singleHouseholdCost" label="单户户内零星安装工程造价（元）" width="200" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.singleHouseholdCost) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="不可用燃气表换表" name="unusable-meter-change">
            <div class="tab-content">
              <el-table :data="unusableMeterChangeData" style="width: 100%" border show-summary :summary-method="getUnusableMeterChangeSummary" v-loading="loading">
                <el-table-column prop="serialNo" label="序号" width="60" align="center" />

                <!-- 户主信息 -->
                <el-table-column label="户主信息" align="center">
                  <el-table-column prop="customerName" label="姓名" width="100" align="center" />
                  <el-table-column prop="customerCode" label="编号" width="120" align="center" />
                  <el-table-column label="详细地址" align="center">
                    <el-table-column prop="communityName" label="小区名称" width="120" align="center" />
                    <el-table-column prop="buildingNo" label="楼橦" width="80" align="center" />
                    <el-table-column prop="roomNo" label="房号" width="80" align="center" />
                  </el-table-column>
                </el-table-column>

                <el-table-column prop="dispatchDate" label="派单日期" width="120" align="center" />
                <el-table-column prop="constructionDate" label="施工日期" width="120" align="center" />
                <el-table-column prop="firePointQty" label="火点数量（个）" width="120" align="center" />
                <el-table-column prop="stoveFrontValveQty" label="灶前阀数量（个）" width="130" align="center" />

                <!-- 安装项目 -->
                <el-table-column label="安装项目" align="center">
                  <el-table-column prop="gasMeterBox" label="燃气表箱(个)" width="120" align="center" />
                  <el-table-column prop="preMeterValve" label="表前阀(个)" width="100" align="center" />
                  <el-table-column prop="lowPressureRegulator" label="低低压调压器" width="120" align="center" />
                  <el-table-column prop="gasMeter" label="燃气表（个）" width="120" align="center" />
                </el-table-column>

                <el-table-column prop="packagePrice" label="包干价（元）" width="120" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.packagePrice) }}
                  </template>
                </el-table-column>

                <!-- 管道材料耗用统计 -->
                <el-table-column label="管道材料耗用统计（米）" align="center">
                  <el-table-column prop="dn15SteelPipe" label="DN15钢管" width="100" align="center" />
                  <el-table-column prop="dn15CoatedSteelPipe" label="DN15涂覆钢管" width="130" align="center" />
                  <el-table-column prop="corrugatedPipe" label="波纹管" width="100" align="center" />
                </el-table-column>

                <el-table-column prop="corrugatedConnectStove" label="波纹管连接（灶）" width="140" align="center" />
                <el-table-column prop="corrugatedConnectHeat" label="波纹管连接（热）" width="140" align="center" />
                <el-table-column prop="prefabShort12" label="预制短管12cm" width="120" align="center" />
                <el-table-column prop="prefabShort18" label="预制短管18cm" width="120" align="center" />
                <el-table-column prop="singleHouseholdCost" label="单户户内零星安装工程造价（元）" width="200" align="center">
                  <template #default="{ row }">
                    ¥{{ formatNumber(row.singleHouseholdCost) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'

// 搜索表单
const searchForm = reactive({
  periodRange: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 表格数据
const recordsData = ref([])
const loading = ref(false)

// 活跃标签页
const activeTab = ref('cover')

// 汇总信息
const summary = ref({
  totalOrders: 0,
  completedOrders: 0,
  pendingOrders: 0,
  totalAmount: 0
})

// 工单明细数据
const orderDetails = ref([])

// 成本分析数据
const costAnalysis = ref([])

// 利润统计数据
const profitStats = ref([])

// 封面数据
const coverData = ref({
  companyName: '',
  projectName: '清远港华燃气有限公司工单结算项目',
  ceaCode: '',
  submittedLaborCost: 15000.00,
  secondReviewCost: 12000.00,
  settlementNumber: 'JS202401001',
  amountPayable: 12500.00,
  amountPayableChinese: '壹万贰仟伍佰元整',
  constructionUnit: '',
  clientUnit: '',
  totalCost: 12500.00,
  laborSettlement: 8000.00,
  materialSettlement: 4500.00
})

// 公司名称选项
const companyNameOptions = ref(['清远港华燃气有限公司'])

// 施工单位选项
const constructionUnitOptions = ref(['清远港华燃气有限公司'])

// 建设单位选项
const clientUnitOptions = ref(['清远港华燃气有限公司'])

// 应付施工单位金额数据
const payableAmountData = ref([])



// 表前数据
const preMeterData = ref([])

// 户内数据
const indoorData = ref([])

// 超领材料数据
const overReceivedData = ref([])

// 管件（总）数据
const fittingsTotalData = ref([])

// 领料数及单价表数据
const materialQuantityPriceData = ref([])

// 挂表管件数据
const meterFittingsData = ref([])

// 零星管件数据
const looseFittingsData = ref([])

// 二次安装管件数据
const secondaryFittingsData = ref([])

// 管件统计表数据
const fittingsStatisticsData = ref([])

// 明装及暗埋(半月板)数据
const exposedBuriedHalfMoonData = ref([])

// 明装及暗埋（未用半月板保护）数据
const exposedBuriedNoProtectionData = ref([])

// 零星安装（半月板）数据
const looseInstallationHalfMoonData = ref([])

// 零星安装（未用半月板保护）数据
const looseInstallationNoProtectionData = ref([])

// 不可用燃气表换表数据
const unusableMeterChangeData = ref([])

// 移除mock数据，使用真实API

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    // 调用真实API
    const response = await request.get('/api/loose-orders/balance/records', {
      params: {
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...searchForm
      }
    })
    
    if (response.success) {
      recordsData.value = response.data.list || []
      pagination.total = response.data.total || 0
    
    // 计算汇总信息
      summary.value = response.data.summary || {
        totalOrders: 0,
        completedOrders: 0,
        pendingOrders: 0,
        totalAmount: 0
      }
    }
    
    // 模拟工单明细数据
    orderDetails.value = [
      {
        orderNo: 'WO2024001',
        customerName: '张三',
        address: '清远市XX区XX小区',
        orderType: '安检',
        workerName: '李师傅',
        createTime: '2024-01-15',
        completeTime: '2024-01-16',
        amount: 500.00,
        status: 'confirmed'
      },
      {
        orderNo: 'WO2024002',
        customerName: '李四',
        address: '清远市XX区XX小区',
        orderType: '维修',
        workerName: '王师傅',
        createTime: '2024-01-16',
        completeTime: '2024-01-17',
        amount: 800.00,
        status: 'confirmed'
      }
    ]
    
    // 模拟成本分析数据
    costAnalysis.value = [
      {
        category: '人工成本',
        amount: 6000.00,
        percentage: 60.0,
        description: '师傅工资和奖金',
        updateTime: '2024-01-20'
      },
      {
        category: '物料成本',
        amount: 3000.00,
        percentage: 30.0,
        description: '维修材料和配件',
        updateTime: '2024-01-20'
      },
      {
        category: '其他成本',
        amount: 1000.00,
        percentage: 10.0,
        description: '运输、管理等费用',
        updateTime: '2024-01-20'
      }
    ]
    
    // 模拟利润统计数据
    profitStats.value = [
      {
        period: '2024-01',
        totalRevenue: 12500.00,
        totalCost: 10000.00,
        profit: 2500.00,
        profitRate: 20.0,
        orderCount: 25
      },
      {
        period: '2024-02',
        totalRevenue: 15000.00,
        totalCost: 12000.00,
        profit: 3000.00,
        profitRate: 20.0,
        orderCount: 30
      }
    ]
    
    // 模拟应付施工单位金额数据
    payableAmountData.value = [
      {
        serialNo: 1,
        projectName: '清远市XX小区燃气安装工程',
        preMeterLaborCost: 3000.00,
        indoorLaborCost: 5000.00,
        laborSubtotal: 8000.00,
        installedHouseholds: 25,
        gasMeterCost: 1500.00,
        postMeterCost: 1000.00,
        fittingsCost: 1000.00,
        materialSubtotal: 3500.00,
        actualReceivedAmount: 3200.00,
        overReceivedAmount: 300.00,
        totalProjectCost: 11500.00,
        payableAmount: 12500.00
      },
      {
        serialNo: 2,
        projectName: '清远市XX小区燃气维修工程',
        preMeterLaborCost: 2000.00,
        indoorLaborCost: 4000.00,
        laborSubtotal: 6000.00,
        installedHouseholds: 18,
        gasMeterCost: 800.00,
        postMeterCost: 700.00,
        fittingsCost: 1000.00,
        materialSubtotal: 2500.00,
        actualReceivedAmount: 2300.00,
        overReceivedAmount: 200.00,
        totalProjectCost: 8500.00,
        payableAmount: 9300.00
      },
      {
        serialNo: 3,
        projectName: '清远市XX小区燃气安检工程',
        preMeterLaborCost: 3500.00,
        indoorLaborCost: 5500.00,
        laborSubtotal: 9000.00,
        installedHouseholds: 30,
        gasMeterCost: 1200.00,
        postMeterCost: 500.00,
        fittingsCost: 300.00,
        materialSubtotal: 2000.00,
        actualReceivedAmount: 1800.00,
        overReceivedAmount: 200.00,
        totalProjectCost: 11000.00,
        payableAmount: 11500.00
      }
    ]

    
    // 模拟表前数据
    preMeterData.value = [
      {
        serialNo: 1,
        projectName: '清远市XX小区燃气安装工程',
        mechanicalConnectorReceived: 50,
        mechanicalConnectorConsumed: 48,
        mechanicalConnectorPrice: 25.00,
        qianjiaMeterReceived: 25,
        qianjiaMeterConsumed: 24,
        qianjiaMeterPrice: 500.00,
        stoveFrontValveReceived: 50,
        stoveFrontValveConsumed: 48,
        stoveFrontValvePrice: 28.00,
        coatedSteelPipeReceived: 200,
        coatedSteelPipeConsumed: 195,
        coatedSteelPipePrice: 12.50,
        corrugatedPipeReceived: 100,
        corrugatedPipeConsumed: 98,
        corrugatedPipePrice: 8.00,
        prefabricatedPipeReceived: 50,
        prefabricatedPipeConsumed: 48,
        prefabricatedPipePrice: 15.00,
        preMeterMaterialsReceivedAmount: 8500.00,
        preMeterMaterialsConsumedAmount: 8200.00
      },
      {
        serialNo: 2,
        projectName: '清远市XX小区燃气维修工程',
        mechanicalConnectorReceived: 30,
        mechanicalConnectorConsumed: 29,
        mechanicalConnectorPrice: 25.00,
        qianjiaMeterReceived: 15,
        qianjiaMeterConsumed: 14,
        qianjiaMeterPrice: 500.00,
        stoveFrontValveReceived: 30,
        stoveFrontValveConsumed: 29,
        stoveFrontValvePrice: 28.00,
        coatedSteelPipeReceived: 120,
        coatedSteelPipeConsumed: 118,
        coatedSteelPipePrice: 12.50,
        corrugatedPipeReceived: 60,
        corrugatedPipeConsumed: 59,
        corrugatedPipePrice: 8.00,
        prefabricatedPipeReceived: 30,
        prefabricatedPipeConsumed: 29,
        prefabricatedPipePrice: 15.00,
        preMeterMaterialsReceivedAmount: 5100.00,
        preMeterMaterialsConsumedAmount: 4950.00
      },
      {
        serialNo: 3,
        projectName: '清远市XX小区燃气安检工程',
        mechanicalConnectorReceived: 40,
        mechanicalConnectorConsumed: 39,
        mechanicalConnectorPrice: 25.00,
        qianjiaMeterReceived: 20,
        qianjiaMeterConsumed: 19,
        qianjiaMeterPrice: 500.00,
        stoveFrontValveReceived: 40,
        stoveFrontValveConsumed: 39,
        stoveFrontValvePrice: 28.00,
        coatedSteelPipeReceived: 160,
        coatedSteelPipeConsumed: 158,
        coatedSteelPipePrice: 12.50,
        corrugatedPipeReceived: 80,
        corrugatedPipeConsumed: 79,
        corrugatedPipePrice: 8.00,
        prefabricatedPipeReceived: 40,
        prefabricatedPipeConsumed: 39,
        prefabricatedPipePrice: 15.00,
        preMeterMaterialsReceivedAmount: 6800.00,
        preMeterMaterialsConsumedAmount: 6650.00
      }
    ]
    
    // 模拟户内数据
    indoorData.value = [
      {
        serialNo: 1,
        projectName: '清远市XX小区燃气安装工程',
        stoveFrontValveReceived: 100,
        stoveFrontValveConsumed: 98,
        stoveFrontValvePrice: 28.00,
        coatedSteelPipeReceived: 300,
        coatedSteelPipeConsumed: 295,
        coatedSteelPipePrice: 12.50,
        corrugatedPipeReceived: 200,
        corrugatedPipeConsumed: 198,
        corrugatedPipePrice: 8.00,
        protectionPlateStraightReceived: 50,
        protectionPlateStraightConsumed: 49,
        protectionPlateStraightPrice: 45.00,
        protectionPlateBendReceived: 30,
        protectionPlateBendConsumed: 29,
        protectionPlateBendPrice: 55.00,
        indoorMaterialsReceivedAmount: 12000.00,
        indoorMaterialsConsumedAmount: 11800.00
      },
      {
        serialNo: 2,
        projectName: '清远市XX小区燃气维修工程',
        stoveFrontValveReceived: 80,
        stoveFrontValveConsumed: 78,
        stoveFrontValvePrice: 28.00,
        coatedSteelPipeReceived: 240,
        coatedSteelPipeConsumed: 238,
        coatedSteelPipePrice: 12.50,
        corrugatedPipeReceived: 160,
        corrugatedPipeConsumed: 158,
        corrugatedPipePrice: 8.00,
        protectionPlateStraightReceived: 40,
        protectionPlateStraightConsumed: 39,
        protectionPlateStraightPrice: 45.00,
        protectionPlateBendReceived: 25,
        protectionPlateBendConsumed: 24,
        protectionPlateBendPrice: 55.00,
        indoorMaterialsReceivedAmount: 9600.00,
        indoorMaterialsConsumedAmount: 9400.00
      },
      {
        serialNo: 3,
        projectName: '清远市XX小区燃气安检工程',
        stoveFrontValveReceived: 120,
        stoveFrontValveConsumed: 118,
        stoveFrontValvePrice: 28.00,
        coatedSteelPipeReceived: 360,
        coatedSteelPipeConsumed: 355,
        coatedSteelPipePrice: 12.50,
        corrugatedPipeReceived: 240,
        corrugatedPipeConsumed: 238,
        corrugatedPipePrice: 8.00,
        protectionPlateStraightReceived: 60,
        protectionPlateStraightConsumed: 59,
        protectionPlateStraightPrice: 45.00,
        protectionPlateBendReceived: 35,
        protectionPlateBendConsumed: 34,
        protectionPlateBendPrice: 55.00,
        indoorMaterialsReceivedAmount: 14400.00,
        indoorMaterialsConsumedAmount: 14200.00
      }
    ]
    
    // 模拟超领材料数据
    overReceivedData.value = [
      {
        serialNo: 1,
        materialName: '灶前阀',
        specification: 'DN20',
        unit: '个',
        overQuantityWithinRate: 10,
        unitPriceWithinRate: 28.00,
        overQuantityBeyondRate: 5,
        unitPriceBeyondRate: 35.00,
        totalPrice: 455.00
      },
      {
        serialNo: 2,
        materialName: '涂覆钢管',
        specification: 'DN20',
        unit: '米',
        overQuantityWithinRate: 50,
        unitPriceWithinRate: 12.50,
        overQuantityBeyondRate: 20,
        unitPriceBeyondRate: 15.00,
        totalPrice: 925.00
      },
      {
        serialNo: 3,
        materialName: '波纹管',
        specification: 'DN20',
        unit: '米',
        overQuantityWithinRate: 30,
        unitPriceWithinRate: 8.00,
        overQuantityBeyondRate: 10,
        unitPriceBeyondRate: 10.00,
        totalPrice: 340.00
      },
      {
        serialNo: 4,
        materialName: '输送波纹管防护钢板',
        specification: '直板',
        unit: '个',
        overQuantityWithinRate: 8,
        unitPriceWithinRate: 45.00,
        overQuantityBeyondRate: 3,
        unitPriceBeyondRate: 55.00,
        totalPrice: 525.00
      },
      {
        serialNo: 5,
        materialName: '输送波纹管防护钢板',
        specification: '外、侧弯',
        unit: '个',
        overQuantityWithinRate: 5,
        unitPriceWithinRate: 55.00,
        overQuantityBeyondRate: 2,
        unitPriceBeyondRate: 65.00,
        totalPrice: 385.00
      }
    ]
    
    // 模拟管件（总）数据
    fittingsTotalData.value = [
      {
        serialNo: 1,
        projectName: '阳光小区A栋',
        corrugatedConnectorReceived: 80,
        corrugatedConnectorConsumed: 78,
        corrugatedConnectorPrice: 15.00,
        galvanizedElbow90Received: 120,
        galvanizedElbow90Consumed: 118,
        galvanizedElbow90Price: 8.50,
        galvanizedHexOuterReceived: 60,
        galvanizedHexOuterConsumed: 58,
        galvanizedHexOuterPrice: 12.00,
        galvanizedInnerReceived: 90,
        galvanizedInnerConsumed: 88,
        galvanizedInnerPrice: 10.00,
        galvanizedInnerOuterElbowReceived: 40,
        galvanizedInnerOuterElbowConsumed: 38,
        galvanizedInnerOuterElbowPrice: 18.00,
        galvanizedTeeReceived: 30,
        galvanizedTeeConsumed: 29,
        galvanizedTeePrice: 15.00,
        stainlessSteelClampReceived: 25,
        stainlessSteelClampConsumed: 24,
        stainlessSteelClampPrice: 12.00,
        customGalvanizedBushingReceived: 20,
        customGalvanizedBushingConsumed: 19,
        customGalvanizedBushingPrice: 8.00,
        meterCorrugatedPipeReceived: 35,
        meterCorrugatedPipeConsumed: 34,
        meterCorrugatedPipePrice: 25.00,
        gasMeterBracketReceived: 15,
        gasMeterBracketConsumed: 14,
        gasMeterBracketPrice: 35.00,
        antiTheftGasLockReceived: 10,
        antiTheftGasLockConsumed: 9,
        antiTheftGasLockPrice: 18.00,
        galvanizedPlugReceived: 8,
        galvanizedPlugConsumed: 7,
        galvanizedPlugPrice: 5.00,
        protectionPlateStraightReceived: 12,
        protectionPlateStraightConsumed: 11,
        protectionPlateStraightPrice: 45.00,
        protectionPlateBendReceived: 8,
        protectionPlateBendConsumed: 7,
        protectionPlateBendPrice: 55.00,
        decorativeCoverReceived: 6,
        decorativeCoverConsumed: 5,
        decorativeCoverPrice: 12.00,
        actualMaterialCost: 2845.50
      },
      {
        serialNo: 2,
        projectName: '绿城花园B区',
        corrugatedConnectorReceived: 100,
        corrugatedConnectorConsumed: 98,
        corrugatedConnectorPrice: 15.00,
        galvanizedElbow90Received: 150,
        galvanizedElbow90Consumed: 148,
        galvanizedElbow90Price: 8.50,
        galvanizedHexOuterReceived: 75,
        galvanizedHexOuterConsumed: 73,
        galvanizedHexOuterPrice: 12.00,
        galvanizedInnerReceived: 110,
        galvanizedInnerConsumed: 108,
        galvanizedInnerPrice: 10.00,
        galvanizedInnerOuterElbowReceived: 50,
        galvanizedInnerOuterElbowConsumed: 48,
        galvanizedInnerOuterElbowPrice: 18.00,
        galvanizedTeeReceived: 40,
        galvanizedTeeConsumed: 39,
        galvanizedTeePrice: 15.00,
        stainlessSteelClampReceived: 30,
        stainlessSteelClampConsumed: 29,
        stainlessSteelClampPrice: 12.00,
        customGalvanizedBushingReceived: 25,
        customGalvanizedBushingConsumed: 24,
        customGalvanizedBushingPrice: 8.00,
        meterCorrugatedPipeReceived: 45,
        meterCorrugatedPipeConsumed: 44,
        meterCorrugatedPipePrice: 25.00,
        gasMeterBracketReceived: 20,
        gasMeterBracketConsumed: 19,
        gasMeterBracketPrice: 35.00,
        antiTheftGasLockReceived: 15,
        antiTheftGasLockConsumed: 14,
        antiTheftGasLockPrice: 18.00,
        galvanizedPlugReceived: 12,
        galvanizedPlugConsumed: 11,
        galvanizedPlugPrice: 5.00,
        protectionPlateStraightReceived: 18,
        protectionPlateStraightConsumed: 17,
        protectionPlateStraightPrice: 45.00,
        protectionPlateBendReceived: 12,
        protectionPlateBendConsumed: 11,
        protectionPlateBendPrice: 55.00,
        decorativeCoverReceived: 8,
        decorativeCoverConsumed: 7,
        decorativeCoverPrice: 12.00,
        actualMaterialCost: 3568.75
      }
    ]
    
    // 模拟领料数及单价表数据
    materialQuantityPriceData.value = [
      {
        materialCode: 'M001',
        materialName: '燃气表',
        specification: 'G2.5',
        unit: '个',
        quantity: 50,
        unitPrice: 500.00,
        totalAmount: 25000.00,
        supplier: 'XX燃气设备有限公司',
        receiveDate: '2024-01-15'
      },
      {
        materialCode: 'M002',
        materialName: '燃气管道',
        specification: 'DN20',
        unit: '米',
        quantity: 200,
        unitPrice: 100.00,
        totalAmount: 20000.00,
        supplier: 'XX管材有限公司',
        receiveDate: '2024-01-16'
      }
    ]
    
    // 模拟挂表管件数据
    meterFittingsData.value = [
      {
        serialNo: 1,
        customerName: '张三',
        customerCode: 'YH001',
        customerAddress: '阳光小区A区101室',
        dispatchTime: '2024-01-15',
        installTime: '2024-01-16',
        corrugatedQuickConnector: 1,
        galvanized90Elbow: 2,
        galvanizedHexMaleThread: 1,
        galvanizedFemaleThread: 1,
        galvanizedMaleFemaleElbow: 1,
        galvanizedTee: 1,
        stainlessSteelClamp: 2,
        customGalvanizedBushing: 1,
        meterCorrugatedPipe: 1,
        gasMeterBracket: 1,
        antiTheftGasLock: 1,
        galvanizedPlug: 1,
        protectionPlateStraight: 1,
        protectionPlateBend: 1,
        decorativeCover: 1,
        actualMaterialCost: 245.80
      },
      {
        serialNo: 2,
        customerName: '李四',
        customerCode: 'YH002',
        customerAddress: '绿城花园B区202室',
        dispatchTime: '2024-01-17',
        installTime: '2024-01-18',
        corrugatedQuickConnector: 2,
        galvanized90Elbow: 3,
        galvanizedHexMaleThread: 2,
        galvanizedFemaleThread: 2,
        galvanizedMaleFemaleElbow: 1,
        galvanizedTee: 2,
        stainlessSteelClamp: 3,
        customGalvanizedBushing: 1,
        meterCorrugatedPipe: 1,
        gasMeterBracket: 1,
        antiTheftGasLock: 1,
        galvanizedPlug: 1,
        protectionPlateStraight: 1,
        protectionPlateBend: 1,
        decorativeCover: 1,
        actualMaterialCost: 325.60
      },
      {
        serialNo: 3,
        customerName: '王五',
        customerCode: 'YH003',
        customerAddress: '阳光小区C区303室',
        dispatchTime: '2024-01-19',
        installTime: '2024-01-20',
        corrugatedQuickConnector: 1,
        galvanized90Elbow: 2,
        galvanizedHexMaleThread: 1,
        galvanizedFemaleThread: 1,
        galvanizedMaleFemaleElbow: 1,
        galvanizedTee: 1,
        stainlessSteelClamp: 2,
        customGalvanizedBushing: 1,
        meterCorrugatedPipe: 1,
        gasMeterBracket: 1,
        antiTheftGasLock: 1,
        galvanizedPlug: 1,
        protectionPlateStraight: 1,
        protectionPlateBend: 1,
        decorativeCover: 1,
        actualMaterialCost: 268.40
      }
    ]
    
    // 模拟零星管件数据
    looseFittingsData.value = [
      {
        serialNo: 1,
        customerName: '张三',
        customerCode: 'YH001',
        fullAddress: '阳光小区A栋101室',
        communityName: '阳光小区',
        buildingNo: 'A栋',
        roomNo: '101室',
        dispatchDate: '2024-01-15',
        constructionDate: '2024-01-16',
        corrugatedQuickConnector: 2,
        galvanized90Elbow: 3,
        galvanizedHexMaleThread: 1,
        galvanizedFemaleThread: 2,
        galvanizedMaleFemaleElbow: 1,
        galvanizedTee: 1,
        stainlessSteelClamp: 2,
        customGalvanizedBushing: 1,
        meterCorrugatedPipe: 1,
        gasMeterBracket: 1,
        antiTheftGasLock: 1,
        galvanizedPlug: 1,
        protectionPlateStraight: 1,
        protectionPlateBend: 1,
        decorativeCover: 1,
        actualMaterialCost: 245.60
      },
      {
        serialNo: 2,
        customerName: '李四',
        customerCode: 'YH002',
        fullAddress: '绿城花园B区202室',
        communityName: '绿城花园',
        buildingNo: 'B区',
        roomNo: '202室',
        dispatchDate: '2024-01-17',
        constructionDate: '2024-01-18',
        corrugatedQuickConnector: 1,
        galvanized90Elbow: 2,
        galvanizedHexMaleThread: 1,
        galvanizedFemaleThread: 1,
        galvanizedMaleFemaleElbow: 1,
        galvanizedTee: 1,
        stainlessSteelClamp: 1,
        customGalvanizedBushing: 1,
        meterCorrugatedPipe: 1,
        gasMeterBracket: 1,
        antiTheftGasLock: 1,
        galvanizedPlug: 1,
        protectionPlateStraight: 1,
        protectionPlateBend: 1,
        decorativeCover: 1,
        actualMaterialCost: 198.40
      },
      {
        serialNo: 3,
        customerName: '王五',
        customerCode: 'YH003',
        fullAddress: '阳光小区C区303室',
        communityName: '阳光小区',
        buildingNo: 'C区',
        roomNo: '303室',
        dispatchDate: '2024-01-19',
        constructionDate: '2024-01-20',
        corrugatedQuickConnector: 1,
        galvanized90Elbow: 2,
        galvanizedHexMaleThread: 1,
        galvanizedFemaleThread: 1,
        galvanizedMaleFemaleElbow: 1,
        galvanizedTee: 1,
        stainlessSteelClamp: 1,
        customGalvanizedBushing: 1,
        meterCorrugatedPipe: 1,
        gasMeterBracket: 1,
        antiTheftGasLock: 1,
        galvanizedPlug: 1,
        protectionPlateStraight: 1,
        protectionPlateBend: 1,
        decorativeCover: 1,
        actualMaterialCost: 176.80
      }
    ]
    
    // 模拟二次安装管件数据
    secondaryFittingsData.value = [
      {
        serialNo: 1,
        customerName: '赵六',
        customerCode: 'YH004',
        communityName: '绿城花园',
        buildingNo: 'C区',
        roomNo: '303室',
        dispatchDate: '2024-01-25',
        constructionDate: '2024-01-26',
        customGalvanizedBushing: 1,
        meterCorrugatedPipe: 2,
        gasMeterBracket: 1,
        antiTheftGasLock: 1,
        actualMaterialCost: 125.50
      },
      {
        serialNo: 2,
        customerName: '周八',
        customerCode: 'YH006',
        communityName: '绿城花园',
        buildingNo: 'D区',
        roomNo: '401室',
        dispatchDate: '2024-01-27',
        constructionDate: '2024-01-28',
        customGalvanizedBushing: 1,
        meterCorrugatedPipe: 2,
        gasMeterBracket: 1,
        antiTheftGasLock: 1,
        actualMaterialCost: 145.80
      },
      {
        serialNo: 3,
        customerName: '吴九',
        customerCode: 'YH007',
        communityName: '阳光小区',
        buildingNo: 'D区',
        roomNo: '502室',
        dispatchDate: '2024-01-29',
        constructionDate: '2024-01-30',
        customGalvanizedBushing: 1,
        meterCorrugatedPipe: 1,
        gasMeterBracket: 1,
        antiTheftGasLock: 1,
        actualMaterialCost: 98.30
      }
    ]
    
    // 模拟管件统计表数据
    fittingsStatisticsData.value = [
      {
        serialNo: 1,
        projectName: '阳光小区A栋燃气安装工程',
        corrugatedConnector: 80,
        galvanized90Elbow: 120,
        galvanizedHexMaleThread: 60,
        galvanizedFemaleThread: 90,
        galvanizedMaleFemaleElbow: 40,
        galvanizedTee: 30,
        stainlessSteelClamp: 25,
        customGalvanizedBushing: 20,
        corrugatedShortPipe: 35,
        meterCorrugatedPipe: 35,
        gasMeterBracket: 15,
        antiTheftGasLock: 10,
        galvanizedPlug: 8,
        protectionPlateStraight: 12,
        protectionPlateBend: 8,
        decorativeCover: 6,
        actualMaterialCost: 2845.50
      },
      {
        serialNo: 2,
        projectName: '绿城花园B区燃气安装工程',
        corrugatedConnector: 100,
        galvanized90Elbow: 150,
        galvanizedHexMaleThread: 75,
        galvanizedFemaleThread: 110,
        galvanizedMaleFemaleElbow: 50,
        galvanizedTee: 40,
        stainlessSteelClamp: 30,
        customGalvanizedBushing: 25,
        corrugatedShortPipe: 45,
        meterCorrugatedPipe: 45,
        gasMeterBracket: 20,
        antiTheftGasLock: 15,
        galvanizedPlug: 12,
        protectionPlateStraight: 18,
        protectionPlateBend: 12,
        decorativeCover: 8,
        actualMaterialCost: 3568.75
      },
      {
        serialNo: 3,
        projectName: '阳光小区C区燃气安装工程',
        corrugatedConnector: 60,
        galvanized90Elbow: 90,
        galvanizedHexMaleThread: 45,
        galvanizedFemaleThread: 70,
        galvanizedMaleFemaleElbow: 30,
        galvanizedTee: 25,
        stainlessSteelClamp: 20,
        customGalvanizedBushing: 15,
        corrugatedShortPipe: 25,
        meterCorrugatedPipe: 25,
        gasMeterBracket: 10,
        antiTheftGasLock: 8,
        galvanizedPlug: 6,
        protectionPlateStraight: 10,
        protectionPlateBend: 6,
        decorativeCover: 4,
        actualMaterialCost: 2156.30
      }
    ]
    
    // 模拟明装及暗埋(半月板)数据
    exposedBuriedHalfMoonData.value = [
      {
        serialNo: 1,
        customerName: '张三',
        customerCode: 'YH001',
        communityName: '阳光小区',
        buildingNo: 'A栋',
        roomNo: '101室',
        dispatchTime: '2024-01-15',
        installTime: '2024-01-16',
        hasMeterBox: '有',
        preMeterValve: '有',
        preMeterPrice: 800.00,
        indoorPrice: 1200.00,
        stoveFrontValveQty: 2,
        preMeterSteelPipe: 3.5,
        preMeterCoatedSteelPipe: 2.2,
        preMeterCorrugatedPipe: 1.8,
        indoorSteelPipe: 4.0,
        indoorCoatedSteelPipe: 2.5,
        indoorCorrugatedPipe: 2.0,
        mechanicalMeter: 1,
        lowPressureRegulator: 1,
        corrugatedConnectLong: 2,
        corrugatedConnectShort: 1,
        prefabShort12: 3,
        prefabShort18: 2,
        preMeterInstallCost: 850.00,
        indoorInstallCost: 1250.00
      },
      {
        serialNo: 2,
        customerName: '李四',
        customerCode: 'YH002',
        communityName: '绿城花园',
        buildingNo: 'B区',
        roomNo: '202室',
        dispatchTime: '2024-01-17',
        installTime: '2024-01-18',
        hasMeterBox: '无',
        preMeterValve: '有',
        preMeterPrice: 900.00,
        indoorPrice: 1300.00,
        stoveFrontValveQty: 1,
        preMeterSteelPipe: 4.0,
        preMeterCoatedSteelPipe: 2.8,
        preMeterCorrugatedPipe: 2.2,
        indoorSteelPipe: 4.5,
        indoorCoatedSteelPipe: 3.0,
        indoorCorrugatedPipe: 2.5,
        mechanicalMeter: 1,
        lowPressureRegulator: 1,
        corrugatedConnectLong: 2,
        corrugatedConnectShort: 2,
        prefabShort12: 4,
        prefabShort18: 3,
        preMeterInstallCost: 950.00,
        indoorInstallCost: 1350.00
      },
      {
        serialNo: 3,
        customerName: '王五',
        customerCode: 'YH003',
        communityName: '阳光小区',
        buildingNo: 'C区',
        roomNo: '303室',
        dispatchTime: '2024-01-19',
        installTime: '2024-01-20',
        hasMeterBox: '有',
        preMeterValve: '有',
        preMeterPrice: 750.00,
        indoorPrice: 1100.00,
        stoveFrontValveQty: 2,
        preMeterSteelPipe: 3.0,
        preMeterCoatedSteelPipe: 2.0,
        preMeterCorrugatedPipe: 1.5,
        indoorSteelPipe: 3.5,
        indoorCoatedSteelPipe: 2.2,
        indoorCorrugatedPipe: 1.8,
        mechanicalMeter: 1,
        lowPressureRegulator: 1,
        corrugatedConnectLong: 1,
        corrugatedConnectShort: 1,
        prefabShort12: 2,
        prefabShort18: 2,
        preMeterInstallCost: 800.00,
        indoorInstallCost: 1150.00
      }
    ]
    
    // 模拟明装及暗埋（未用半月板保护）数据
    exposedBuriedNoProtectionData.value = [
      {
        serialNo: 1,
        communityName: '阳光小区',
        buildingNo: 'A栋',
        roomNo: '101室',
        dispatchTime: '2024-01-15',
        installTime: '2024-01-16',
        hasMeterBox: '有',
        preMeterValve: '有',
        preMeterPrice: 800.00,
        indoorPrice: 1200.00,
        stoveFrontValveQty: 2,
        preMeterSteelPipe: 3.5,
        preMeterCoatedSteelPipe: 2.2,
        preMeterCorrugatedPipe: 1.8,
        indoorSteelPipe: 4.0,
        indoorCoatedSteelPipe: 2.5,
        indoorCorrugatedPipe: 2.0,
        mechanicalMeter: 1,
        lowPressureRegulator: 1,
        corrugatedConnectLong: 2,
        corrugatedConnectShort: 1,
        prefabShort12: 3,
        prefabShort18: 2,
        preMeterInstallCost: 850.00,
        indoorInstallCost: 1250.00
      },
      {
        serialNo: 2,
        communityName: '绿城花园',
        buildingNo: 'B区',
        roomNo: '202室',
        dispatchTime: '2024-01-17',
        installTime: '2024-01-18',
        hasMeterBox: '无',
        preMeterValve: '有',
        preMeterPrice: 900.00,
        indoorPrice: 1300.00,
        stoveFrontValveQty: 1,
        preMeterSteelPipe: 4.0,
        preMeterCoatedSteelPipe: 2.8,
        preMeterCorrugatedPipe: 2.2,
        indoorSteelPipe: 4.5,
        indoorCoatedSteelPipe: 3.0,
        indoorCorrugatedPipe: 2.5,
        mechanicalMeter: 1,
        lowPressureRegulator: 1,
        corrugatedConnectLong: 2,
        corrugatedConnectShort: 2,
        prefabShort12: 4,
        prefabShort18: 3,
        preMeterInstallCost: 950.00,
        indoorInstallCost: 1350.00
      },
      {
        serialNo: 3,
        communityName: '阳光小区',
        buildingNo: 'C区',
        roomNo: '303室',
        dispatchTime: '2024-01-19',
        installTime: '2024-01-20',
        hasMeterBox: '有',
        preMeterValve: '有',
        preMeterPrice: 750.00,
        indoorPrice: 1100.00,
        stoveFrontValveQty: 2,
        preMeterSteelPipe: 3.0,
        preMeterCoatedSteelPipe: 2.0,
        preMeterCorrugatedPipe: 1.5,
        indoorSteelPipe: 3.5,
        indoorCoatedSteelPipe: 2.2,
        indoorCorrugatedPipe: 1.8,
        mechanicalMeter: 1,
        lowPressureRegulator: 1,
        corrugatedConnectLong: 1,
        corrugatedConnectShort: 1,
        prefabShort12: 2,
        prefabShort18: 2,
        preMeterInstallCost: 800.00,
        indoorInstallCost: 1150.00
      }
    ]
    
    // 模拟零星安装（半月板）数据
    looseInstallationHalfMoonData.value = [
      {
        customerName: '张三',
        customerCode: 'YH001',
        communityName: '阳光小区',
        buildingNo: 'A栋',
        roomNo: '101室',
        dispatchDate: '2024-01-15',
        constructionDate: '2024-01-16',
        firePointQty: 2,
        stoveFrontValveQty: 1,
        gasMeterBox: 1,
        preMeterValve: 1,
        lowPressureRegulator: 1,
        gasMeter: 1,
        packagePrice: 1200.00,
        dn15SteelPipe: 3.5,
        dn15CoatedSteelPipe: 2.2,
        corrugatedPipe: 1.8,
        corrugatedConnectStove: 2,
        corrugatedConnectHeat: 1,
        prefabShort12: 3,
        prefabShort18: 2,
        singleHouseholdCost: 1350.00
      },
      {
        customerName: '李四',
        customerCode: 'YH002',
        communityName: '绿城花园',
        buildingNo: 'B区',
        roomNo: '202室',
        dispatchDate: '2024-01-17',
        constructionDate: '2024-01-18',
        firePointQty: 1,
        stoveFrontValveQty: 1,
        gasMeterBox: 1,
        preMeterValve: 1,
        lowPressureRegulator: 1,
        gasMeter: 1,
        packagePrice: 1100.00,
        dn15SteelPipe: 3.0,
        dn15CoatedSteelPipe: 2.0,
        corrugatedPipe: 1.5,
        corrugatedConnectStove: 1,
        corrugatedConnectHeat: 1,
        prefabShort12: 2,
        prefabShort18: 2,
        singleHouseholdCost: 1250.00
      },
      {
        customerName: '王五',
        customerCode: 'YH003',
        communityName: '阳光小区',
        buildingNo: 'C区',
        roomNo: '303室',
        dispatchDate: '2024-01-19',
        constructionDate: '2024-01-20',
        firePointQty: 2,
        stoveFrontValveQty: 2,
        gasMeterBox: 1,
        preMeterValve: 1,
        lowPressureRegulator: 1,
        gasMeter: 1,
        packagePrice: 1300.00,
        dn15SteelPipe: 4.0,
        dn15CoatedSteelPipe: 2.5,
        corrugatedPipe: 2.0,
        corrugatedConnectStove: 2,
        corrugatedConnectHeat: 1,
        prefabShort12: 4,
        prefabShort18: 3,
        singleHouseholdCost: 1450.00
      }
    ]
    
    // 模拟零星安装（未用半月板保护）数据
    looseInstallationNoProtectionData.value = [
      {
        serialNo: 1,
        customerName: '张三',
        customerCode: 'YH001',
        communityName: '阳光小区',
        buildingNo: 'A栋',
        roomNo: '101室',
        dispatchDate: '2024-01-15',
        constructionDate: '2024-01-16',
        firePointQty: 2,
        stoveFrontValveQty: 1,
        gasMeterBox: 1,
        preMeterValve: 1,
        lowPressureRegulator: 1,
        gasMeter: 1,
        packagePrice: 1200.00,
        dn15SteelPipe: 3.5,
        dn15CoatedSteelPipe: 2.2,
        corrugatedPipe: 1.8,
        corrugatedConnectStove: 2,
        corrugatedConnectHeat: 1,
        prefabShort12: 3,
        prefabShort18: 2,
        singleHouseholdCost: 1350.00
      },
      {
        serialNo: 2,
        customerName: '李四',
        customerCode: 'YH002',
        communityName: '绿城花园',
        buildingNo: 'B区',
        roomNo: '202室',
        dispatchDate: '2024-01-17',
        constructionDate: '2024-01-18',
        firePointQty: 1,
        stoveFrontValveQty: 1,
        gasMeterBox: 1,
        preMeterValve: 1,
        lowPressureRegulator: 1,
        gasMeter: 1,
        packagePrice: 1100.00,
        dn15SteelPipe: 3.0,
        dn15CoatedSteelPipe: 2.0,
        corrugatedPipe: 1.5,
        corrugatedConnectStove: 1,
        corrugatedConnectHeat: 1,
        prefabShort12: 2,
        prefabShort18: 2,
        singleHouseholdCost: 1250.00
      },
      {
        serialNo: 3,
        customerName: '王五',
        customerCode: 'YH003',
        communityName: '阳光小区',
        buildingNo: 'C区',
        roomNo: '303室',
        dispatchDate: '2024-01-19',
        constructionDate: '2024-01-20',
        firePointQty: 2,
        stoveFrontValveQty: 2,
        gasMeterBox: 1,
        preMeterValve: 1,
        lowPressureRegulator: 1,
        gasMeter: 1,
        packagePrice: 1300.00,
        dn15SteelPipe: 4.0,
        dn15CoatedSteelPipe: 2.5,
        corrugatedPipe: 2.0,
        corrugatedConnectStove: 2,
        corrugatedConnectHeat: 1,
        prefabShort12: 4,
        prefabShort18: 3,
        singleHouseholdCost: 1450.00
      }
    ]
    
    // 模拟不可用燃气表换表数据
    unusableMeterChangeData.value = [
      {
        orderNo: 'WO2024021',
        customerName: '林二三',
        address: '清远市XX区XX小区21栋2121',
        oldMeterNumber: 'M2023001',
        newMeterNumber: 'M2024001',
        changeReason: '表具损坏',
        oldMeterValue: 1234.5,
        newMeterValue: 0.0,
        changeDate: '2024-02-02',
        workerName: '罗二四',
        amount: 500.00
      },
      {
        orderNo: '*********',
        customerName: '罗二四',
        address: '清远市XX区XX小区22栋2222',
        oldMeterNumber: '********',
        newMeterNumber: '********',
        changeReason: '表具老化',
        oldMeterValue: 2345.6,
        newMeterValue: 0.0,
        changeDate: '2024-02-03',
        workerName: '梁二五',
        amount: 500.00
      }
    ]
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    (searchForm as any)[key] = ''
  })
  pagination.page = 1
  fetchData()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  fetchData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchData()
}

// 标签页点击
const handleTabClick = () => {
  // 标签页切换时的处理逻辑
}

// 查看详情
const handleView = (row: any) => {
  ElMessage.info(`查看工单结算详情: ${row.id}`)
}

// 查看工单明细
const handleViewDetail = (row: any) => {
  ElMessage.info(`查看工单明细: ${row.orderNo}`)
}





// 应付施工单位金额合计方法
const getPayableAmountSummary = (param: any) => {
  const { columns, data } = param
  const sums: any = []
  
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    
    // 跳过文本列
    if (column.property === 'projectName') {
      sums[index] = ''
      return
    }
    
    // 处理数值列
    const values = data.map((item: any) => Number(item[column.property]))
    if (!values.every((value: number) => isNaN(value))) {
      const sum = values.reduce((prev: number, curr: number) => {
        const value = Number(curr)
        if (!isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
      
      // 检查是否为金额字段
      if (column.property.includes('Cost') || column.property.includes('Amount') || column.property.includes('Subtotal')) {
        sums[index] = `¥${formatNumber(sum)}`
      } else {
        sums[index] = formatNumber(sum)
      }
    } else {
      sums[index] = ''
    }
  })
  
  return sums
}

// 表前数据合计方法
const getPreMeterSummary = (param: any) => {
  const { columns, data } = param
  const sums: any = []
  
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    
    // 跳过文本列
    if (column.property === 'projectName') {
      sums[index] = ''
      return
    }
    
    // 处理数值列
    const values = data.map((item: any) => Number(item[column.property]))
    if (!values.every((value: number) => isNaN(value))) {
      const sum = values.reduce((prev: number, curr: number) => {
        const value = Number(curr)
        if (!isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
      
      // 检查是否为金额字段
      if (column.property.includes('Price') || column.property.includes('Amount')) {
        sums[index] = `¥${formatNumber(sum)}`
      } else {
        sums[index] = formatNumber(sum)
      }
    } else {
      sums[index] = ''
    }
  })
  
  return sums
}

// 户内数据合计方法
const getIndoorSummary = (param: any) => {
  const { columns, data } = param
  const sums: any = []
  
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    
    // 跳过文本列
    if (column.property === 'projectName') {
      sums[index] = ''
      return
    }
    
    // 处理数值列
    const values = data.map((item: any) => Number(item[column.property]))
    if (!values.every((value: number) => isNaN(value))) {
      const sum = values.reduce((prev: number, curr: number) => {
        const value = Number(curr)
        if (!isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
      
      // 检查是否为金额字段
      if (column.property.includes('Price') || column.property.includes('Amount')) {
        sums[index] = `¥${formatNumber(sum)}`
      } else {
        sums[index] = formatNumber(sum)
      }
    } else {
      sums[index] = ''
    }
  })
  
  return sums
}

// 超领材料数据合计方法
const getOverReceivedSummary = (param: any) => {
  const { columns, data } = param
  const sums: any = []
  
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    
    // 跳过文本列
    if (column.property === 'materialName' || column.property === 'specification' || column.property === 'unit') {
      sums[index] = ''
      return
    }
    
    // 处理数值列
    const values = data.map((item: any) => Number(item[column.property]))
    if (!values.every((value: number) => isNaN(value))) {
      const sum = values.reduce((prev: number, curr: number) => {
        const value = Number(curr)
        if (!isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
      
      // 检查是否为金额字段
      if (column.property.includes('Price')) {
        sums[index] = `¥${formatNumber(sum)}`
      } else {
        sums[index] = formatNumber(sum)
      }
    } else {
      sums[index] = ''
    }
  })
  
  return sums
}

// 管件（总）数据合计方法
const getFittingsTotalSummary = (param: any) => {
  const { columns, data } = param
  const sums: any = []
  
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    
    // 跳过文本列
    if (column.property === 'projectName') {
      sums[index] = ''
      return
    }
    
    // 处理数值列
    const values = data.map((item: any) => Number(item[column.property]))
    if (!values.every((value: number) => isNaN(value))) {
      const sum = values.reduce((prev: number, curr: number) => {
        const value = Number(curr)
        if (!isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
      
      // 检查是否为金额字段
      if (column.property.includes('Price') || column.property.includes('Cost')) {
        sums[index] = `¥${formatNumber(sum)}`
      } else {
        sums[index] = formatNumber(sum)
      }
    } else {
      sums[index] = ''
    }
  })
  
  return sums
}

// 挂表管件数据合计方法
const getMeterFittingsSummary = (param: any) => {
  const { columns, data } = param
  const sums: any = []
  
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    
    // 跳过文本列
    if (column.property === 'customerName' || column.property === 'customerCode' || column.property === 'customerAddress' || 
        column.property === 'dispatchTime' || column.property === 'installTime') {
      sums[index] = ''
      return
    }
    
    // 处理数值列
    const values = data.map((item: any) => Number(item[column.property]))
    if (!values.every((value: number) => isNaN(value))) {
      const sum = values.reduce((prev: number, curr: number) => {
        const value = Number(curr)
        if (!isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
      
      // 检查是否为金额字段
      if (column.property.includes('Cost')) {
        sums[index] = `¥${formatNumber(sum)}`
      } else {
        sums[index] = formatNumber(sum)
      }
    } else {
      sums[index] = ''
    }
  })
  
  return sums
}

// 二次安装管件数据合计方法
const getSecondaryFittingsSummary = (param: any) => {
  const { columns, data } = param
  const sums: any = []
  
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    
    // 跳过文本列
    if (column.property === 'customerName' || column.property === 'customerCode' || column.property === 'communityName' || 
        column.property === 'buildingNo' || column.property === 'roomNo' || column.property === 'dispatchDate' || 
        column.property === 'constructionDate') {
      sums[index] = ''
      return
    }
    
    // 处理数值列
    const values = data.map((item: any) => Number(item[column.property]))
    if (!values.every((value: number) => isNaN(value))) {
      const sum = values.reduce((prev: number, curr: number) => {
        const value = Number(curr)
        if (!isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
      
      // 检查是否为金额字段
      if (column.property.includes('Cost')) {
        sums[index] = `¥${formatNumber(sum)}`
      } else {
        sums[index] = formatNumber(sum)
      }
    } else {
      sums[index] = ''
    }
  })
  
  return sums
}

// 管件统计表数据合计方法
const getFittingsStatisticsSummary = (param: any) => {
  const { columns, data } = param
  const sums: any = []
  
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    
    // 跳过文本列
    if (column.property === 'projectName') {
      sums[index] = ''
      return
    }
    
    // 处理数值列
    const values = data.map((item: any) => Number(item[column.property]))
    if (!values.every((value: number) => isNaN(value))) {
      const sum = values.reduce((prev: number, curr: number) => {
        const value = Number(curr)
        if (!isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
      
      // 检查是否为金额字段
      if (column.property.includes('Cost')) {
        sums[index] = `¥${formatNumber(sum)}`
      } else {
        sums[index] = formatNumber(sum)
      }
    } else {
      sums[index] = ''
    }
  })
  
  return sums
}

// 明装及暗埋(半月板)数据合计方法
const getExposedBuriedHalfMoonSummary = (param: any) => {
  const { columns, data } = param
  const sums: any = []
  
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    
    // 跳过文本列
    if (column.property === 'customerName' || column.property === 'customerCode' || 
        column.property === 'communityName' || column.property === 'buildingNo' || 
        column.property === 'roomNo' || column.property === 'dispatchTime' || 
        column.property === 'installTime' || column.property === 'hasMeterBox' || 
        column.property === 'preMeterValve' || column.property === 'mechanicalMeter') {
      sums[index] = ''
      return
    }
    
    // 处理数值列
    const values = data.map((item: any) => Number(item[column.property]))
    if (!values.every((value: number) => isNaN(value))) {
      const sum = values.reduce((prev: number, curr: number) => {
        const value = Number(curr)
        if (!isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
      
      // 检查是否为金额字段
      if (column.property.includes('Price') || column.property.includes('Cost')) {
        sums[index] = `¥${formatNumber(sum)}`
      } else {
        sums[index] = formatNumber(sum)
      }
    } else {
      sums[index] = ''
    }
  })
  
  return sums
}

// 零星管件数据合计方法
const getLooseFittingsSummary = (param: any) => {
  const { columns, data } = param
  const sums: any = []
  
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    
    // 跳过文本列
    if (column.property === 'customerName' || column.property === 'customerCode' || 
        column.property === 'fullAddress' || column.property === 'communityName' || 
        column.property === 'buildingNo' || column.property === 'roomNo' || 
        column.property === 'dispatchDate' || column.property === 'constructionDate') {
      sums[index] = ''
      return
    }
    
    // 处理数值列
    const values = data.map((item: any) => Number(item[column.property]))
    if (!values.every((value: number) => isNaN(value))) {
      const sum = values.reduce((prev: number, curr: number) => {
        const value = Number(curr)
        if (!isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
      
      // 检查是否为金额字段
      if (column.property.includes('Cost')) {
        sums[index] = `¥${formatNumber(sum)}`
      } else {
        sums[index] = formatNumber(sum)
      }
    } else {
      sums[index] = ''
    }
  })
  
  return sums
}

// 明装及暗埋（未用半月板保护）数据合计方法
const getExposedBuriedNoProtectionSummary = (param: any) => {
  const { columns, data } = param
  const sums: any = []
  
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    
    // 跳过文本列
    if (column.property === 'communityName' || column.property === 'buildingNo' || 
        column.property === 'roomNo' || column.property === 'dispatchTime' || 
        column.property === 'installTime' || column.property === 'hasMeterBox' || 
        column.property === 'preMeterValve' || column.property === 'mechanicalMeter') {
      sums[index] = ''
      return
    }
    
    // 处理数值列
    const values = data.map((item: any) => Number(item[column.property]))
    if (!values.every((value: number) => isNaN(value))) {
      const sum = values.reduce((prev: number, curr: number) => {
        const value = Number(curr)
        if (!isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
      
      // 检查是否为金额字段
      if (column.property.includes('Price') || column.property.includes('Cost')) {
        sums[index] = `¥${formatNumber(sum)}`
      } else {
        sums[index] = formatNumber(sum)
      }
    } else {
      sums[index] = ''
    }
  })
  
  return sums
}

// 零星安装（半月板）数据合计方法
const getLooseInstallationHalfMoonSummary = (param: any) => {
  const { columns, data } = param
  const sums: any = []
  
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    
    // 跳过文本列
    if (column.property === 'customerName' || column.property === 'customerCode' || 
        column.property === 'communityName' || column.property === 'buildingNo' || 
        column.property === 'roomNo' || column.property === 'dispatchDate' || 
        column.property === 'constructionDate') {
      sums[index] = ''
      return
    }
    
    // 处理数值列
    const values = data.map((item: any) => Number(item[column.property]))
    if (!values.every((value: number) => isNaN(value))) {
      const sum = values.reduce((prev: number, curr: number) => {
        const value = Number(curr)
        if (!isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
      
      // 检查是否为金额字段
      if (column.property.includes('Price') || column.property.includes('Cost')) {
        sums[index] = `¥${formatNumber(sum)}`
      } else {
        sums[index] = formatNumber(sum)
      }
    } else {
      sums[index] = ''
    }
  })
  
  return sums
}

// 零星安装（未用半月板保护）数据合计方法
const getLooseInstallationNoProtectionSummary = (param: any) => {
  const { columns, data } = param
  const sums: any = []
  
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    
    // 跳过文本列
    if (column.property === 'customerName' || column.property === 'customerCode' || 
        column.property === 'communityName' || column.property === 'buildingNo' || 
        column.property === 'roomNo' || column.property === 'dispatchDate' || 
        column.property === 'constructionDate') {
      sums[index] = ''
      return
    }
    
    // 处理数值列
    const values = data.map((item: any) => Number(item[column.property]))
    if (!values.every((value: number) => isNaN(value))) {
      const sum = values.reduce((prev: number, curr: number) => {
        const value = Number(curr)
        if (!isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
      
      // 检查是否为金额字段
      if (column.property.includes('Price') || column.property.includes('Cost')) {
        sums[index] = `¥${formatNumber(sum)}`
      } else {
        sums[index] = formatNumber(sum)
      }
    } else {
      sums[index] = ''
    }
  })
  
  return sums
}

// 确认结算
const handleConfirm = (row: any) => {
  ElMessageBox.confirm('确定要确认这个结算记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('确认成功')
    fetchData()
  })
}

// 打印
const handlePrint = (row: any) => {
  ElMessage.success('开始打印')
}

// 导出Excel
const handleExportExcel = () => {
  ElMessage.success('导出成功')
}

// 统计图表
const handleStatistics = () => {
  ElMessage.info('统计图表功能开发中')
}

// 格式化日期 - 显示北京时间
const formatDate = (date: string) => {
  if (!date) return ''
  
  // 创建日期对象并添加8小时转换为北京时间
  const dateObj = new Date(date)
  dateObj.setHours(dateObj.getHours() + 8)
  
  return dateObj.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

// 格式化数字
const formatNumber = (num: number | undefined | null) => {
  if (num === undefined || num === null) return '0'
  return num.toLocaleString()
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'confirmed': '已确认',
    'pending': '待确认'
  }
  return statusMap[status] || '未知'
}

// 获取状态类型
const getStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    'confirmed': 'success',
    'pending': 'warning'
  }
  return typeMap[status] || 'info'
}

// 在现有方法后添加缺失的方法
// 获取不可用燃气表换表汇总
const getUnusableMeterChangeSummary = (param: any) => {
  const { columns, data } = param
  const sums: string[] = []
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    const values = data.map((item: any) => Number(item[column.property]))
    if (!values.every((value: number) => isNaN(value))) {
      sums[index] = values.reduce((prev: number, curr: number) => {
        const value = Number(curr)
        if (!isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0).toLocaleString()
    } else {
      sums[index] = ''
    }
  })
  return sums
}

// 导出结算书
const handleExportSettlement = () => {
  ElMessage.success('导出结算书成功')
}

// 确定平帐归档
const handleConfirmArchive = async () => {
  try {
    await ElMessageBox.confirm('确定要平帐归档吗？此操作不可撤销。', '确认平帐归档', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('平帐归档成功')
  } catch {
    // 用户取消操作
  }
}

// 导入甲方材料价格表Excel
const handleImportMaterialPriceExcel = () => {
  ElMessage.info('导入甲方材料价格表Excel功能开发中')
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.order-settlement {
  padding-bottom: 60px;
}

.settlement-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 0;
  box-shadow: none;
}

.month-selector {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.summary-line {
  display: flex;
  align-items: center;
  gap: 18px;
  margin: 18px 0 18px 20px;
  font-size: 16px;
}

.summary-badge {
  display: inline-block;
  padding: 4px 16px;
  border-radius: 16px;
  font-weight: bold;
  color: #fff;
  font-size: 15px;
  letter-spacing: 1px;
}

.badge-blue {
  background: #409eff;
}

.badge-green {
  background: #67c23a;
}

.badge-orange {
  background: #e6a23c;
}

.badge-purple {
  background: #a279e6;
}

.module-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tab-content {
  padding: 20px 0;
}

.pagination-section {
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
  background: #fff;
  z-index: 10;
  position: relative;
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-form {
  margin-top: 10px;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

/* 封面样式 */
.settlement-cover {
  max-width: 900px;
  margin: 0 auto;
  background: #fff;
  padding: 32px 24px 24px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.06);
}

.cover-header {
  text-align: center;
  margin-bottom: 40px;
}

.document-title {
  text-align: center;
  font-size: 26px;
  font-weight: bold;
  letter-spacing: 12px;
  margin: 20px 0;
  text-decoration: underline;
  text-underline-offset: 4px;
}

.cover-content {
  display: flex;
  gap: 40px;
}

.left-column {
  flex: 1;
}

.right-column {
  flex: 1;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  font-size: 16px;
}

.form-row .label {
  width: 120px;
  font-weight: bold;
  color: #303133;
}

.form-row .value {
  flex: 1;
  color: #606266;
}

.form-row .currency-symbol {
  margin-left: 10px;
  font-weight: bold;
  color: #409eff;
}

/* 公司名称输入框样式 */
.company-name-input {
  font-size: 18px;
  font-weight: normal;
  text-align: center;
  margin: 0 0 10px 0;
  width: 100%;
}

/* 空内容样式 */
.empty-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}
</style> 