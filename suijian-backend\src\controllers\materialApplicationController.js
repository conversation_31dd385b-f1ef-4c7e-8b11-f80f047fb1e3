const { query, execute } = require('../config/database');
const Response = require('../utils/response');
const { generatePrefixedId } = require('../utils/uuid');

/**
 * 创建领料申请
 * POST /api/material-applications
 */
const createMaterialApplication = async(req, res) => {
    try {
        const {
            applyNo,
            applyDate,
            applicantId,
            purpose,
            orderNo,
            remarks,
            materials
        } = req.body;

        // 验证必填字段
        console.log('接收到的数据:', { applyNo, applyDate, applicantId, purpose, materials });

        if (!applyNo || !applyDate || !applicantId || !purpose) {
            const missingFields = [];
            if (!applyNo) missingFields.push('申请单号');
            if (!applyDate) missingFields.push('申请日期');
            if (!applicantId) missingFields.push('申请人');
            if (!purpose) missingFields.push('申领用途');

            return Response.error(res, `请填写完整的申请信息：${missingFields.join('、')}`, 400);
        }

        if (!Array.isArray(materials) || materials.length === 0) {
            return Response.error(res, '请至少添加一个物料', 400);
        }

        // 检查申请单号是否已存在
        const existingSql = 'SELECT id FROM material_records WHERE order_id = ? AND type = "apply"';
        const existing = await query(existingSql, [applyNo]);
        if (existing.length > 0) {
            return Response.error(res, '申请单号已存在', 400);
        }

        // 验证物料是否存在且有足够库存
        const materialIds = materials.map(m => m.materialId);
        const placeholders = materialIds.map(() => '?').join(',');
        const materialSql = `SELECT id, name, stock_quantity FROM materials WHERE id IN (${placeholders}) AND status = 1`;
        const materialList = await query(materialSql, materialIds);

        if (materialList.length !== materials.length) {
            return Response.error(res, '部分物料不存在或已禁用', 400);
        }

        // 检查库存是否足够
        const insufficientMaterials = materials.filter(item => {
            const material = materialList.find(m => m.id === item.materialId);
            return material && item.quantity > material.stock_quantity;
        });

        if (insufficientMaterials.length > 0) {
            return Response.error(res, '部分物料库存不足', 400);
        }

        // 生成申请ID
        const applicationId = generatePrefixedId('ma');

        // 开始事务
        await execute('BEGIN TRANSACTION');

        try {
            // 直接保存到物料记录表（申请记录）
            const recordId = generatePrefixedId('mr');
            await execute(
                `INSERT INTO material_records (id, type, operator_id, order_id, recipient, purpose, remarks, created_at) VALUES (?, 'apply', ?, ?, ?, ?, ?, datetime('now'))`, [recordId, req.user.id, applyNo, applicantId, purpose, remarks || '']
            );

            // 保存物料记录明细
            for (const material of materials) {
                // 获取当前库存数量
                const currentStockSql = 'SELECT stock_quantity FROM materials WHERE id = ?';
                const currentStockResult = await query(currentStockSql, [material.materialId]);
                const currentQuantity = currentStockResult[0] && currentStockResult[0].stock_quantity ? currentStockResult[0].stock_quantity : 0;

                await execute(
                    `INSERT INTO material_record_items (id, record_id, material_id, quantity, current_quantity, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))`, [generatePrefixedId('mri'), recordId, material.materialId, material.quantity, currentQuantity]
                );
            }

            // 提交事务
            await execute('COMMIT');

            Response.success(res, { id: applicationId }, '领料申请创建成功');
        } catch (error) {
            // 回滚事务
            await execute('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('创建领料申请失败:', error);
        Response.serverError(res, '创建领料申请失败');
    }
};

/**
 * 获取领料申请列表
 * GET /api/material-applications
 */
const getMaterialApplications = async(req, res) => {
    try {
        const {
            page = 1,
                pageSize = 20,
                applyNo = '',
                applicantId = '',
                purpose = '',
                startDate = '',
                endDate = ''
        } = req.query;

        let where = 'WHERE mr.type = "apply"';
        const params = [];

        if (applyNo) {
            where += ' AND mr.order_id LIKE ?';
            params.push(`%${applyNo}%`);
        }

        if (applicantId) {
            where += ' AND mr.recipient = ?';
            params.push(applicantId);
        }

        if (purpose) {
            where += ' AND mr.purpose = ?';
            params.push(purpose);
        }

        if (startDate) {
            where += ' AND mr.created_at >= ?';
            params.push(startDate);
        }

        if (endDate) {
            where += ' AND mr.created_at <= ?';
            params.push(endDate);
        }

        const offset = (page - 1) * pageSize;

        // 获取总数
        const countSql = `
            SELECT COUNT(*) as total 
            FROM material_records mr 
            ${where}
        `;
        const countResult = await query(countSql, params);
        const total = countResult[0] && countResult[0].total ? countResult[0].total : 0;

        // 获取列表
        const listSql = `
            SELECT 
                mr.id,
                mr.order_id as apply_no,
                mr.created_at as apply_date,
                mr.operator_id,
                mr.recipient as applicant_id,
                mr.purpose,
                mr.remarks,
                mr.created_at,
                e.name as applicant_name,
                u.real_name as operator_name
            FROM material_records mr
            LEFT JOIN employees e ON mr.recipient = e.id
            LEFT JOIN users u ON mr.operator_id = u.id
            ${where}
            ORDER BY mr.created_at DESC
            LIMIT ? OFFSET ?
        `;

        const listParams = [...params, parseInt(pageSize), offset];
        const list = await query(listSql, listParams);

        // 获取每个申请的物料明细
        for (const application of list) {
            const itemsSql = `
                SELECT 
                    mri.material_id,
                    mri.quantity,
                    mri.current_quantity,
                    m.name as material_name,
                    m.company_code,
                    m.client_code,
                    m.specification,
                    m.unit
                FROM material_record_items mri
                LEFT JOIN materials m ON mri.material_id = m.id
                WHERE mri.record_id = ?
            `;
            const items = await query(itemsSql, [application.id]);
            application.materials = items;
        }

        Response.success(res, { list, total });
    } catch (error) {
        console.error('获取领料申请列表失败:', error);
        Response.serverError(res, '获取领料申请列表失败');
    }
};

/**
 * 获取领料申请详情
 * GET /api/material-applications/:id
 */
const getMaterialApplicationById = async(req, res) => {
    try {
        const { id } = req.params;

        // 获取申请主信息
        const applicationSql = `
            SELECT 
                mr.id,
                mr.order_id as apply_no,
                mr.created_at as apply_date,
                mr.operator_id,
                mr.recipient as applicant_id,
                mr.purpose,
                mr.remarks,
                mr.created_at,
                e.name as applicant_name,
                u.real_name as operator_name
            FROM material_records mr
            LEFT JOIN employees e ON mr.recipient = e.id
            LEFT JOIN users u ON mr.operator_id = u.id
            WHERE mr.id = ? AND mr.type = 'apply'
        `;
        const applications = await query(applicationSql, [id]);

        if (applications.length === 0) {
            return Response.error(res, '申请不存在', 404);
        }

        const application = applications[0];

        // 获取物料明细
        const itemsSql = `
            SELECT 
                mri.material_id,
                mri.quantity,
                mri.current_quantity,
                m.name as material_name,
                m.company_code,
                m.client_code,
                m.specification,
                m.unit,
                m.stock_quantity
            FROM material_record_items mri
            LEFT JOIN materials m ON mri.material_id = m.id
            WHERE mri.record_id = ?
        `;
        const items = await query(itemsSql, [id]);
        application.materials = items;

        Response.success(res, application);
    } catch (error) {
        console.error('获取领料申请详情失败:', error);
        Response.serverError(res, '获取领料申请详情失败');
    }
};

/**
 * 批准领料申请并出库
 * PUT /api/material-applications/:id/approve
 */
const approveApplication = async(req, res) => {
    try {
        const { id } = req.params;
        const { remarks } = req.body;

        // 检查申请是否存在
        const existingSql = 'SELECT id, order_id, operator_id, purpose FROM material_records WHERE id = ? AND type = "apply"';
        const existing = await query(existingSql, [id]);
        if (existing.length === 0) {
            return Response.error(res, '申请不存在', 404);
        }

        const application = existing[0];

        // 开始事务
        await execute('BEGIN TRANSACTION');

        try {
            // 获取申请明细
            const itemsSql = `
                SELECT mri.material_id, mri.quantity
                FROM material_record_items mri
                WHERE mri.record_id = ?
            `;
            const applicationItems = await query(itemsSql, [id]);

            if (applicationItems.length === 0) {
                throw new Error('申请明细不存在');
            }

            // 创建出库记录
            const outboundRecordId = generatePrefixedId('mr');
            await execute(
                `INSERT INTO material_records (id, type, operator_id, order_id, purpose, remarks, created_at) VALUES (?, 'out', ?, ?, ?, ?, datetime('now'))`, [outboundRecordId, application.operator_id, application.order_id, application.purpose, remarks || '']
            );

            // 处理每个物料的出库
            for (const item of applicationItems) {
                // 获取当前库存数量
                const currentStockSql = 'SELECT stock_quantity FROM materials WHERE id = ?';
                const currentStockResult = await query(currentStockSql, [item.material_id]);
                const currentQuantity = currentStockResult[0] && currentStockResult[0].stock_quantity ? currentStockResult[0].stock_quantity : 0;

                // 检查库存是否足够
                if (currentQuantity < item.quantity) {
                    throw new Error(`物料 ${item.material_id} 库存不足，当前库存: ${currentQuantity}，申请数量: ${item.quantity}`);
                }

                // 更新库存
                const newQuantity = currentQuantity - item.quantity;
                await execute('UPDATE materials SET stock_quantity = ? WHERE id = ?', [newQuantity, item.material_id]);

                // 记录出库明细
                await execute(
                    `INSERT INTO material_record_items (id, record_id, material_id, quantity, current_quantity, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))`, [generatePrefixedId('mri'), outboundRecordId, item.material_id, item.quantity, currentQuantity]
                );
            }

            // 提交事务
            await execute('COMMIT');

            Response.success(res, null, '申请批准成功，物料已出库');
        } catch (error) {
            // 回滚事务
            await execute('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('批准申请失败:', error);
        Response.serverError(res, '批准申请失败: ' + error.message);
    }
};

/**
 * 删除领料申请
 * DELETE /api/material-applications/:id
 */
const deleteMaterialApplication = async(req, res) => {
    try {
        const { id } = req.params;

        // 检查申请是否存在
        const existingSql = 'SELECT id FROM material_records WHERE id = ? AND type = "apply"';
        const existing = await query(existingSql, [id]);
        if (existing.length === 0) {
            return Response.error(res, '申请不存在', 404);
        }

        // 开始事务
        await execute('BEGIN TRANSACTION');

        try {
            // 删除物料记录明细
            await execute('DELETE FROM material_record_items WHERE record_id = ?', [id]);

            // 删除物料记录主表
            await execute('DELETE FROM material_records WHERE id = ?', [id]);

            // 提交事务
            await execute('COMMIT');

            Response.success(res, null, '申请删除成功');
        } catch (error) {
            // 回滚事务
            await execute('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('删除领料申请失败:', error);
        Response.serverError(res, '删除领料申请失败');
    }
};

/**
 * 获取物料记录中的申请记录
 * GET /api/material-applications/records
 */
const getApplicationRecords = async(req, res) => {
    try {
        const {
            page = 1,
                pageSize = 20,
                type = 'apply',
                startDate = '',
                endDate = '',
                operatorId = ''
        } = req.query;

        let where = 'WHERE mr.type = ?';
        const params = [type];

        if (startDate) {
            where += ' AND mr.created_at >= ?';
            params.push(startDate);
        }

        if (endDate) {
            where += ' AND mr.created_at <= ?';
            params.push(endDate);
        }

        if (operatorId) {
            where += ' AND mr.operator_id = ?';
            params.push(operatorId);
        }

        const offset = (page - 1) * pageSize;

        // 获取总数
        const countSql = `
            SELECT COUNT(*) as total 
            FROM material_records mr 
            ${where}
        `;
        const countResult = await query(countSql, params);
        const total = countResult[0] && countResult[0].total ? countResult[0].total : 0;

        // 获取列表
        const listSql = `
            SELECT 
                mr.*,
                e.name as operator_name,
                ma.apply_no,
                ma.apply_date,
                ma.status as application_status
            FROM material_records mr
            LEFT JOIN employees e ON mr.operator_id = e.id
            LEFT JOIN material_applications ma ON mr.order_id = ma.id
            ${where}
            ORDER BY mr.created_at DESC
            LIMIT ? OFFSET ?
        `;

        const listParams = [...params, parseInt(pageSize), offset];
        const list = await query(listSql, listParams);

        // 获取每个记录的物料明细
        for (const record of list) {
            const itemsSql = `
                SELECT 
                    mri.*,
                    m.name as material_name,
                    m.company_code,
                    m.client_code,
                    m.specification,
                    m.unit
                FROM material_record_items mri
                LEFT JOIN materials m ON mri.material_id = m.id
                WHERE mri.record_id = ?
            `;
            const items = await query(itemsSql, [record.id]);
            record.materials = items;
        }

        Response.success(res, { list, total });
    } catch (error) {
        console.error('获取申请记录失败:', error);
        Response.serverError(res, '获取申请记录失败');
    }
};

module.exports = {
    createMaterialApplication,
    getMaterialApplications,
    getMaterialApplicationById,
    approveApplication,
    deleteMaterialApplication,
    getApplicationRecords
};