-- 为loose_orders表添加新字段
-- 地址字段
ALTER TABLE loose_orders ADD COLUMN address TEXT COMMENT '地址信息';

-- 诉求描述字段
ALTER TABLE loose_orders ADD COLUMN appeal_description TEXT COMMENT '诉求描述';

-- 费用合计金额字段
ALTER TABLE loose_orders ADD COLUMN total_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '费用合计金额';

-- 批次ID字段
ALTER TABLE loose_orders ADD COLUMN batch_id VARCHAR(36) COMMENT '批次ID，用于标识同一批导入的数据';

-- 为batch_id创建索引
CREATE INDEX idx_loose_orders_batch_id ON loose_orders(batch_id);

-- 为address创建索引
CREATE INDEX idx_loose_orders_address ON loose_orders(address(100));

-- 为total_amount创建索引
CREATE INDEX idx_loose_orders_total_amount ON loose_orders(total_amount); 