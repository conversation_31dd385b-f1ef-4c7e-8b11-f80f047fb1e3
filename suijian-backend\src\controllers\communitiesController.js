const Response = require('../utils/response');
const { logger } = require('../utils/logger');
const { execute, query } = require('../config/database');
const { v4: uuidv4 } = require('uuid');
const addressParser = require('../utils/addressParser');

/**
 * 获取小区列表
 */
const getCommunities = async(req, res) => {
    try {
        const {
            page = 1,
                pageSize = 20,
                name,
                district,
                city,
                province
        } = req.query;

        let whereClause = 'WHERE status = 1';
        const params = [];

        if (name) {
            whereClause += ' AND (name LIKE ? OR alias LIKE ?)';
            params.push(`%${name}%`, `%${name}%`);
        }

        if (district) {
            whereClause += ' AND district LIKE ?';
            params.push(`%${district}%`);
        }

        if (city) {
            whereClause += ' AND city LIKE ?';
            params.push(`%${city}%`);
        }

        if (province) {
            whereClause += ' AND province LIKE ?';
            params.push(`%${province}%`);
        }

        // 获取总数
        const countSql = `SELECT COUNT(*) as total FROM communities ${whereClause}`;
        const countResult = await query(countSql, params);
        const total = countResult[0].total;

        // 获取分页数据
        const offset = (page - 1) * pageSize;
        const sql = `
            SELECT 
                id, name, alias, district, address, 
                status, created_at, updated_at
            FROM communities 
            ${whereClause}
            ORDER BY name
            LIMIT ? OFFSET ?
        `;

        const rows = await query(sql, [...params, parseInt(pageSize), offset]);

        // 转换字段名为驼峰格式
        const formattedRows = rows.map(row => ({
            id: row.id,
            name: row.name,
            alias: row.alias,
            district: row.district,
            address: row.address,
            status: row.status,
            createdAt: row.created_at,
            updatedAt: row.updated_at
        }));

        Response.success(res, {
            list: formattedRows,
            total,
            page: parseInt(page),
            pageSize: parseInt(pageSize)
        }, '获取小区列表成功');

    } catch (error) {
        logger.error('获取小区列表失败', error);
        Response.error(res, '获取小区列表失败: ' + error.message);
    }
};

/**
 * 创建小区
 */
const createCommunity = async(req, res) => {
    try {
        const {
            name,
            alias,
            district,
            city,
            province,
            address
        } = req.body;

        // 检查小区名称是否已存在
        const existingCommunity = await query(
            'SELECT id FROM communities WHERE name = ? AND status = 1', [name]
        );

        if (existingCommunity.length > 0) {
            return Response.badRequest(res, '小区名称已存在');
        }

        const id = uuidv4();
        const now = new Date().toISOString();
        const sql = `
            INSERT INTO communities (
                id, name, alias, district, address,
                status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `;

        await execute(sql, [
            id, name, alias || '', district || '', address || '',
            1, now, now
        ]);

        // 清除地址解析器缓存
        addressParser.communityCache.clear();
        addressParser.lastCacheUpdate = 0;

        Response.success(res, { id }, '创建小区成功');

    } catch (error) {
        logger.error('创建小区失败', error);
        Response.error(res, '创建小区失败: ' + error.message);
    }
};

/**
 * 更新小区
 */
const updateCommunity = async(req, res) => {
    try {
        const { id } = req.params;
        const {
            name,
            alias,
            district,
            city,
            province,
            address,
            status
        } = req.body;

        // 检查小区是否存在
        const existingCommunity = await query(
            'SELECT id FROM communities WHERE id = ?', [id]
        );

        if (existingCommunity.length === 0) {
            return Response.notFound(res, '小区不存在');
        }

        // 如果修改了小区名称，检查是否重复
        if (name) {
            const duplicateCommunity = await query(
                'SELECT id FROM communities WHERE name = ? AND id != ? AND status = 1', [name, id]
            );

            if (duplicateCommunity.length > 0) {
                return Response.badRequest(res, '小区名称已存在');
            }
        }

        const now = new Date().toISOString();
        const sql = `
            UPDATE communities SET
                name = COALESCE(?, name),
                alias = COALESCE(?, alias),
                district = COALESCE(?, district),
                address = COALESCE(?, address),
                status = COALESCE(?, status),
                updated_at = ?
            WHERE id = ?
        `;

        await execute(sql, [
            name, alias, district, address, status, now, id
        ]);

        // 清除地址解析器缓存
        addressParser.communityCache.clear();
        addressParser.lastCacheUpdate = 0;

        Response.success(res, null, '更新小区成功');

    } catch (error) {
        logger.error('更新小区失败', error);
        Response.error(res, '更新小区失败: ' + error.message);
    }
};

/**
 * 删除小区
 */
const deleteCommunity = async(req, res) => {
    try {
        const { id } = req.params;

        // 检查是否有订单使用了该小区
        const ordersUsingCommunity = await query(
            'SELECT COUNT(*) as count FROM loose_orders WHERE community_name = (SELECT name FROM communities WHERE id = ?)', [id]
        );

        if (ordersUsingCommunity[0].count > 0) {
            return Response.badRequest(res, '该小区已被订单使用，无法删除');
        }

        const result = await execute(
            'DELETE FROM communities WHERE id = ?', [id]
        );

        if (result.changes === 0) {
            return Response.notFound(res, '小区不存在');
        }

        // 清除地址解析器缓存
        addressParser.communityCache.clear();
        addressParser.lastCacheUpdate = 0;

        Response.success(res, null, '删除小区成功');

    } catch (error) {
        logger.error('删除小区失败', error);
        Response.error(res, '删除小区失败: ' + error.message);
    }
};

/**
 * 获取小区详情
 */
const getCommunityById = async(req, res) => {
    try {
        const { id } = req.params;

        const sql = `
            SELECT 
                id, name, alias, district, address, 
                status, created_at, updated_at
            FROM communities 
            WHERE id = ?
        `;

        const rows = await query(sql, [id]);

        if (rows.length === 0) {
            return Response.notFound(res, '小区不存在');
        }

        const community = rows[0];
        Response.success(res, {
            id: community.id,
            name: community.name,
            alias: community.alias,
            district: community.district,
            address: community.address,
            status: community.status,
            createdAt: community.created_at,
            updatedAt: community.updated_at
        }, '获取小区详情成功');

    } catch (error) {
        logger.error('获取小区详情失败', error);
        Response.error(res, '获取小区详情失败: ' + error.message);
    }
};

/**
 * 测试地址解析
 */
const testAddressParse = async(req, res) => {
    try {
        const { address } = req.body;

        if (!address) {
            return Response.badRequest(res, '请提供地址');
        }

        const result = await addressParser.parseAddress(address);

        Response.success(res, {
            originalAddress: address,
            parsedResult: result
        }, '地址解析测试成功');

    } catch (error) {
        logger.error('地址解析测试失败', error);
        Response.error(res, '地址解析测试失败: ' + error.message);
    }
};

/**
 * 批量导入小区
 */
const importCommunities = async(req, res) => {
    try {
        const { communities } = req.body;

        if (!Array.isArray(communities) || communities.length === 0) {
            return Response.badRequest(res, '请提供小区数据');
        }

        let successCount = 0;
        let skipCount = 0;
        const errors = [];

        for (const community of communities) {
            try {
                const { name, alias, district, city, province, address } = community;

                if (!name) {
                    errors.push(`小区名称不能为空`);
                    continue;
                }

                // 检查小区名称是否已存在
                const existingCommunity = await query(
                    'SELECT id FROM communities WHERE name = ? AND status = 1', [name]
                );

                if (existingCommunity.length > 0) {
                    skipCount++;
                    continue;
                }

                const id = uuidv4();
                const now = new Date().toISOString();
                const sql = `
                    INSERT INTO communities (
                        id, name, alias, district, address,
                        status, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                `;

                await execute(sql, [
                    id, name, alias || '', district || '', address || '',
                    1, now, now
                ]);

                successCount++;

            } catch (error) {
                errors.push(`小区 ${community.name}: ${error.message}`);
            }
        }

        // 清除地址解析器缓存
        addressParser.communityCache.clear();
        addressParser.lastCacheUpdate = 0;

        Response.success(res, {
            total: communities.length,
            success: successCount,
            skip: skipCount,
            error: errors.length,
            errors: errors.slice(0, 10) // 只返回前10个错误
        }, '批量导入小区完成');

    } catch (error) {
        logger.error('批量导入小区失败', error);
        Response.error(res, '批量导入小区失败: ' + error.message);
    }
};

module.exports = {
    getCommunities,
    createCommunity,
    updateCommunity,
    deleteCommunity,
    getCommunityById,
    testAddressParse,
    importCommunities
};