<template>
  <div class="order-assign-entry">
    <el-card class="form-card">
      <!-- 返回按钮和状态指示 -->
      <div class="header-section">
        <div class="back-button">
          <el-button @click="goBack" icon="ArrowLeft">
            返回列表
          </el-button>
        </div>
      </div>
      
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="order-assign-form"
      >
      
      <!-- 基本信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">基本信息</div>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务类型:">
            <el-checkbox-group v-model="formData.businessType">
              <el-checkbox label="挂表">挂表</el-checkbox>
              <el-checkbox label="改管">改管</el-checkbox>
              <el-checkbox label="二次挂表">二次挂表</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="派单时间:">
            <el-date-picker
              v-model="formData.dispatchTime"
              type="date"
              placeholder="请选择派单时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="姓名:">
            <el-input v-model="formData.customerName" placeholder="请输入客户姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="户号:">
            <el-input v-model="formData.accountNumber" placeholder="请输入户号" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业主联系电话:">
            <el-input v-model="formData.ownerPhone" placeholder="请输入业主联系电话" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="小区地址:">
            <el-input v-model="formData.communityAddress" placeholder="请输入小区地址" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="楼栋:">
            <el-input v-model="formData.building" placeholder="请输入楼栋" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="房号:">
                              <el-input v-model="formData.roomNo" placeholder="请输入房号" />
          </el-form-item>
        </el-col>
              </el-row>
        
        <!-- 施工单位 -->
        <el-row :gutter="20" class="form-section">
          <el-col :span="24">
            <div class="section-title">施工单位</div>
          </el-col>
          <el-col :span="8">
            <el-form-item label="安装时间:">
              <el-date-picker
                v-model="formData.installationTime"
                type="date"
                placeholder="请选择安装时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单位名称:">
              <el-input v-model="formData.unitName" placeholder="请输入单位名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="安装人员:">
              <el-input v-model="formData.installer" placeholder="请输入安装人员" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="安装人员联系电话:">
              <el-input v-model="formData.installerPhone" placeholder="请输入安装人员联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        
                <!-- 安装信息 -->
        <el-row :gutter="20" class="form-section">
          <el-col :span="24">
            <div class="section-title">安装信息</div>
          </el-col>
          
          <!-- 第一行 -->
          <el-col :span="6">
            <el-form-item label="明装/暗埋:">
              <el-radio-group v-model="formData.installationType">
                <el-radio label="明装">明装</el-radio>
                <el-radio label="暗埋">暗埋</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="火点数:">
              <el-input-number v-model="formData.firePoints" :min="0" style="width: 100%" />
              <span style="margin-left: 5px;">个</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="表前阀:">
              <el-input-number v-model="formData.preMeterValve" :min="0" style="width: 100%" />
              <span style="margin-left: 5px;">个</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="灶前阀:">
              <el-input-number v-model="formData.preStoveValve" :min="0" style="width: 100%" />
              <span style="margin-left: 5px;">个</span>
            </el-form-item>
          </el-col>
          
          <!-- 第二行 -->
          <el-col :span="6">
            <el-form-item label="调压器:">
              <el-input-number v-model="formData.regulator" :min="0" style="width: 100%" />
              <span style="margin-left: 5px;">个</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="表箱:">
              <el-radio-group v-model="formData.meterBox">
                <el-radio label="有">有</el-radio>
                <el-radio label="无">无</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="进气方式:">
              <el-radio-group v-model="formData.airIntakeMethod">
                <el-radio label="左">左</el-radio>
                <el-radio label="右">右</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="表号:">
              <el-input v-model="formData.meterNumber" placeholder="请输入表号" />
            </el-form-item>
          </el-col>
          
          <!-- 第三行 -->
          <el-col :span="6">
            <el-form-item label="是否换表:">
              <el-radio-group v-model="formData.changeMeter">
                <el-radio label="是">是</el-radio>
                <el-radio label="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="旧表表底:">
              <el-input v-model="formData.oldMeterReading" placeholder="请输入旧表表底" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="换表原因:">
              <el-input v-model="formData.meterChangeReason" placeholder="请输入换表原因" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="表底数:">
              <el-input v-model="formData.meterReading" placeholder="请输入表底数" />
            </el-form-item>
          </el-col>
          
          <!-- 第四行 -->
          <el-col :span="6">
            <el-form-item label="燃气表型号:">
              <el-input v-model="formData.gasMeterModel" placeholder="请输入燃气表型号" />
            </el-form-item>
          </el-col>
          
          <!-- 表前材料 -->
          <el-col :span="24">
            <div class="sub-section-title">表前材料</div>
          </el-col>
          <el-col :span="6">
            <el-form-item label="非定尺波纹管:">
              <el-input-number v-model="formData.preMeterCorrugatedPipe" :min="0" :precision="1" style="width: 100%" />
              <span style="margin-left: 5px;">m</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="镀锌钢管:">
              <el-input-number v-model="formData.preMeterGalvanizedPipe" :min="0" :precision="1" style="width: 100%" />
              <span style="margin-left: 5px;">m</span>
            </el-form-item>
          </el-col>
          
          <!-- 表后材料 -->
          <el-col :span="24">
            <div class="sub-section-title">表后材料</div>
          </el-col>
          <el-col :span="6">
            <el-form-item label="非定尺波纹管:">
              <el-input-number v-model="formData.postMeterCorrugatedPipe" :min="0" :precision="1" style="width: 100%" />
              <span style="margin-left: 5px;">m</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="镀锌钢管:">
              <el-input-number v-model="formData.postMeterGalvanizedPipe" :min="0" :precision="1" style="width: 100%" />
              <span style="margin-left: 5px;">m</span>
            </el-form-item>
          </el-col>
          
          <!-- 强压测试 -->
          <el-col :span="24">
            <div class="sub-section-title">强压测试 (>0.1MPa/30min)</div>
          </el-col>
          <el-col :span="6">
            <el-form-item label="测试压力:">
              <el-input v-model="formData.strongTestPressure" placeholder="请输入测试压力" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="开始时间:">
              <el-time-picker
                v-model="formData.strongTestStartTime"
                placeholder="选择开始时间"
                format="HH:mm"
                value-format="HH:mm"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="结束时间:">
              <el-time-picker
                v-model="formData.strongTestEndTime"
                placeholder="选择结束时间"
                format="HH:mm"
                value-format="HH:mm"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否合格:">
              <el-radio-group v-model="formData.strongTestQualified">
                <el-radio label="是">是</el-radio>
                <el-radio label="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          
          <!-- 气密测试 -->
          <el-col :span="24">
            <div class="sub-section-title">气密测试 (>5KPa/15min)</div>
          </el-col>
          <el-col :span="6">
            <el-form-item label="测试压力:">
              <el-input v-model="formData.airTightTestPressure" placeholder="请输入测试压力" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="开始时间:">
              <el-time-picker
                v-model="formData.airTightTestStartTime"
                placeholder="选择开始时间"
                format="HH:mm"
                value-format="HH:mm"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="结束时间:">
              <el-time-picker
                v-model="formData.airTightTestEndTime"
                placeholder="选择结束时间"
                format="HH:mm"
                value-format="HH:mm"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否合格:">
              <el-radio-group v-model="formData.airTightTestQualified">
                <el-radio label="是">是</el-radio>
                <el-radio label="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      
                      <!-- 管件/配件清单 -->
        <el-row :gutter="20" class="form-section">
          <el-col :span="24">
            <div class="section-title">管件/配件清单</div>
          </el-col>
          <el-col :span="24">
            <div class="components-list">
              <el-table :data="formData.components" border style="width: 100%">
                <el-table-column label="配件名称" min-width="200">
                  <template #default="{ row }">
                    <span>{{ row.name }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="数量" width="120">
                  <template #default="{ row }">
                    <el-input-number
                      v-model="row.quantity"
                      :min="0"
                      :precision="1"
                      controls-position="right"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-col>
        </el-row>
      
      <!-- 其他情况备注 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">其他情况备注</div>
        </el-col>
        <el-col :span="24">
          <el-form-item>
            <el-input
              v-model="formData.otherNotes"
              type="textarea"
              :rows="3"
              placeholder="请输入其他情况备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 用户评价 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">用户评价</div>
        </el-col>
        <el-col :span="24">
          <el-form-item>
            <el-radio-group v-model="formData.userEvaluation">
              <el-radio label="很满意">很满意</el-radio>
              <el-radio label="满意">满意</el-radio>
              <el-radio label="一般">一般</el-radio>
              <el-radio label="较差">较差</el-radio>
              <el-radio label="差">差</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 签字确认 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <div class="section-title">签字确认</div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户/委托人:">
            <el-input v-model="formData.userSignature" placeholder="请输入用户/委托人签名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="安装人员:">
            <el-input v-model="formData.installerSignature" placeholder="请输入安装人员签名" />
          </el-form-item>
        </el-col>
              </el-row>
        
        <!-- 工单图片上传 -->
        <el-row :gutter="20" class="form-section">
          <el-col :span="24">
            <div class="section-title">工单图片上传</div>
          </el-col>
          <el-col :span="24">
            <el-form-item label="工单图片:">
              <el-upload
                ref="uploadRef"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :before-upload="beforeUpload"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                :file-list="fileList"
                :limit="5"
                accept="image/*"
                list-type="picture-card"
                :auto-upload="false"
              >
                <el-icon><Plus /></el-icon>
                <template #tip>
                  <div class="el-upload__tip">
                    支持jpg/png格式，文件大小不超过10MB，最多上传5张图片
                  </div>
                </template>
              </el-upload>
              
              <div class="upload-actions" v-if="fileList.length > 0">
                <el-button @click="clearUpload">清空</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 条形码 -->
        <el-row :gutter="20" class="form-section">
          <el-col :span="24">
            <div class="section-title">条形码</div>
          </el-col>
          <el-col :span="12">
            <el-form-item label="条形码:">
              <el-input
                v-model="formData.barcode"
                placeholder="请输入条形码数字"
                maxlength="20"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="条形码预览:">
              <div class="barcode-preview">
                <span v-if="formData.barcode" class="barcode-text">{{ formData.barcode }}</span>
                <span v-else class="barcode-placeholder">请输入条形码数字</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button type="primary" @click="handleSave">保存</el-button>
          <el-button type="success" @click="handleSubmit">提交</el-button>
          <el-button type="info" @click="handleConfirmCheck">确认核对</el-button>
          <el-button @click="handlePrint">打印</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { Plus } from '@element-plus/icons-vue'

const router = useRouter()

// 返回列表页面
const goBack = () => {
  router.back()
}

// 表单数据
// 上传相关
const fileList = ref([])
const uploadRef = ref()
const uploadUrl = '/api/upload' // 上传接口
const uploadHeaders = {
  'Authorization': 'Bearer ' + localStorage.getItem('token') || ''
}

// 在formData中增加checked字段
const formData = reactive({
  // 基本信息
  businessType: [], // 业务类型
  dispatchTime: '', // 派单时间
  installationTime: '', // 安装时间
  customerName: '', // 姓名
  accountNumber: '', // 户号
  ownerPhone: '', // 业主联系电话
  communityAddress: '', // 小区地址
  building: '', // 楼栋
  roomNo: '', // 房号
  constructionUnit: '', // 施工单位
  unitName: '', // 单位名称
  installer: '', // 安装人员
  installerPhone: '', // 安装人员联系电话
  
  // 安装信息
  installationType: '', // 明装/暗埋
  firePoints: 0, // 火点数
  preMeterValve: 0, // 表前阀
  preStoveValve: 0, // 灶前阀
  regulator: 0, // 调压器
  meterBox: '', // 表箱
  airIntakeMethod: '', // 进气方式
  meterNumber: '', // 表号
  changeMeter: '', // 是否换表
  oldMeterReading: '', // 旧表表底
  meterChangeReason: '', // 换表原因
  meterReading: '', // 表底数
  gasMeterModel: '', // 燃气表型号
  
  // 表前材料
  preMeterCorrugatedPipe: 0, // 表前非定尺波纹管
  preMeterGalvanizedPipe: 0, // 表前镀锌钢管
  
  // 表后材料
  postMeterCorrugatedPipe: 0, // 表后非定尺波纹管
  postMeterGalvanizedPipe: 0, // 表后镀锌钢管
  
  // 强压测试
  strongTestPressure: '', // 强压测试压力
  strongTestStartTime: '', // 强压测试开始时间
  strongTestEndTime: '', // 强压测试结束时间
  strongTestQualified: '', // 强压测试是否合格
  
  // 气密测试
  airTightTestPressure: '', // 气密测试压力
  airTightTestStartTime: '', // 气密测试开始时间
  airTightTestEndTime: '', // 气密测试结束时间
  airTightTestQualified: '', // 气密测试是否合格
  
  // 管件/配件清单
  components: [
    { name: '50CM(定尺波纹管)', quantity: 0 },
    { name: '100CM(定尺波纹管)', quantity: 0 },
    { name: '热收缩套', quantity: 0 },
    { name: '12CM镀锌钢管', quantity: 0 },
    { name: '18CM镀锌钢管', quantity: 0 },
    { name: '表防盗气卡锁', quantity: 0 },
    { name: '表接波纹管', quantity: 0 },
    { name: '表接头', quantity: 0 },
    { name: '燃气表托架', quantity: 0 },
    { name: '15镀锌定制补芯', quantity: 0 },
    { name: '15不锈钢管卡', quantity: 0 },
    { name: '15镀锌外丝弯头', quantity: 0 },
    { name: '15镀锌弯头', quantity: 0 },
    { name: '15镀锌六角外丝', quantity: 0 },
    { name: '15镀锌内丝', quantity: 0 },
    { name: '15镀锌三通', quantity: 0 },
    { name: '半月板直板', quantity: 0 },
    { name: '90°侧弯盖板', quantity: 0 },
    { name: '90°外弯盖板', quantity: 0 },
    { name: '爆炸螺栓', quantity: 0 },
    { name: '定中装饰盖', quantity: 0 },
    { name: '报警器', quantity: 0 },
    { name: '报警器切断阀', quantity: 0 },
    { name: '快速接头', quantity: 0 },
    { name: '灶具波纹管', quantity: 0 },
    { name: '堵头', quantity: 0 }
  ] as Array<{
    name: string
    quantity: number
  }>,
  
  // 其他信息
  otherNotes: '', // 其他情况备注
  userEvaluation: '', // 用户评价
  userSignature: '', // 用户/委托人签名
  installerSignature: '', // 安装人员签名
  
  // 工单图片和条形码
  barcode: '', // 条形码
  checked: false // 新增的checked字段
})

// 在formData定义后添加rules
const rules = {
  customerName: [{ required: true, message: '请输入客户姓名', trigger: 'blur' }],
  accountNumber: [{ required: true, message: '请输入户号', trigger: 'blur' }],
  ownerPhone: [{ required: true, message: '请输入业主联系电话', trigger: 'blur' }]
}


// 上传前验证
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过 10MB!')
    return false
  }
  return true
}

// 上传成功
const handleUploadSuccess = (response: any, file: any) => {
  ElMessage.success('图片上传成功')
  console.log('上传成功:', response)
}

// 上传失败
const handleUploadError = (error: any) => {
  ElMessage.error('图片上传失败')
  console.error('上传失败:', error)
}

// 清空上传
const clearUpload = () => {
  fileList.value = []
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 保存
const handleSave = () => {
  ElMessage.success('保存成功')
  console.log('保存数据:', formData)
}

// 提交
const handleSubmit = () => {
  ElMessage.success('提交成功')
  console.log('提交数据:', formData)
}

// 打印
const handlePrint = () => {
  ElMessage.success('开始打印')
  console.log('打印数据:', formData)
}

// 取消
const handleCancel = () => {
  ElMessage.info('已取消')
  // 重置表单
  Object.keys(formData).forEach(key => {
    if (key === 'businessType') {
      (formData as any)[key] = []
    } else if (key === 'components') {
      (formData as any)[key] = []
    } else if (typeof (formData as any)[key] === 'number') {
      (formData as any)[key] = 0
    } else {
      (formData as any)[key] = ''
    }
  })
  // 清空上传文件
  clearUpload()
}

// 在<script setup>中增加handleConfirmCheck方法
const handleConfirmCheck = () => {
  formData.checked = true
  ElMessage.success('已确认核对！')
}
</script>

<style lang="scss" scoped>
.order-assign-entry {
  padding: 0;
  height: 100%;
  width: 100%;
  overflow: auto;
  background: #f5f7fa;
}

.form-card {
  width: 100%;
  min-height: 100%;
  border: none;
  box-shadow: none;
  background: #fff;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
    margin-bottom: 20px;
  }

.back-button {
  margin-bottom: 0;
}

.order-assign-form {
  margin-top: 20px;
}
  
  .form-section {
    margin-bottom: 20px;
    
      .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #606266;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }
  
  .sub-section-title {
    font-size: 14px;
    font-weight: bold;
    color: #409eff;
    margin: 20px 0 10px 0;
    padding-left: 10px;
    border-left: 3px solid #409eff;
  }
    
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  
  .components-list {
    margin: 20px 0;
  }
  
  .upload-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
  }
  
  .barcode-preview {
    padding: 10px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #f5f7fa;
    min-height: 40px;
    display: flex;
    align-items: center;
  }
  
  .barcode-text {
    font-family: 'Courier New', monospace;
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    letter-spacing: 2px;
  }
  
  .barcode-placeholder {
    color: #c0c4cc;
    font-style: italic;
  }
  
  :deep(.el-upload--picture-card) {
    width: 148px;
    height: 148px;
    line-height: 146px;
  }
  
  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 148px;
    height: 148px;
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
  }
}
</style>