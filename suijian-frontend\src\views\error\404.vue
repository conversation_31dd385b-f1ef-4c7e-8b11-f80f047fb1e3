<template>
  <div class="error-404">
    <div class="error-container">
      <div class="error-content">
        <div class="error-code">404</div>
        <div class="error-title">页面未找到</div>
        <div class="error-description">
          抱歉，您访问的页面不存在或已被删除。
        </div>
        <div class="error-actions">
          <el-button type="primary" @click="goHome">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回上页
          </el-button>
        </div>
      </div>
      <div class="error-image">
        <div class="image-placeholder">
          🔍
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { House, ArrowLeft } from '@element-plus/icons-vue'

const router = useRouter()

// 返回首页
const goHome = () => {
  router.push('/dashboard')
}

// 返回上页
const goBack = () => {
  router.go(-1)
}
</script>

<style scoped lang="scss">
.error-404 {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  .error-container {
    display: flex;
    align-items: center;
    gap: 60px;
    max-width: 800px;
    padding: 40px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 30px;
      padding: 30px 20px;
      margin: 20px;
    }
  }
  
  .error-content {
    flex: 1;
    text-align: left;
    
    @media (max-width: 768px) {
      text-align: center;
    }
  }
  
  .error-code {
    font-size: 120px;
    font-weight: bold;
    color: #409eff;
    line-height: 1;
    margin-bottom: 20px;
    
    @media (max-width: 768px) {
      font-size: 80px;
    }
  }
  
  .error-title {
    font-size: 32px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 16px;
    
    @media (max-width: 768px) {
      font-size: 24px;
    }
  }
  
  .error-description {
    font-size: 16px;
    color: #606266;
    line-height: 1.6;
    margin-bottom: 32px;
  }
  
  .error-actions {
    display: flex;
    gap: 16px;
    
    @media (max-width: 768px) {
      justify-content: center;
    }
    
    .el-button {
      padding: 12px 24px;
      font-size: 14px;
      
      .el-icon {
        margin-right: 6px;
      }
    }
  }
  
  .error-image {
    flex: 0 0 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    @media (max-width: 768px) {
      flex: none;
    }
  }
  
  .image-placeholder {
    font-size: 120px;
    opacity: 0.3;
    
    @media (max-width: 768px) {
      font-size: 80px;
    }
  }
}
</style>
