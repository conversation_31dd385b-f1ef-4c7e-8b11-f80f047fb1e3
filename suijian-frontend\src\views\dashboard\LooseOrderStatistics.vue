<template>
  <div class="loose-order-statistics">
    <!-- 统计概览 -->
    <el-card class="overview-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>📊 散单情况概览</span>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <div class="overview-content">
        <div class="overview-item">
          <span class="label">📋 总散单数:</span>
          <span class="value">{{ totalOrders }}单</span>
        </div>
        <div class="overview-item">
          <span class="label">💰 总金额:</span>
          <span class="amount">¥{{ formatNumber(totalAmount) }}</span>
        </div>
        <div class="overview-item">
          <span class="label">⏱️ 本月新增:</span>
          <span class="value">{{ monthlyNewOrders }}单</span>
        </div>
      </div>
    </el-card>

    <!-- 散单列表 -->
    <el-card class="order-list-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>📋 散单列表</span>
          <div class="header-actions">
            <el-button type="success" size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>

      <el-table :data="paginatedOrderList" style="width: 100%" border>
        <el-table-column prop="serialNo" label="序号" width="80" align="center" />
        <el-table-column prop="projectName" label="单项工程名称" width="200" />
        <el-table-column prop="preMeterLaborCost" label="表前安装人工费" width="120" align="center">
          <template #default="{ row }">
            ¥{{ formatNumber(row.preMeterLaborCost) }}
          </template>
        </el-table-column>
        <el-table-column prop="indoorLaborCost" label="户内安装人工费" width="120" align="center">
          <template #default="{ row }">
            ¥{{ formatNumber(row.indoorLaborCost) }}
          </template>
        </el-table-column>
        <el-table-column prop="meterInstallationCost" label="燃气表安装费" width="120" align="center">
          <template #default="{ row }">
            ¥{{ formatNumber(row.meterInstallationCost) }}
          </template>
        </el-table-column>
        <el-table-column prop="minorInstallationCost" label="燃气小件安装费" width="130" align="center">
          <template #default="{ row }">
            ¥{{ formatNumber(row.minorInstallationCost) }}
          </template>
        </el-table-column>
        <el-table-column prop="secondaryInstallationCost" label="二次安装费" width="110" align="center">
          <template #default="{ row }">
            ¥{{ formatNumber(row.secondaryInstallationCost) }}
          </template>
        </el-table-column>
        <el-table-column prop="indoorFittingsCost" label="户内配件费" width="110" align="center">
          <template #default="{ row }">
            ¥{{ formatNumber(row.indoorFittingsCost) }}
          </template>
        </el-table-column>
        <el-table-column prop="totalCost" label="合计" width="120" align="center">
          <template #default="{ row }">
            <span class="total-cost">¥{{ formatNumber(row.totalCost) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="orderDate" label="订单日期" width="110" align="center" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewOrderDetails(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="orderList.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="散单详情" width="800px">
      <div v-if="selectedOrder" class="order-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工程名称">{{ selectedOrder.projectName }}</el-descriptions-item>
          <el-descriptions-item label="订单日期">{{ selectedOrder.orderDate }}</el-descriptions-item>
          <el-descriptions-item label="表前安装人工费">¥{{ formatNumber(selectedOrder.preMeterLaborCost) }}</el-descriptions-item>
          <el-descriptions-item label="户内安装人工费">¥{{ formatNumber(selectedOrder.indoorLaborCost) }}</el-descriptions-item>
          <el-descriptions-item label="燃气表安装费">¥{{ formatNumber(selectedOrder.meterInstallationCost) }}</el-descriptions-item>
          <el-descriptions-item label="燃气小件安装费">¥{{ formatNumber(selectedOrder.minorInstallationCost) }}</el-descriptions-item>
          <el-descriptions-item label="二次安装费">¥{{ formatNumber(selectedOrder.secondaryInstallationCost) }}</el-descriptions-item>
          <el-descriptions-item label="户内配件费">¥{{ formatNumber(selectedOrder.indoorFittingsCost) }}</el-descriptions-item>
          <el-descriptions-item label="合计金额">
            <span class="total-amount">¥{{ formatNumber(selectedOrder.totalCost) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedOrder.status)">{{ selectedOrder.status }}</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Download } from '@element-plus/icons-vue'

// 数据定义
interface LooseOrder {
  id: number
  serialNo: string
  projectName: string
  preMeterLaborCost: number
  indoorLaborCost: number
  meterInstallationCost: number
  minorInstallationCost: number
  secondaryInstallationCost: number
  indoorFittingsCost: number
  totalCost: number
  orderDate: string
  status: string
}

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10
})

// 弹窗控制
const detailDialogVisible = ref(false)
const selectedOrder = ref<LooseOrder | null>(null)

// 响应式数据
const orderList = ref<LooseOrder[]>([
  {
    id: 1,
    serialNo: 'LS001',
    projectName: '阳光小区1号楼',
    preMeterLaborCost: 150,
    indoorLaborCost: 200,
    meterInstallationCost: 100,
    minorInstallationCost: 80,
    secondaryInstallationCost: 50,
    indoorFittingsCost: 120,
    totalCost: 700,
    orderDate: '2024-01-15',
    status: '已完成'
  },
  {
    id: 2,
    serialNo: 'LS002',
    projectName: '花园广场2号楼',
    preMeterLaborCost: 180,
    indoorLaborCost: 220,
    meterInstallationCost: 100,
    minorInstallationCost: 90,
    secondaryInstallationCost: 60,
    indoorFittingsCost: 140,
    totalCost: 790,
    orderDate: '2024-01-14',
    status: '进行中'
  },
  {
    id: 3,
    serialNo: 'LS003',
    projectName: '商业中心A座',
    preMeterLaborCost: 200,
    indoorLaborCost: 250,
    meterInstallationCost: 120,
    minorInstallationCost: 100,
    secondaryInstallationCost: 70,
    indoorFittingsCost: 160,
    totalCost: 900,
    orderDate: '2024-01-13',
    status: '待开始'
  }
])

// 计算属性
const totalOrders = computed(() => orderList.value.length)
const totalAmount = computed(() => orderList.value.reduce((sum, order) => sum + order.totalCost, 0))
const monthlyNewOrders = computed(() => {
  const currentMonth = new Date().getMonth() + 1
  return orderList.value.filter(order => {
    const orderMonth = new Date(order.orderDate).getMonth() + 1
    return orderMonth === currentMonth
  }).length
})

// 分页计算属性
const paginatedOrderList = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return orderList.value.slice(start, end)
})

// 方法
const formatNumber = (num: number | undefined | null): string => {
  if (num === undefined || num === null) return '0.00'
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

const getStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  switch (status) {
    case '已完成':
      return 'success'
    case '进行中':
      return 'primary'
    case '待开始':
      return 'warning'
    default:
      return 'info'
  }
}

const refreshData = () => {
  ElMessage.success('数据已刷新')
}

const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

const viewOrderDetails = (order: LooseOrder) => {
  selectedOrder.value = order
  detailDialogVisible.value = true
}

// 分页处理方法
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
}

// 生命周期
onMounted(() => {
  // 页面加载时获取数据
})
</script>

<style scoped lang="scss">
.loose-order-statistics {
  padding: 20px;
  
  .overview-card {
    margin-bottom: 20px;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
  }
  
  .header-actions {
    display: flex;
    gap: 10px;
  }
  
  .overview-content {
    display: flex;
    gap: 40px;
    padding: 10px 0;
  }
  
  .overview-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 16px;
  }
  
  .label {
    color: #606266;
  }
  
  .value {
    font-weight: bold;
    color: #409EFF;
  }
  
  .amount {
    font-weight: bold;
    color: #67C23A;
  }
  
  .order-list-card {
    margin-bottom: 20px;
  }
  
  .pagination-section {
    margin-top: 20px;
    text-align: right;
  }
  
  .total-cost {
    font-weight: bold;
    color: #67C23A;
  }
  
  .total-amount {
    font-weight: bold;
    color: #67C23A;
    font-size: 16px;
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
