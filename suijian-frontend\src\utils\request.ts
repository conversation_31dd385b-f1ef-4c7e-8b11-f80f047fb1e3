import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

// 调试信息
console.log('Environment variables:', {
  VITE_APP_API_BASE_URL: import.meta.env.VITE_APP_API_BASE_URL,
  MODE: import.meta.env.MODE
})

class Request {
  private instance: AxiosInstance

  constructor() {
    this.instance = axios.create({
      // 使用环境变量中的API基础URL，如果没有则使用空字符串（依赖代理）
      baseURL: import.meta.env.VITE_APP_API_BASE_URL || '',
      timeout: 10000
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const authStore = useAuthStore()
        if (authStore.token) {
          config.headers.Authorization = `Bearer ${authStore.token}`
        }
        
        // 如果是FormData，不要设置Content-Type，让浏览器自动处理
        if (config.data instanceof FormData) {
          delete config.headers['Content-Type']
        }
        
        console.log(`Request: ${config.method?.toUpperCase()} ${config.url}`)
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        // 关键：如果是 blob，返回整个 response，否则只返回 data
        if (response.config.responseType === 'blob') {
          return response
        }
        return response.data
      },
      (error) => {
        const authStore = useAuthStore()

        if (error.response?.status === 401) {
          authStore.logout()
          window.location.href = '/login'
        }

        ElMessage.error(error.response?.data?.message || '请求失败')
        return Promise.reject(error)
      }
    )
  }

  public get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.get(url, config)
  }

  public post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.post(url, data, config)
  }

  public put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.put(url, data, config)
  }

  public delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.delete(url, config)
  }
}

export default new Request() 