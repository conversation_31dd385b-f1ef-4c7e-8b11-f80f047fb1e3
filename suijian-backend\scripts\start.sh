#!/bin/bash

# 工程管理系统后端启动脚本

echo "正在启动工程管理系统后端服务..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "错误: Node.js未安装，请先安装Node.js"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "错误: npm未安装，请先安装npm"
    exit 1
fi

# 检查环境配置文件
if [ ! -f ".env" ]; then
    echo "警告: .env文件不存在，正在从env.example复制..."
    cp env.example .env
    echo "请根据实际情况修改.env文件中的配置"
fi

# 安装依赖
echo "正在安装依赖..."
npm install

# 创建必要的目录
echo "正在创建必要的目录..."
mkdir -p logs uploads/excel uploads/pdf uploads/temp

# 启动服务
echo "正在启动服务..."
if [ "$NODE_ENV" = "production" ]; then
    echo "生产环境模式"
    npm start
else
    echo "开发环境模式"
    npm run dev
fi 