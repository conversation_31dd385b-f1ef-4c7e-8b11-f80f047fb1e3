const { body, param, query, validationResult } = require('express-validator');

/**
 * 验证工具类
 */
class Validator {
    /**
     * 处理验证结果
     */
    static handleValidationResult(req, res, next) {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: '数据验证失败',
                errors: errors.array(),
                timestamp: new Date().toISOString()
            });
        }
        next();
    }

    /**
     * 用户相关验证规则
     */
    static userValidationRules() {
        return [
            body('username')
            .isLength({ min: 3, max: 50 })
            .withMessage('用户名长度必须在3-50个字符之间')
            .matches(/^[a-zA-Z0-9_]+$/)
            .withMessage('用户名只能包含字母、数字和下划线'),
            body('password')
            .isLength({ min: 6 })
            .withMessage('密码长度不能少于6个字符'),
            body('realName')
            .notEmpty()
            .withMessage('真实姓名不能为空')
            .isLength({ max: 50 })
            .withMessage('真实姓名长度不能超过50个字符'),
            body('phone')
            .optional()
            .matches(/^1[3-9]\d{9}$/)
            .withMessage('手机号格式不正确'),
            body('email')
            .optional()
            .isEmail()
            .withMessage('邮箱格式不正确'),
            body('role')
            .optional()
            .isIn(['admin', 'manager', 'worker'])
            .withMessage('角色只能是admin、manager或worker')
        ];
    }

    /**
     * 登录验证规则
     */
    static loginValidationRules() {
        return [
            body('username')
            .isLength({ min: 3, max: 50 })
            .withMessage('用户名长度必须在3-50个字符之间')
            .matches(/^[a-zA-Z0-9_]+$/)
            .withMessage('用户名只能包含字母、数字和下划线'),
            body('password')
            .isLength({ min: 6 })
            .withMessage('密码长度不能少于6个字符')
        ];
    }

    /**
     * 物料相关验证规则
     */
    static materialValidationRules() {
        return [
            body('companyCode')
            .optional()
            .isLength({ max: 50 })
            .withMessage('公司物料编码长度不能超过50个字符'),
            body('name')
            .notEmpty()
            .withMessage('物料名称不能为空')
            .isLength({ max: 100 })
            .withMessage('物料名称长度不能超过100个字符'),
            body('category')
            .isIn(['甲料', '乙料', '商品', '辅料'])
            .withMessage('物料分类只能是甲料、乙料、商品或辅料'),
            body('unit')
            .optional()
            .isLength({ max: 20 })
            .withMessage('单位长度不能超过20个字符'),
            body('price')
            .optional()
            .isFloat({ min: 0 })
            .withMessage('价格必须是非负数'),
            body('stockQuantity')
            .optional()
            .isInt({ min: 0 })
            .withMessage('库存数量必须是非负整数'),
            body('warningQuantity')
            .optional()
            .isInt({ min: 0 })
            .withMessage('预警数量必须是非负整数')
        ];
    }

    /**
     * 甲方编码相关验证规则
     */
    static clientCodeValidationRules() {
        return [
            body('companyCode')
            .notEmpty()
            .withMessage('公司物料编码不能为空')
            .isLength({ max: 50 })
            .withMessage('公司物料编码长度不能超过50个字符'),
            body('clientCodes')
            .notEmpty()
            .withMessage('甲方编码不能为空')
            .isLength({ max: 100 })
            .withMessage('甲方编码长度不能超过100个字符'),
            body('name')
            .notEmpty()
            .withMessage('物料名称不能为空')
            .isLength({ max: 100 })
            .withMessage('物料名称长度不能超过100个字符'),
            body('category')
            .isIn(['甲料', '乙料', '商品', '辅料'])
            .withMessage('物料分类只能是甲料、乙料、商品或辅料'),
            body('unit')
            .notEmpty()
            .withMessage('单位不能为空')
            .isLength({ max: 20 })
            .withMessage('单位长度不能超过20个字符'),
            body('model')
            .optional()
            .isLength({ max: 100 })
            .withMessage('型号长度不能超过100个字符'),
            body('specification')
            .optional()
            .isLength({ max: 200 })
            .withMessage('规格长度不能超过200个字符')
        ];
    }

    /**
     * 订单相关验证规则
     */
    static orderValidationRules() {
        return [
            body('customerName')
            .notEmpty()
            .withMessage('客户姓名不能为空')
            .isLength({ max: 50 })
            .withMessage('客户姓名长度不能超过50个字符'),
            body('phone')
            .notEmpty()
            .withMessage('联系电话不能为空')
            .matches(/^1[3-9]\d{9}$/)
            .withMessage('手机号格式不正确'),
            body('orderType')
            .isIn(['一次挂表', '二次挂表', '一次安装', '二次安装', '售后', '单项工程'])
            .withMessage('订单类型不正确'),
            body('communityName')
            .optional()
            .isLength({ max: 100 })
            .withMessage('小区名称长度不能超过100个字符'),
            body('building')
            .optional()
            .isLength({ max: 50 })
            .withMessage('楼栋长度不能超过50个字符'),
            body('roomNumber')
            .optional()
            .isLength({ max: 50 })
            .withMessage('房号长度不能超过50个字符')
        ];
    }

    /**
     * 工程相关验证规则
     */
    static projectValidationRules() {
        return [
            body('projectName')
            .notEmpty()
            .withMessage('工程名称不能为空')
            .isLength({ max: 100 })
            .withMessage('工程名称长度不能超过100个字符'),
            body('projectAddress')
            .optional()
            .isLength({ max: 500 })
            .withMessage('工程地址长度不能超过500个字符'),
            body('estimatedStartDate')
            .optional()
            .isISO8601()
            .withMessage('预估开始日期格式不正确'),
            body('estimatedEndDate')
            .optional()
            .isISO8601()
            .withMessage('预估结束日期格式不正确'),
            body('contractAmount')
            .optional()
            .isFloat({ min: 0 })
            .withMessage('合同金额必须是非负数')
        ];
    }

    /**
     * 员工相关验证规则
     */
    static employeeValidationRules() {
        return [
            body('name')
            .notEmpty()
            .withMessage('员工姓名不能为空')
            .isLength({ max: 50 })
            .withMessage('员工姓名长度不能超过50个字符'),
            body('workType')
            .optional()
            .isLength({ max: 50 })
            .withMessage('工种长度不能超过50个字符'),
            body('wage')
            .optional()
            .isFloat({ min: 0 })
            .withMessage('工价必须是非负数'),
            body('phone')
            .optional()
            .matches(/^1[3-9]\d{9}$/)
            .withMessage('手机号格式不正确')
        ];
    }

    /**
     * ID参数验证规则
     */
    static idValidationRule() {
        return [
            param('id')
            .notEmpty()
            .withMessage('ID不能为空')
            .isLength({ min: 1, max: 50 })
            .withMessage('ID长度必须在1-50个字符之间')
        ];
    }

    /**
     * 分页查询验证规则
     */
    static paginationValidationRules() {
        return [
            query('page')
            .optional()
            .isInt({ min: 1 })
            .withMessage('页码必须是正整数'),
            query('pageSize')
            .optional()
            .isInt({ min: 1, max: 100 })
            .withMessage('每页数量必须在1-100之间'),
            query('search')
            .optional()
            .isLength({ max: 100 })
            .withMessage('搜索关键词长度不能超过100个字符')
        ];
    }

    /**
     * 文件上传验证规则
     */
    static fileUploadValidationRules() {
        return [
            body('file')
            .custom((value, { req }) => {
                if (!req.file) {
                    throw new Error('请选择要上传的文件');
                }
                return true;
            })
        ];
    }
}

module.exports = Validator;