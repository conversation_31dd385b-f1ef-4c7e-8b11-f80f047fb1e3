<template>
  <div class="material-records">
    <el-card class="main-card">
      <!-- 搜索条件 -->
      <div class="search-section">
        <el-form :model="searchForm" inline>
          <el-form-item label="操作类型:">
            <el-select v-model="searchForm.operationType" placeholder="请选择操作类型" style="width: 150px;">
              <el-option label="全部" value="" />
              <el-option label="甲料入库" value="in" />
              <el-option label="商品入库" value="product_in" />
              <el-option label="出库" value="out" />
              <el-option label="退库" value="return" />
              <el-option label="申请" value="apply" />
            </el-select>
          </el-form-item>
          <el-form-item label="单号:">
            <el-input v-model="searchForm.documentNumber" placeholder="请输入单号" />
          </el-form-item>
          <el-form-item label="物料编码:">
            <el-input v-model="searchForm.materialCode" placeholder="请输入物料编码" />
          </el-form-item>
          <el-form-item label="物料名称:">
            <el-input v-model="searchForm.materialName" placeholder="请输入物料名称" />
          </el-form-item>
          <el-form-item label="日期范围:">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table :data="tableData" border style="width: 100%" v-loading="loading" @sort-change="handleSortChange">
        <el-table-column prop="created_at" label="操作日期" width="160" sortable="custom">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="order_id" label="单号" width="150" sortable="custom" />
        <el-table-column prop="type" label="操作类型" width="120" sortable="custom">
          <template #default="{ row }">
            <el-tag :type="getOperationTypeTag(row.type)">
              {{ getOperationTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operator_name" label="操作员" width="100" sortable="custom" />
        <el-table-column prop="recipient_name" label="申请人" width="100" sortable="custom" />
        <el-table-column prop="purpose" label="用途" width="175" sortable="custom" />
        <el-table-column prop="remarks" label="备注" min-width="150" />
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetails(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="记录详情"
      width="800px"
      :close-on-click-modal="true"
    >
      <div v-if="currentRecord">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="记录ID">{{ currentRecord.id }}</el-descriptions-item>
          <el-descriptions-item label="操作类型">
            <el-tag :type="getOperationTypeTag(currentRecord.type)">
              {{ getOperationTypeText(currentRecord.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="单号">{{ currentRecord.order_id || '-' }}</el-descriptions-item>
          <el-descriptions-item label="操作员">{{ currentRecord.operator_name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="申请人">{{ currentRecord.recipient_name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="用途">{{ currentRecord.purpose || '-' }}</el-descriptions-item>
          <el-descriptions-item label="操作时间">{{ formatDate(currentRecord.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="备注">{{ currentRecord.remarks || '-' }}</el-descriptions-item>
        </el-descriptions>

        <!-- 物料明细 -->
        <div style="margin-top: 20px;">
          <h4>物料明细</h4>
          <el-table :data="recordItems" border style="width: 100%" v-loading="itemsLoading">
            <el-table-column prop="material_code" label="物料编码" width="150" />
            <el-table-column prop="material_name" label="物料名称" min-width="150" />
            <el-table-column prop="specification" label="规格" min-width="120" />
            <el-table-column prop="quantity" label="数量" width="100" />
            <el-table-column prop="current_quantity" label="操作前库存" width="120" />
            <el-table-column prop="unit" label="单位" width="80" />
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

// 搜索表单
const searchForm = reactive({
  materialCode: '',
  materialName: '',
  operationType: '',
  documentNumber: '',
  dateRange: []
})

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 表格数据
const tableData = ref([])

// 加载状态
const loading = ref(false)

// 排序状态
const sortState = reactive({
  prop: '',
  order: ''
})

// 详情相关
const detailDialogVisible = ref(false)
const currentRecord = ref(null)
const recordItems = ref([])
const itemsLoading = ref(false)

// 获取操作类型标签
const getOperationTypeTag = (type: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  switch (type) {
    case 'in':
      return 'success'
    case 'product_in':
      return 'success'
    case 'out':
      return 'primary'
    case 'return':
      return 'warning'
    case 'apply':
      return 'info'
    default:
      return 'info'
  }
}

// 获取操作类型文本
const getOperationTypeText = (type: string): string => {
  switch (type) {
    case 'in':
      return '甲料入库'
    case 'product_in':
      return '商品入库'
    case 'out':
      return '出库'
    case 'return':
      return '退库'
    case 'apply':
      return '申请'
    default:
      return '未知'
  }
}

// 格式化日期 - 显示北京时间
const formatDate = (timestamp: string) => {
  if (!timestamp) return ''
  
  // 创建日期对象并添加8小时转换为北京时间
  const date = new Date(timestamp)
  date.setHours(date.getHours() + 8)
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 搜索
const handleSearch = async () => {
  pagination.currentPage = 1
  await loadData()
}

// 排序处理
const handleSortChange = ({ prop, order }: { prop: string, order: string }) => {
  sortState.prop = prop
  sortState.order = order
  pagination.currentPage = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    if (key === 'dateRange') {
      (searchForm as any)[key] = []
    } else {
      (searchForm as any)[key] = ''
    }
  })
  // 重置排序状态
  sortState.prop = ''
  sortState.order = ''
  pagination.currentPage = 1
  loadData()
  ElMessage.info('搜索条件已重置')
}

// 查看详情
const viewDetails = async (row: any) => {
  try {
    currentRecord.value = row
    detailDialogVisible.value = true
    itemsLoading.value = true
    
    // 加载记录明细
    const response = await request.get(`/api/materials/records/${row.id}/items`)
    
    if (response && response.success && response.data) {
      recordItems.value = response.data || []
    } else {
      ElMessage.error('加载明细失败')
      recordItems.value = []
    }
  } catch (error) {
    console.error('加载详情失败:', error)
    ElMessage.error('加载详情失败')
    recordItems.value = []
  } finally {
    itemsLoading.value = false
  }
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params: any = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    }

    // 添加搜索条件
    if (searchForm.materialCode) {
      params.materialCode = searchForm.materialCode
    }
    if (searchForm.materialName) {
      params.materialName = searchForm.materialName
    }
    if (searchForm.operationType) {
      params.type = searchForm.operationType
    }
    if (searchForm.documentNumber) {
      params.orderId = searchForm.documentNumber
    }
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0]
      params.endDate = searchForm.dateRange[1]
    }

    // 添加排序参数
    if (sortState.prop && sortState.order) {
      // 将驼峰命名转换为下划线命名
      const fieldMapping = {
        'created_at': 'created_at',
        'order_id': 'order_id',
        'type': 'type',
        'operator_name': 'operator_name',
        'recipient_name': 'recipient_name',
        'purpose': 'purpose'
      }
      params.sortBy = fieldMapping[sortState.prop] || sortState.prop
      params.sortOrder = sortState.order === 'ascending' ? 'asc' : 'desc'
    }

    const response = await request.get('/api/materials/records', { params })
    
    if (response && response.success && response.data) {
      tableData.value = response.data.list || []
      pagination.total = response.data.pagination?.total || 0
    } else {
      console.error('API响应格式错误:', response)
      ElMessage.error('数据格式错误')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error(error.message || '加载数据失败')
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.material-records {
  padding: 0;
  margin: 0;
  
  .search-section {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .pagination-section {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
