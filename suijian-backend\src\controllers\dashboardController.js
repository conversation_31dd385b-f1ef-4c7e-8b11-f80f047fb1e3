const Response = require('../utils/response');
const { logger } = require('../utils/logger');

// 模拟数据（实际项目中应该从数据库获取）
const mockMaterials = [
    { id: 1, name: '管道', category: '甲料', stockQuantity: 100, price: 50, usedQuantity: 20, unusedQuantity: 80 },
    { id: 2, name: '阀门', category: '甲料', stockQuantity: 50, price: 100, usedQuantity: 10, unusedQuantity: 40 },
    { id: 3, name: '螺丝', category: '辅料', stockQuantity: 1000, price: 2, usedQuantity: 200, unusedQuantity: 800 },
    { id: 4, name: '燃气表', category: '商品', stockQuantity: 30, price: 500, usedQuantity: 5, unusedQuantity: 25 }
];

const mockProjects = [
    { id: 1, name: '项目A', status: 'not_started' },
    { id: 2, name: '项目B', status: 'in_progress' },
    { id: 3, name: '项目C', status: 'paused' },
    { id: 4, name: '项目D', status: 'completed' },
    { id: 5, name: '项目E', status: 'in_progress' }
];

const mockLooseOrders = [
    { id: 1, orderNo: 'LO001', status: 'pending', orderType: '一次挂表' },
    { id: 2, orderNo: 'LO002', status: 'assigned', orderType: '二次安装' },
    { id: 3, orderNo: 'LO003', status: 'completed', orderType: '售后' }
];

/**
 * 获取未使用物料统计
 */
const getUnusedMaterials = async(req, res) => {
    try {
        const unusedMaterials = mockMaterials.map(material => ({
            ...material,
            totalPrice: material.unusedQuantity * material.price
        })).sort((a, b) => b.totalPrice - a.totalPrice);

        const totalItems = unusedMaterials.length;
        const totalPrice = unusedMaterials.reduce((sum, material) => sum + material.totalPrice, 0);

        Response.success(res, {
            totalItems,
            totalPrice,
            details: unusedMaterials
        }, '获取未使用物料统计成功');

    } catch (error) {
        logger.error('获取未使用物料统计失败', error);
        Response.serverError(res, '获取未使用物料统计失败');
    }
};

/**
 * 获取已使用物料统计
 */
const getUsedMaterials = async(req, res) => {
    try {
        const usedMaterials = mockMaterials.map(material => ({
            ...material,
            totalPrice: material.usedQuantity * material.price
        })).sort((a, b) => b.totalPrice - a.totalPrice);

        const totalItems = usedMaterials.length;
        const totalPrice = usedMaterials.reduce((sum, material) => sum + material.totalPrice, 0);

        Response.success(res, {
            totalItems,
            totalPrice,
            details: usedMaterials
        }, '获取已使用物料统计成功');

    } catch (error) {
        logger.error('获取已使用物料统计失败', error);
        Response.serverError(res, '获取已使用物料统计失败');
    }
};

/**
 * 获取已领出物料统计
 */
const getIssuedMaterials = async(req, res) => {
    try {
        const issuedMaterials = mockMaterials.map(material => ({
            ...material,
            totalPrice: (material.usedQuantity + material.unusedQuantity) * material.price
        }));

        const totalItems = issuedMaterials.length;
        const totalPrice = issuedMaterials.reduce((sum, material) => sum + material.totalPrice, 0);

        Response.success(res, {
            totalItems,
            totalPrice
        }, '获取已领出物料统计成功');

    } catch (error) {
        logger.error('获取已领出物料统计失败', error);
        Response.serverError(res, '获取已领出物料统计失败');
    }
};

/**
 * 获取工程进度统计
 */
const getProjectStatus = async(req, res) => {
    try {
        const statusCounts = {
            not_started: 0,
            in_progress: 0,
            paused: 0,
            completed: 0
        };

        const projectDetails = mockProjects.map(project => {
            statusCounts[project.status]++;
            return {
                id: project.id,
                name: project.name,
                status: project.status
            };
        }).sort((a, b) => {
            const statusOrder = { 'in_progress': 1, 'paused': 2, 'not_started': 3, 'completed': 4 };
            return statusOrder[a.status] - statusOrder[b.status];
        });

        Response.success(res, {
            counts: statusCounts,
            details: projectDetails
        }, '获取工程进度统计成功');

    } catch (error) {
        logger.error('获取工程进度统计失败', error);
        Response.serverError(res, '获取工程进度统计失败');
    }
};

/**
 * 获取散单物料统计
 */
const getLooseMaterials = async(req, res) => {
    try {
        const lastBalanceTime = '2024-01-15';
        const daysSinceLastBalance = Math.floor((new Date() - new Date(lastBalanceTime)) / (1000 * 60 * 60 * 24));

        const partyMaterials = mockMaterials.filter(material => material.category === '甲料');

        const unusedPartyMaterials = partyMaterials.map(material => ({
            ...material,
            totalPrice: material.unusedQuantity * material.price
        }));

        const usedPartyMaterials = partyMaterials.map(material => ({
            ...material,
            totalPrice: material.usedQuantity * material.price
        }));

        const warehouseQuantity = partyMaterials.reduce((sum, material) => sum + material.stockQuantity, 0);
        const workerQuantity = partyMaterials.reduce((sum, material) => sum + material.usedQuantity, 0);

        Response.success(res, {
            lastBalanceTime,
            daysSinceLastBalance,
            unusedPartyMaterials: {
                totalItems: unusedPartyMaterials.length,
                totalQuantity: unusedPartyMaterials.reduce((sum, material) => sum + material.unusedQuantity, 0),
                totalPrice: unusedPartyMaterials.reduce((sum, material) => sum + material.totalPrice, 0)
            },
            usedPartyMaterials: {
                totalItems: usedPartyMaterials.length,
                totalQuantity: usedPartyMaterials.reduce((sum, material) => sum + material.usedQuantity, 0),
                totalPrice: usedPartyMaterials.reduce((sum, material) => sum + material.totalPrice, 0)
            },
            warehouseQuantity,
            workerQuantity
        }, '获取散单物料统计成功');

    } catch (error) {
        logger.error('获取散单物料统计失败', error);
        Response.serverError(res, '获取散单物料统计失败');
    }
};



/**
 * 获取销售统计
 */
const getSalesStatistics = async(req, res) => {
    try {
        const currentMonth = new Date().getMonth() + 1;
        const currentYear = new Date().getFullYear();

        // 模拟销售统计数据
        const salesStats = {
            totalSales: 150000,
            orderCount: 45,
            averagePrice: 3333.33,
            growthRate: 15.5
        };

        Response.success(res, {
            month: `${currentYear}年${currentMonth}月`,
            ...salesStats
        }, '获取销售统计成功');

    } catch (error) {
        logger.error('获取销售统计失败', error);
        Response.serverError(res, '获取销售统计失败');
    }
};

/**
 * 获取散户物料统计
 */
const getLooseMaterialsStats = async(req, res) => {
    try {
        const currentMonth = new Date().getMonth() + 1;
        const currentYear = new Date().getFullYear();

        // 模拟散户物料统计数据
        const looseMaterialsStats = {
            applicationCount: 25,
            totalAmount: 50000,
            growthRate: 8.5
        };

        Response.success(res, {
            month: `${currentYear}年${currentMonth}月`,
            ...looseMaterialsStats
        }, '获取散户物料统计成功');

    } catch (error) {
        logger.error('获取散户物料统计失败', error);
        Response.serverError(res, '获取散户物料统计失败');
    }
};

module.exports = {
    getUnusedMaterials,
    getUsedMaterials,
    getIssuedMaterials,
    getProjectStatus,
    getLooseMaterials,
    getSalesStatistics,
    getLooseMaterialsStats
};