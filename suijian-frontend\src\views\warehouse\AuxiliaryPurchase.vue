<template>
  <div class="auxiliary-purchase-container">
    <el-card class="main-card">
      <!-- 采购信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="12">
          <el-form-item label="采购单号:">
            <span>CG20240115001</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="采购日期:">
            <el-date-picker
              v-model="purchaseForm.purchaseDate"
              type="date"
              placeholder="请选择采购日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="供应商:">
            <el-input v-model="purchaseForm.supplier" placeholder="请输入供应商" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 物料列表 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <el-table :data="purchaseItems" border class="purchase-table">
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="companyCode" label="公司物料编码" width="150" />
            <el-table-column prop="name" label="物料名称" />
            <el-table-column prop="specification" label="规格" />
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column prop="quantity" label="采购数量" width="120" >
              <template #default="scope">
                <el-input-number 
                  v-model="scope.row.quantity" 
                  :min="1" 
                  controls-position="right" 
                  style="width: 100%" 
                  @change="updateLocalStorage"
                />
              </template>
            </el-table-column>
            <el-table-column prop="unitPrice" label="单价(元)" width="120">
              <template #default="scope">
                <el-input 
                  v-model="scope.row.unitPrice" 
                  placeholder="请输入单价"
                  @input="updateLocalStorage"
                />
              </template>
            </el-table-column>
            <el-table-column prop="subtotal" label="小计(元)" width="100">
              <template #default="scope">
                {{ calculateSubtotal(scope.row) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" fixed="right">
              <template #default="scope">
                <el-button type="danger" link @click="removeItem(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 15px;">
            <el-button type="primary" @click="openMaterialSelector">选择物料</el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 备注信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <el-form-item label="备注:">
            <el-input
              v-model="purchaseForm.remarks"
              type="textarea"
              :rows="2"
              placeholder="请输入备注"
              @input="updateLocalStorage"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button @click="handlePrint">打印</el-button>
        <el-button type="success" @click="handleSubmit" :loading="submitting">提交</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </el-card>
    
    <!-- 物料选择器 -->
    <MaterialSelector
      v-model="materialDialogVisible"
      title="选择辅料"
      category="辅料"
      :selected-ids="selectedMaterialIds"
      :disable-category-switch="true"
      @selection-change="handleMaterialSelectionChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import MaterialSelector from '@/components/MaterialSelector.vue'
import request from '@/utils/request'

// 本地存储键名
const STORAGE_KEY = 'auxiliary_purchase_data'

// 采购表单
const purchaseForm = reactive({
  purchaseDate: '2024-01-15',
  supplier: '',
  remarks: ''
})

// 采购明细
const purchaseItems = ref([])

// 物料选择器相关
const materialDialogVisible = ref(false)
const selectedMaterials = ref([])



// 提交状态
const submitting = ref(false)

// 计算已选物料ID列表
const selectedMaterialIds = computed(() => 
  selectedMaterials.value.map(m => m.id)
)



// 打开物料选择器
const openMaterialSelector = () => {
  materialDialogVisible.value = true
}

// 处理物料选择变化
const handleMaterialSelectionChange = (materials) => {
  console.log('选择的物料:', materials)
  selectedMaterials.value = materials
  
  // 更新采购明细列表
  purchaseItems.value = materials.map(material => ({
    ...material,
    quantity: purchaseItems.value.find(item => item.id === material.id)?.quantity || 1,
    unitPrice: purchaseItems.value.find(item => item.id === material.id)?.unitPrice || '0.00'
  }))
  
  updateLocalStorage()
}

// 计算小计
const calculateSubtotal = (row) => {
  const quantity = Number(row.quantity) || 0
  const unitPrice = Number(row.unitPrice) || 0
  return (quantity * unitPrice).toFixed(2)
}

// 删除物料
const removeItem = (index: number) => {
  const removedItem = purchaseItems.value[index]
  purchaseItems.value.splice(index, 1)
  
  // 从选中的物料中移除
  selectedMaterials.value = selectedMaterials.value.filter(item => item.id !== removedItem.id)
  
  ElMessage.success('删除成功')
  updateLocalStorage()
}

// 更新本地存储
const updateLocalStorage = () => {
  const data = {
    purchaseForm: { ...purchaseForm },
    purchaseItems: [...purchaseItems.value],
    selectedMaterials: [...selectedMaterials.value],
    timestamp: Date.now()
  }
  localStorage.setItem(STORAGE_KEY, JSON.stringify(data))
  console.log('数据已保存到本地存储')
}

// 从本地存储加载数据
const loadFromLocalStorage = () => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    if (stored) {
      const data = JSON.parse(stored)
      
      // 恢复表单数据
      Object.assign(purchaseForm, data.purchaseForm || {})
      
      // 恢复采购明细
      purchaseItems.value = data.purchaseItems || []
      
      // 恢复选中的物料
      selectedMaterials.value = data.selectedMaterials || []
      
      console.log('从本地存储加载数据成功')
    }
  } catch (error) {
    console.error('从本地存储加载数据失败:', error)
  }
}

// 清空本地存储
const clearLocalStorage = () => {
  localStorage.removeItem(STORAGE_KEY)
  console.log('本地存储已清空')
}

// 清空当前数据
const clearCurrentData = () => {
  // 清空表单数据
  purchaseForm.purchaseDate = new Date().toISOString().split('T')[0]
  purchaseForm.supplier = ''
  purchaseForm.remarks = ''
  
  // 清空物料列表
  purchaseItems.value = []
  selectedMaterials.value = []
  
  // 清空本地存储
  clearLocalStorage()
}

// 监听表单变化，自动保存
watch(purchaseForm, () => {
  updateLocalStorage()
}, { deep: true })

// 打印
const handlePrint = () => {
  ElMessage.success('开始打印')
}

// 提交
const handleSubmit = async () => {
  if (purchaseItems.value.length === 0) {
    ElMessage.warning('请至少选择一个物料')
    return
  }
  

  
  if (!purchaseForm.supplier) {
    ElMessage.warning('请输入供应商')
    return
  }
  
  submitting.value = true
  
  try {
    // 准备提交数据
    const submitData = {
      purchaseDate: purchaseForm.purchaseDate,
      supplier: purchaseForm.supplier,
      remarks: purchaseForm.remarks,
      items: purchaseItems.value.map(item => ({
        materialId: item.id,
        quantity: item.quantity,
        unitPrice: Number(item.unitPrice) || 0
      }))
    }
    
    console.log('提交数据:', submitData)
    
    // 调用API提交数据
    // 注意：如果后端API还未实现，这里会失败，但会显示错误信息
    const response = await request.post('/api/auxiliary-purchases', submitData)
    
    if (response && response.success) {
      ElMessage.success('提交成功')
      console.log('提交成功:', response)
      
      // 提交成功后清空当前数据
      clearCurrentData()
    } else {
      ElMessage.error(response?.message || '提交失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    
    // 如果是API不存在的情况，显示开发中的提示
    if (error.response?.status === 404) {
      ElMessage.warning('辅料采购API正在开发中，数据已保存到本地')
      // 模拟提交成功，清空数据
      clearCurrentData()
    } else {
      ElMessage.error('提交失败，请重试')
    }
  } finally {
    submitting.value = false
  }
}

// 取消
const handleCancel = () => {
  ElMessage.info('已取消')
  // 可以选择是否清空本地存储
  // clearLocalStorage()
}

// 组件挂载时加载数据
onMounted(async () => {
  // 加载本地存储的数据
  loadFromLocalStorage()
})
</script>

<style lang="scss" scoped>
.auxiliary-purchase-container {
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    .form-section {
      margin-bottom: 20px;
      
      .el-form-item {
        margin-bottom: 18px;
      }
    }
    
    .purchase-table {
      margin-top: 10px;
    }
    
    .form-actions {
      text-align: center;
      padding: 20px 0;
      
      .el-button {
        margin: 0 10px;
        padding: 12px 30px;
      }
    }
  }
}
</style>