# 物料记录明细表 current_quantity 字段说明

## 字段概述

`current_quantity` 字段用于记录物料出入库操作时的当前库存数量，提供历史库存快照功能。

## 字段定义

```sql
current_quantity INTEGER NOT NULL
```

- **类型**: INTEGER
- **约束**: NOT NULL
- **说明**: 记录操作时的当前库存数量

## 字段用途

### 1. 历史库存追踪
- 记录每次操作时的库存状态
- 便于追溯物料库存变化历史
- 支持库存审计和问题排查

### 2. 数据完整性验证
- 可以验证库存计算的准确性
- 发现库存异常时便于定位问题
- 提供数据校验的依据

### 3. 报表和分析
- 生成库存变化趋势报表
- 分析物料使用频率和模式
- 支持库存预警和优化

## 数据填充逻辑

### 入库操作
```javascript
// 获取操作前的库存数量
const currentStockSql = 'SELECT stock_quantity FROM materials WHERE id = ?';
const currentStockResult = await query(currentStockSql, [item.id]);
const currentQuantity = currentStockResult[0] && currentStockResult[0].stock_quantity ? currentStockResult[0].stock_quantity : 0;

// 记录操作时的库存数量
await execute(
    `INSERT INTO material_record_items (id, record_id, material_id, quantity, current_quantity, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))`,
    [generatePrefixedId('mri'), recordId, item.id, parseInt(item.quantity) || 0, currentQuantity]
);
```

### 出库操作
```javascript
// 获取操作前的库存数量
const currentStockSql = 'SELECT stock_quantity FROM materials WHERE id = ?';
const currentStockResult = await query(currentStockSql, [item.id]);
const currentQuantity = currentStockResult[0] && currentStockResult[0].stock_quantity ? currentStockResult[0].stock_quantity : 0;

// 记录操作时的库存数量
await execute(
    `INSERT INTO material_record_items (id, record_id, material_id, quantity, current_quantity, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))`,
    [generatePrefixedId('mri'), recordId, item.id, parseInt(item.quantity) || 0, currentQuantity]
);
```

## 查询示例

### 1. 查询物料库存变化历史
```sql
SELECT 
    mr.created_at,
    mr.type,
    mri.quantity as operation_quantity,
    mri.current_quantity as stock_before,
    (mri.current_quantity + mri.quantity) as stock_after,
    m.name as material_name
FROM material_records mr
JOIN material_record_items mri ON mr.id = mri.record_id
JOIN materials m ON mri.material_id = m.id
WHERE mri.material_id = 'material-xxx'
ORDER BY mr.created_at DESC;
```

### 2. 查询库存异常记录
```sql
SELECT 
    mr.created_at,
    mr.type,
    mri.quantity,
    mri.current_quantity,
    m.name as material_name,
    CASE 
        WHEN mri.current_quantity < 0 THEN '库存不足'
        WHEN mri.current_quantity > 10000 THEN '库存异常'
        ELSE '正常'
    END as status
FROM material_records mr
JOIN material_record_items mri ON mr.id = mri.record_id
JOIN materials m ON mri.material_id = m.id
WHERE mri.current_quantity < 0 OR mri.current_quantity > 10000
ORDER BY mr.created_at DESC;
```

### 3. 生成库存变化报表
```sql
SELECT 
    DATE(mr.created_at) as operation_date,
    m.name as material_name,
    COUNT(*) as operation_count,
    SUM(CASE WHEN mr.type = 'in' THEN mri.quantity ELSE 0 END) as total_in,
    SUM(CASE WHEN mr.type = 'out' THEN mri.quantity ELSE 0 END) as total_out,
    AVG(mri.current_quantity) as avg_stock_before
FROM material_records mr
JOIN material_record_items mri ON mr.id = mri.record_id
JOIN materials m ON mri.material_id = m.id
WHERE mr.created_at >= date('now', '-30 days')
GROUP BY DATE(mr.created_at), m.id, m.name
ORDER BY operation_date DESC;
```

## 数据迁移说明

对于历史数据迁移，`current_quantity` 字段的处理策略：

1. **入库记录**: 使用 `quantity` 作为 `current_quantity`（假设入库前库存为0）
2. **出库记录**: 使用 `quantity` 作为 `current_quantity`（假设出库前库存等于出库数量）
3. **其他记录**: 使用 `quantity` 作为默认值

```sql
-- 迁移脚本示例
INSERT INTO material_record_items (id, record_id, material_id, quantity, current_quantity, unit_price, total_amount, created_at)
SELECT 
    generatePrefixedId('mri'),
    id as record_id,
    material_id,
    quantity,
    quantity as current_quantity, -- 对于历史数据，使用quantity作为current_quantity
    unit_price,
    total_amount,
    created_at
FROM material_records_old;
```

## 注意事项

1. **数据一致性**: 确保 `current_quantity` 反映操作时的真实库存状态
2. **性能考虑**: 在批量操作时，考虑使用事务确保数据一致性
3. **历史数据**: 对于历史数据迁移，需要根据实际情况调整 `current_quantity` 的计算逻辑
4. **数据验证**: 定期验证 `current_quantity` 的准确性，确保数据质量

## 应用场景

1. **库存审计**: 追踪物料库存变化，支持审计需求
2. **问题排查**: 当库存出现异常时，可以快速定位问题
3. **报表分析**: 生成库存变化趋势和统计报表
4. **数据校验**: 验证库存计算的准确性和一致性 