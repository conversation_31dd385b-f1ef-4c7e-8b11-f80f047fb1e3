# 大文件上传配置说明

## 概述

本系统已配置支持最大200MB的Excel文件上传。以下是相关的配置说明和注意事项。

## 配置项

### 1. 环境变量配置

在 `.env` 文件中设置以下配置：

```bash
# 文件上传配置
MAX_FILE_SIZE=209715200  # 200MB (200 * 1024 * 1024)
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,xlsx,xls,doc,docx
```

### 2. 后端配置

#### Express应用配置 (`src/app.js`)
```javascript
// 解析JSON请求体
app.use(express.json({ limit: '200mb' }));
app.use(express.urlencoded({ extended: true, limit: '200mb' }));
```

#### Multer中间件配置 (`src/middleware/upload.js`)
```javascript
const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: parseInt(process.env.MAX_FILE_SIZE) || 200 * 1024 * 1024, // 默认200MB
        files: 1
    }
});
```

### 3. 前端配置

#### 请求超时配置 (`src/utils/request.ts`)
```typescript
this.instance = axios.create({
    baseURL: import.meta.env.VITE_APP_API_BASE_URL || '',
    timeout: 300000, // 5分钟超时，支持大文件上传
    headers: {
        'Content-Type': 'application/json'
    }
})
```

#### 文件大小验证 (`src/views/looseOrders/OrderList.vue`)
```javascript
// 验证文件大小（最大200MB）
if (file.size > 200 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过200MB')
    return
}
```

## 服务器配置

### 1. Nginx配置

如果使用Nginx作为反向代理，需要配置：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 客户端请求体大小限制
    client_max_body_size 200M;

    # 代理超时设置
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;

    location /api {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 2. Apache配置

如果使用Apache，在 `.htaccess` 文件中添加：

```apache
# 文件上传大小限制
php_value upload_max_filesize 200M
php_value post_max_size 200M
php_value max_execution_time 300
php_value max_input_time 300
```

### 3. Node.js进程配置

对于大文件上传，建议增加Node.js的内存限制：

```bash
# 启动时增加内存限制
node --max-old-space-size=4096 src/app.js

# 或在package.json中配置
{
  "scripts": {
    "start": "node --max-old-space-size=4096 src/app.js",
    "dev": "nodemon --max-old-space-size=4096 src/app.js"
  }
}
```

## 性能优化建议

### 1. 流式处理

对于大文件，建议使用流式处理：

```javascript
// 在控制器中使用流式处理
const processLargeFile = async (filePath) => {
    const stream = fs.createReadStream(filePath);
    // 使用流式处理逻辑
};
```

### 2. 内存管理

- 及时清理临时文件
- 使用流式处理避免内存溢出
- 分批处理大量数据

### 3. 进度显示

为提升用户体验，建议添加上传进度显示：

```javascript
// 前端进度显示
const uploadProgress = (progressEvent) => {
    const percentCompleted = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
    );
    console.log('上传进度:', percentCompleted + '%');
};
```

## 错误处理

### 1. 常见错误

- `LIMIT_FILE_SIZE`: 文件大小超出限制
- `LIMIT_FILE_COUNT`: 文件数量超出限制
- `LIMIT_UNEXPECTED_FILE`: 不支持的文件类型

### 2. 错误处理中间件

```javascript
const handleUploadError = (error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return Response.error(res, '文件大小超出限制（最大200MB）', 400);
        }
        // 其他错误处理...
    }
    next(error);
};
```

## 监控和日志

### 1. 上传日志

记录文件上传的详细信息：

```javascript
logger.info('文件上传', {
    fileName: file.originalname,
    fileSize: file.size,
    uploadTime: new Date(),
    userId: req.user.id
});
```

### 2. 性能监控

监控上传性能指标：

- 上传时间
- 文件大小
- 成功率
- 错误率

## 安全考虑

### 1. 文件类型验证

严格验证文件类型，防止恶意文件上传：

```javascript
const allowedTypes = ['xlsx', 'xls'];
const fileExt = path.extname(file.originalname).toLowerCase().substring(1);

if (!allowedTypes.includes(fileExt)) {
    return Response.error(res, '不支持的文件类型', 400);
}
```

### 2. 文件内容验证

验证Excel文件的内容格式：

```javascript
// 验证Excel文件格式
const validateExcelFile = (filePath) => {
    try {
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(filePath);
        return true;
    } catch (error) {
        return false;
    }
};
```

### 3. 临时文件清理

定期清理临时文件：

```javascript
// 清理超过24小时的临时文件
const cleanupTempFiles = () => {
    const tempDir = path.join(uploadDir, 'temp');
    const files = fs.readdirSync(tempDir);
    
    files.forEach(file => {
        const filePath = path.join(tempDir, file);
        const stats = fs.statSync(filePath);
        const hoursOld = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60);
        
        if (hoursOld > 24) {
            fs.unlinkSync(filePath);
        }
    });
};
```

## 测试

### 1. 大文件上传测试

```bash
# 创建测试文件
dd if=/dev/zero of=test_200mb.xlsx bs=1M count=200

# 测试上传
curl -X POST \
  -H "Authorization: Bearer your-token" \
  -F "file=@test_200mb.xlsx" \
  http://localhost:3000/api/loose-orders/import
```

### 2. 性能测试

使用工具如Apache Bench进行性能测试：

```bash
ab -n 10 -c 1 -p test_file.txt -T "multipart/form-data" \
  http://localhost:3000/api/loose-orders/import
```

## 故障排除

### 1. 上传失败

- 检查文件大小是否超出限制
- 检查网络连接
- 检查服务器磁盘空间
- 查看错误日志

### 2. 超时问题

- 增加超时时间配置
- 检查网络带宽
- 优化文件处理逻辑

### 3. 内存问题

- 增加Node.js内存限制
- 使用流式处理
- 分批处理数据

## 联系支持

如遇到问题，请联系开发团队或查看相关日志文件。 