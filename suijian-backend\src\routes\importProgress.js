const express = require('express');
const router = express.Router();
const importProgressController = require('../controllers/importProgressController');
const { authMiddleware } = require('../middleware/auth');

/**
 * 通用导入进度查询路由
 * 
 * 路由说明：
 * GET /api/import-progress/:taskId - 获取指定任务的导入进度
 * GET /api/import-progress/tasks - 获取所有导入任务列表
 * DELETE /api/import-progress/tasks/:taskId - 删除指定导入任务
 * GET /api/import-progress/stats - 获取导入任务统计信息
 * POST /api/import-progress/cleanup - 清理所有已完成的任务
 */

// 获取导入进度
router.get('/:taskId', authMiddleware, importProgressController.getImportProgress);

// 获取所有导入任务
router.get('/tasks', authMiddleware, importProgressController.getAllImportTasks);

// 删除导入任务
router.delete('/tasks/:taskId', authMiddleware, importProgressController.deleteImportTask);

// 获取导入统计信息
router.get('/stats', authMiddleware, importProgressController.getImportStats);

// 清理所有已完成的任务
router.post('/cleanup', authMiddleware, importProgressController.cleanupAllTasks);

module.exports = router;