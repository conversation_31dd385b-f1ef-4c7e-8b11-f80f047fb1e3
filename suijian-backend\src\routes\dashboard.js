const express = require('express');
const router = express.Router();
const dashboardController = require('../controllers/dashboardController');
const { authMiddleware } = require('../middleware/auth');

// 所有仪表板路由都需要认证
router.use(authMiddleware);

// 获取未使用物料统计
router.get('/materials/unused', dashboardController.getUnusedMaterials);

// 获取已使用物料统计
router.get('/materials/used', dashboardController.getUsedMaterials);

// 获取已领出物料统计
router.get('/materials/issued', dashboardController.getIssuedMaterials);

// 获取工程进度统计
router.get('/projects/status', dashboardController.getProjectStatus);

// 获取散单物料统计
router.get('/loose-materials', dashboardController.getLooseMaterials);



// 获取销售统计
router.get('/sales/statistics', dashboardController.getSalesStatistics);

// 获取散户物料统计
router.get('/loose-materials/stats', dashboardController.getLooseMaterialsStats);

module.exports = router;