# 工程管理系统 - 轻量级后端架构设计

## 1. 项目概述

### 1.1 项目简介
工程管理系统是一个综合性的工程管理平台，主要用于管理工程项目的物料、订单、员工、财务等各个方面。系统采用前后端分离架构，后端提供RESTful API接口服务。

### 1.2 轻量级技术栈选择
- **开发语言**：Node.js 18.x
- **Web框架**：Express.js 4.x
- **数据库**：SQLite 3.x（开发）/ MySQL 8.0（生产）
- **ORM**：Sequelize 6.x
- **认证**：JWT + bcrypt
- **API文档**：Swagger UI Express
- **文件上传**：Multer
- **日志**：Winston
- **测试**：Jest + Supertest
- **部署**：PM2 / Docker

## 2. 系统架构设计

### 2.1 整体架构
```
┌─────────────────────────────────────────────────────────────────┐
│                        前端应用层                                │
│                    Vue3 + TypeScript                           │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        轻量级后端                                │
│                    Node.js + Express                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  用户模块   │ │  订单模块   │ │  仓库模块   │ │ 文件模块│ │
│  │ UserModule  │ │OrderModule  │ │WarehouseMod │ │FileMod  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        数据层                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   SQLite    │ │   MySQL     │ │   文件系统   │ │ 本地存储│ │
│  │  开发环境   │ │  生产环境   │ │   文件存储   │ │ 缓存    │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2 项目结构
```
engineering-management-backend/
├── src/
│   ├── app.js                 # 应用入口
│   ├── config/
│   │   ├── database.js        # 数据库配置
│   │   ├── auth.js           # 认证配置
│   │   └── upload.js         # 文件上传配置
│   ├── models/               # 数据模型
│   │   ├── User.js
│   │   ├── Material.js
│   │   ├── Order.js
│   │   └── Project.js
│   ├── routes/               # 路由模块
│   │   ├── auth.js          # 认证路由
│   │   ├── users.js         # 用户路由
│   │   ├── materials.js     # 物料路由
│   │   ├── orders.js        # 订单路由
│   │   └── projects.js      # 项目路由
│   ├── controllers/          # 控制器
│   │   ├── authController.js
│   │   ├── userController.js
│   │   ├── materialController.js
│   │   └── orderController.js
│   ├── middleware/           # 中间件
│   │   ├── auth.js          # 认证中间件
│   │   ├── upload.js        # 文件上传中间件
│   │   └── validation.js    # 数据验证中间件
│   ├── utils/               # 工具函数
│   │   ├── logger.js        # 日志工具
│   │   ├── response.js      # 响应工具
│   │   └── validator.js     # 验证工具
│   └── uploads/             # 文件上传目录
├── tests/                   # 测试文件
├── docs/                    # API文档
├── package.json
├── .env.example
└── README.md
```

## 3. 数据库设计

### 3.1 数据库选择
- **开发环境**：SQLite（轻量级，无需安装）
- **生产环境**：MySQL 8.0（性能更好）

### 3.2 核心数据表设计

#### 3.2.1 用户管理模块
```sql
-- 用户表
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    role ENUM('admin', 'manager', 'worker') DEFAULT 'worker',
    status INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 用户会话表
CREATE TABLE user_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.2.2 仓库管理模块
```sql
-- 物料表
CREATE TABLE materials (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    model VARCHAR(100),
    specification VARCHAR(200),
    unit VARCHAR(20),
    category VARCHAR(20) NOT NULL, -- '甲料', '商品', '辅料'
    price DECIMAL(10,2),
    stock_quantity INTEGER DEFAULT 0,
    warning_quantity INTEGER DEFAULT 0,
    status INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 物料出入库记录表
CREATE TABLE material_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    material_id INTEGER NOT NULL,
    type VARCHAR(20) NOT NULL, -- 'in', 'out'
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2),
    total_amount DECIMAL(10,2),
    operator_id INTEGER NOT NULL,
    order_id INTEGER,
    remarks TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.2.3 订单管理模块
```sql
-- 散户订单表
CREATE TABLE loose_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_no VARCHAR(50) UNIQUE NOT NULL,
    customer_name VARCHAR(50) NOT NULL,
    customer_code VARCHAR(50),
    community_name VARCHAR(100),
    building VARCHAR(50),
    room_number VARCHAR(50),
    phone VARCHAR(20),
    contact_person VARCHAR(50),
    order_type VARCHAR(50), -- '一次挂表', '二次挂表', '一次安装', '二次安装', '售后', '单项工程'
    project_name VARCHAR(100),
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'assigned', 'in_progress', 'completed', 'paused'
    installation_date DATE,
    assigned_worker_id INTEGER,
    remarks TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 工程订单表
CREATE TABLE projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_no VARCHAR(50) UNIQUE NOT NULL,
    project_name VARCHAR(100) NOT NULL,
    project_address TEXT,
    status VARCHAR(20) DEFAULT 'not_started', -- 'not_started', 'in_progress', 'paused', 'completed'
    estimated_start_date DATE,
    estimated_end_date DATE,
    actual_start_date DATE,
    actual_end_date DATE,
    contract_amount DECIMAL(12,2),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 4. 核心功能模块

### 4.1 用户认证模块
```javascript
// routes/auth.js
const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');

router.post('/login', authController.login);
router.post('/register', authController.register);
router.post('/logout', authController.logout);
router.get('/profile', authMiddleware, authController.getProfile);

module.exports = router;
```

### 4.2 物料管理模块
```javascript
// routes/materials.js
const express = require('express');
const router = express.Router();
const materialController = require('../controllers/materialController');
const authMiddleware = require('../middleware/auth');

router.get('/', authMiddleware, materialController.getMaterials);
router.post('/', authMiddleware, materialController.createMaterial);
router.put('/:id', authMiddleware, materialController.updateMaterial);
router.delete('/:id', authMiddleware, materialController.deleteMaterial);
router.post('/:id/stock-in', authMiddleware, materialController.stockIn);
router.post('/:id/stock-out', authMiddleware, materialController.stockOut);

module.exports = router;
```

### 4.3 订单管理模块
```javascript
// routes/orders.js
const express = require('express');
const router = express.Router();
const orderController = require('../controllers/orderController');
const authMiddleware = require('../middleware/auth');

router.get('/loose', authMiddleware, orderController.getLooseOrders);
router.post('/loose', authMiddleware, orderController.createLooseOrder);
router.put('/loose/:id', authMiddleware, orderController.updateLooseOrder);
router.post('/loose/:id/assign', authMiddleware, orderController.assignOrder);

module.exports = router;
```

## 5. 技术实现细节

### 5.1 认证机制
```javascript
// middleware/auth.js
const jwt = require('jsonwebtoken');

const authMiddleware = (req, res, next) => {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
        return res.status(401).json({ message: '访问被拒绝，需要token' });
    }
    
    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        req.user = decoded;
        next();
    } catch (error) {
        res.status(401).json({ message: '无效的token' });
    }
};

module.exports = authMiddleware;
```

### 5.2 数据库连接
```javascript
// config/database.js
const { Sequelize } = require('sequelize');

const sequelize = new Sequelize({
    dialect: process.env.NODE_ENV === 'production' ? 'mysql' : 'sqlite',
    host: process.env.DB_HOST,
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    storage: process.env.NODE_ENV === 'production' ? undefined : './database/suijian.db',
    logging: false
});

module.exports = sequelize;
```

### 5.3 响应格式统一
```javascript
// utils/response.js
class Response {
    static success(res, data, message = '操作成功') {
        res.json({
            success: true,
            message,
            data
        });
    }
    
    static error(res, message = '操作失败', status = 400) {
        res.status(status).json({
            success: false,
            message
        });
    }
}

module.exports = Response;
```

## 6. 部署方案

### 6.1 开发环境
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 运行测试
npm test
```

### 6.2 生产环境
```bash
# 使用PM2部署
npm install -g pm2
pm2 start ecosystem.config.js

# 或使用Docker部署
docker build -t engineering-backend .
docker run -p 3000:3000 engineering-backend
```

### 6.3 Docker配置
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

CMD ["npm", "start"]
```

## 7. 性能优化

### 7.1 数据库优化
- 合理使用索引
- 查询优化
- 连接池配置

### 7.2 应用优化
- 静态文件缓存
- 响应压缩
- 内存使用优化

### 7.3 监控
- 使用PM2监控
- 日志记录
- 错误处理

## 8. 安全考虑

### 8.1 输入验证
```javascript
// middleware/validation.js
const { body, validationResult } = require('express-validator');

const validateMaterial = [
    body('name').notEmpty().withMessage('物料名称不能为空'),
    body('category').isIn(['甲料', '商品', '辅料']).withMessage('无效的物料类别'),
    (req, res, next) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        next();
    }
];
```

### 8.2 安全中间件
```javascript
// app.js
const helmet = require('helmet');
const cors = require('cors');
const rateLimit = require('express-rate-limit');

app.use(helmet());
app.use(cors());
app.use(rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100 // 限制每个IP 15分钟内最多100个请求
}));
```

## 9. 优势对比

### 9.1 轻量级架构优势
- **快速开发**：Node.js开发效率高
- **资源占用少**：相比Java应用占用更少内存
- **部署简单**：单文件部署，无需复杂配置
- **学习成本低**：JavaScript生态丰富
- **热重载**：开发时自动重启

### 9.2 适用场景
- 中小型项目
- 快速原型开发
- 资源受限环境
- 团队JavaScript技能较强

## 10. 项目时间规划

### 第一阶段（1周）：基础搭建
- 项目初始化
- 数据库设计
- 基础路由搭建
- 认证系统实现

### 第二阶段（2周）：核心功能
- 用户管理模块
- 物料管理模块
- 订单管理模块
- 基础API开发

### 第三阶段（1周）：功能完善
- 文件上传功能
- 数据统计功能
- 系统优化
- 测试完善

### 第四阶段（1周）：部署上线
- 生产环境配置
- 性能优化
- 安全加固
- 部署上线

## 11. 总结

这个轻量级架构相比原来的Spring Boot微服务架构有以下优势：

1. **技术栈简化**：从Java + Spring Boot + 多个微服务简化为Node.js + Express
2. **部署简单**：单应用部署，无需复杂的容器编排
3. **开发效率高**：JavaScript开发，前后端技术栈统一
4. **资源占用少**：相比Java应用占用更少系统资源
5. **维护成本低**：代码量少，逻辑清晰，易于维护

适合中小型项目快速开发和部署。 