# 领料申请记录功能说明

## 功能概述

领料申请现在会同时保存到两个地方：
1. **申请专用表**：`material_applications` 和 `material_application_items`
2. **统一记录表**：`material_records` 和 `material_record_items`

这样可以实现统一的物料出入库记录管理，便于查询和统计。

## 数据流程

### 1. 创建申请
```
用户提交申请 → 保存到申请表 → 同时保存到物料记录表(type='apply')
```

### 2. 审批申请
```
申请被批准 → 更新申请状态 → 创建出库记录(type='out') → 更新库存
```

### 3. 删除申请
```
删除申请 → 删除申请表数据 → 删除相关物料记录
```

## 表结构关系

### 申请表结构
```sql
-- 申请主表
material_applications (
    id, apply_no, apply_date, applicant_id, receiver_id, 
    purpose, order_no, status, remarks, created_at, updated_at
)

-- 申请明细表
material_application_items (
    id, application_id, material_id, quantity, remarks, created_at
)
```

### 物料记录表结构
```sql
-- 物料记录主表
material_records (
    id, type, operator_id, order_id, purpose, remarks, created_at
)

-- 物料记录明细表
material_record_items (
    id, record_id, material_id, quantity, current_quantity, created_at
)
```

## 记录类型

### 1. 申请记录 (type='apply')
- **用途**：记录物料申请信息
- **操作员**：申请人
- **关联订单**：申请ID
- **库存影响**：无（申请时不影响库存）

### 2. 出库记录 (type='out')
- **用途**：记录申请批准后的出库信息
- **操作员**：申请人
- **关联订单**：申请ID
- **库存影响**：减少库存

## API接口

### 1. 创建申请
```http
POST /api/material-applications
```
- 同时保存到申请表和物料记录表

### 2. 更新申请状态
```http
PUT /api/material-applications/:id/status
```
- 当状态为 `approved` 时，自动创建出库记录并更新库存

### 3. 获取申请记录
```http
GET /api/material-applications/records/list?type=apply&page=1&pageSize=20
```
- 从物料记录表中查询申请记录

## 查询示例

### 1. 查询所有申请记录
```sql
SELECT 
    mr.*,
    e.name as operator_name,
    ma.apply_no,
    ma.apply_date,
    ma.status as application_status
FROM material_records mr
LEFT JOIN employees e ON mr.operator_id = e.id
LEFT JOIN material_applications ma ON mr.order_id = ma.id
WHERE mr.type = 'apply'
ORDER BY mr.created_at DESC;
```

### 2. 查询申请记录及其物料明细
```sql
SELECT 
    mr.id,
    mr.type,
    mr.created_at,
    ma.apply_no,
    ma.status as application_status,
    mri.material_id,
    mri.quantity,
    mri.current_quantity,
    m.name as material_name
FROM material_records mr
JOIN material_application_items mai ON mr.order_id = mai.application_id
JOIN material_record_items mri ON mr.id = mri.record_id
JOIN materials m ON mri.material_id = m.id
LEFT JOIN material_applications ma ON mr.order_id = ma.id
WHERE mr.type = 'apply'
ORDER BY mr.created_at DESC;
```

### 3. 查询出库记录
```sql
SELECT 
    mr.*,
    e.name as operator_name,
    ma.apply_no,
    ma.apply_date
FROM material_records mr
LEFT JOIN employees e ON mr.operator_id = e.id
LEFT JOIN material_applications ma ON mr.order_id = ma.id
WHERE mr.type = 'out'
ORDER BY mr.created_at DESC;
```

## 数据一致性

### 1. 事务处理
- 所有操作都使用数据库事务确保数据一致性
- 如果任何步骤失败，整个操作都会回滚

### 2. 库存检查
- 申请批准时会检查库存是否足够
- 库存不足时会抛出错误并回滚事务

### 3. 记录完整性
- 申请记录和物料记录通过 `order_id` 关联
- 删除申请时会同时删除相关记录

## 使用场景

### 1. 申请管理
- 查看所有申请记录
- 按状态筛选申请
- 按时间范围查询

### 2. 库存追踪
- 查看物料的申请历史
- 追踪库存变化
- 分析物料使用情况

### 3. 报表统计
- 申请统计报表
- 出库统计报表
- 物料使用分析

## 注意事项

1. **数据冗余**：申请信息会同时保存在两个地方，确保数据完整性
2. **性能考虑**：查询时可以根据需要选择从哪个表查询
3. **数据同步**：删除申请时会同时删除相关记录
4. **库存管理**：申请时不影响库存，批准时才减少库存 