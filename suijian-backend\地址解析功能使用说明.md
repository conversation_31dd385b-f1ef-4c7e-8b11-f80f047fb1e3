# 地址解析功能使用说明

## 概述

地址解析功能用于从Excel导入的地址中自动提取小区名称、楼栋号和房号，并保存到 `loose_orders` 表的相应字段中。

## 功能特性

### 1. 智能地址解析
- 自动识别小区名称（支持数据库匹配和关键词匹配）
- 提取楼栋号（支持多种格式：数字+栋、字母+数字+栋等）
- 提取房号（支持多种格式：数字+室、楼层-房间等）

### 2. 小区数据库管理
- 支持小区信息的增删改查
- 支持小区别名管理
- 支持按区域、城市、省份筛选
- 自动缓存机制，提高解析性能

### 3. 多种地址格式支持
- 标准格式：`广东省清远市清城区阳光花园1栋101室`
- 简化格式：`阳光花园1栋101`
- 复杂格式：`广东省清远市清城区阳光花园A栋2楼201室`
- 特殊格式：`阳光花园1-101`

## 数据库结构

### communities 表
```sql
CREATE TABLE communities (
    id VARCHAR(36) PRIMARY KEY,                    -- 小区ID
    name VARCHAR(100) NOT NULL,                    -- 小区名称
    alias VARCHAR(200),                            -- 小区别名（多个别名用逗号分隔）
    district VARCHAR(50),                          -- 所属区域
    city VARCHAR(50),                              -- 所属城市
    province VARCHAR(50),                          -- 所属省份
    address TEXT,                                  -- 小区详细地址
    status INTEGER DEFAULT 1,                      -- 小区状态
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);
```

### loose_orders 表新增字段
- `community_name`: 小区名称（从地址解析得出）
- `building`: 楼栋号（从地址解析得出）
- `room_number`: 房号（从地址解析得出）

## API接口

### 小区管理接口

#### 1. 获取小区列表
```
GET /api/communities
参数：
- page: 页码（默认1）
- pageSize: 每页数量（默认20）
- name: 小区名称（模糊搜索）
- district: 区域（模糊搜索）
- city: 城市（模糊搜索）
- province: 省份（模糊搜索）
```

#### 2. 创建小区
```
POST /api/communities
参数：
{
    "name": "小区名称",
    "alias": "别名1,别名2,别名3",
    "district": "清城区",
    "city": "清远市",
    "province": "广东省",
    "address": "详细地址"
}
```

#### 3. 更新小区
```
PUT /api/communities/:id
参数：同创建小区
```

#### 4. 删除小区
```
DELETE /api/communities/:id
注意：如果小区已被订单使用，无法删除
```

#### 5. 获取小区详情
```
GET /api/communities/:id
```

#### 6. 测试地址解析
```
POST /api/communities/test-parse
参数：
{
    "address": "要解析的地址"
}
```

#### 7. 批量导入小区
```
POST /api/communities/import
参数：
{
    "communities": [
        {
            "name": "小区名称",
            "alias": "别名",
            "district": "区域",
            "city": "城市",
            "province": "省份",
            "address": "详细地址"
        }
    ]
}
```

## Excel导入流程

### 1. 导入前的准备
- 确保小区数据库中有相关小区信息
- 可以通过小区管理接口添加小区信息
- 支持批量导入小区数据

### 2. Excel文件要求
- 必须包含"地址"列
- 地址格式建议：`广东省清远市清城区阳光花园1栋101室`
- 支持多种地址格式

### 3. 导入过程
1. 上传Excel文件
2. 系统自动识别地址列
3. 逐行解析地址，提取小区名称、楼栋号、房号
4. 将解析结果保存到数据库
5. 返回导入结果统计

### 4. 解析结果示例
```
原始地址：广东省清远市清城区阳光花园1栋101室
解析结果：
- 小区名称：阳光花园
- 楼栋号：1
- 房号：101
```

## 地址解析规则

### 小区名称识别
1. **数据库匹配**：优先从小区数据库中查找匹配
2. **关键词匹配**：使用常见小区关键词进行匹配
   - 关键词：小区、花园、城、苑、园、庭、府、湾、轩、阁、居、公寓、大厦、广场

### 楼栋号识别
支持以下格式：
- `数字+栋`：1栋、2栋、3栋
- `数字+号楼`：1号楼、2号楼、3号楼
- `数字+号`：1号、2号、3号
- `字母+数字+栋`：A1栋、B2栋、C3栋
- `字母+数字+号楼`：A1号楼、B2号楼、C3号楼
- `字母+数字+号`：A1号、B2号、C3号
- `数字+座`：1座、2座、3座
- `字母+数字+座`：A1座、B2座、C3座

### 房号识别
支持以下格式：
- `数字+室`：101室、202室、303室
- `数字+号`：101号、202号、303号
- `数字+房`：101房、202房、303房
- `数字-数字`：1-101、2-202、3-303（楼层-房间）
- `数字楼数字室`：1楼101室、2楼202室、3楼303室
- `数字楼数字号`：1楼101号、2楼202号、3楼303号
- `数字层数字室`：1层101室、2层202室、3层303室
- `数字层数字号`：1层101号、2层202号、3层303号

## 性能优化

### 1. 缓存机制
- 小区信息缓存30分钟
- 自动刷新缓存
- 减少数据库查询次数

### 2. 批量处理
- 支持批量地址解析
- 支持批量小区导入
- 提高处理效率

### 3. 错误处理
- 解析失败时记录错误日志
- 不影响其他记录的导入
- 提供详细的错误信息

## 测试和验证

### 1. 运行测试脚本
```bash
cd suijian-backend
node test-address-parser.js
```

### 2. 测试内容
- 数据库更新脚本执行
- 地址解析功能测试
- 批量解析测试
- 小区管理功能测试
- 缓存功能测试
- 特定地址格式测试

### 3. 测试地址示例
```
广东省清远市清城区阳光花园1栋101室
广东省清远市清城区碧桂园A2栋302号
广东省清远市清新区恒大城3号楼501房
广东省清远市清城区保利城B1栋2楼201室
广东省清远市清新区万科城5号楼3层301号
```

## 部署说明

### 1. 数据库更新
执行数据库更新脚本：
```bash
cd suijian-backend
node -e "require('./src/config/database').execute('CREATE TABLE IF NOT EXISTS communities (id VARCHAR(36) PRIMARY KEY, name VARCHAR(100) NOT NULL, alias VARCHAR(200), district VARCHAR(50), city VARCHAR(50), province VARCHAR(50), address TEXT, status INTEGER DEFAULT 1, created_at DATETIME DEFAULT CURRENT_TIMESTAMP, updated_at DATETIME DEFAULT CURRENT_TIMESTAMP)')"
```

### 2. 初始化小区数据
可以通过API接口或直接执行SQL脚本添加小区数据。

### 3. 验证功能
使用测试脚本验证功能是否正常。

## 注意事项

1. **小区数据质量**：小区数据库的质量直接影响解析准确率
2. **地址格式**：建议使用标准地址格式，提高解析成功率
3. **性能考虑**：大量数据导入时，建议分批处理
4. **错误处理**：解析失败时，相关字段会保持为空
5. **缓存更新**：小区数据更新后，缓存会自动刷新

## 常见问题

### Q1: 地址解析不准确怎么办？
A1: 检查小区数据库中是否有相关小区信息，可以手动添加小区数据。

### Q2: 如何提高解析准确率？
A2: 完善小区数据库，添加更多小区信息和别名。

### Q3: 支持哪些地址格式？
A3: 支持多种常见地址格式，详见"地址解析规则"部分。

### Q4: 如何处理解析失败的情况？
A4: 解析失败时，相关字段会保持为空，不影响其他数据的导入。

### Q5: 如何批量添加小区数据？
A5: 使用小区管理接口的批量导入功能，或直接执行SQL脚本。 