const { logger } = require('../utils/logger');
const Response = require('../utils/response');

/**
 * 全局错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
    // 记录错误日志
    logger.error('错误详情:', {
        error: err.message,
        stack: err.stack,
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
    });

    // JWT错误处理
    if (err.name === 'JsonWebTokenError') {
        return Response.unauthorized(res, '无效的token');
    }

    if (err.name === 'TokenExpiredError') {
        return Response.unauthorized(res, 'token已过期');
    }

    // 文件上传错误处理
    if (err.code === 'LIMIT_FILE_SIZE') {
        return Response.error(res, '文件大小超出限制', 400);
    }

    if (err.code === 'LIMIT_UNEXPECTED_FILE') {
        return Response.error(res, '不支持的文件类型', 400);
    }

    // 验证错误处理
    if (err.name === 'ValidationError') {
        return Response.error(res, '数据验证失败', 400, err.details);
    }

    // 默认错误处理
    const status = err.status || 500;
    const message = err.message || '服务器内部错误';

    // 生产环境下不暴露详细错误信息
    if (process.env.NODE_ENV === 'production' && status === 500) {
        return Response.serverError(res, '服务器内部错误');
    }

    Response.error(res, message, status, err);
};

module.exports = errorHandler;