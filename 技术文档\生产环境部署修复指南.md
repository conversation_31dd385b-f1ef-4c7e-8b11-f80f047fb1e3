# 生产环境重定向循环问题修复指南

## 问题描述
生产环境中出现 `net::ERR_TOO_MANY_REDIRECTS` 错误，导致CSS和JS文件无法正常加载。

## 问题原因分析
根据您提供的nginx配置，问题可能出现在：

1. **静态资源处理配置冲突**: `try_files $uri =404` 可能与主location块冲突
2. **路径匹配优先级**: 静态资源location可能被其他规则覆盖
3. **缓存头设置**: 缓存控制头可能导致浏览器重定向

## 立即修复方案

### 方案A: 使用简化配置（推荐）
```bash
# 1. 备份当前配置
sudo cp /etc/nginx/conf.d/default.conf /etc/nginx/conf.d/default.conf.backup

# 2. 使用简化配置
sudo cp nginx.simple.conf /etc/nginx/conf.d/default.conf

# 3. 测试配置
sudo nginx -t

# 4. 重新加载
sudo nginx -s reload
```

### 方案B: 修改现有配置
将您的nginx配置修改为：

```nginx
server {
    listen 80;
    server_name www.suijianranqi.cn suijianranqi.cn;
    
    root /data/wwwroot/mainpage;
    index index.html;

    # 静态资源 - 简化处理，移除try_files
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
        # 移除 try_files，让nginx直接处理
    }

    # API请求 - 直接返回404
    location /api/ {
        return 404;
    }

    # 主页面处理
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
}
```

## 关键修复点

### 1. 移除静态资源的try_files
**问题**: `try_files $uri =404` 可能导致重定向循环
**解决**: 移除try_files，让nginx直接处理静态文件

### 2. 简化location匹配
**问题**: 复杂的location匹配可能导致冲突
**解决**: 使用简单的正则匹配，避免嵌套重定向

### 3. 确保路径正确
**问题**: 文件路径不匹配
**解决**: 确认 `/data/wwwroot/mainpage` 路径下有正确的文件

## 验证步骤

### 1. 检查文件结构
```bash
ls -la /data/wwwroot/mainpage/
ls -la /data/wwwroot/mainpage/css/
ls -la /data/wwwroot/mainpage/js/
```

### 2. 测试静态文件访问
```bash
curl -I http://localhost/css/index-563be766.css
curl -I http://localhost/js/index-2c91ba0a.js
```

### 3. 检查nginx日志
```bash
tail -f /var/log/nginx/error.log
tail -f /var/log/nginx/access.log
```

## 自动化修复

### 使用修复脚本
```bash
# 给脚本执行权限
chmod +x fix-redirect-loop.sh

# 运行修复脚本
./fix-redirect-loop.sh
```

### 使用诊断脚本
```bash
# 诊断问题
chmod +x diagnose-redirect.sh
./diagnose-redirect.sh
```

## 常见问题排查

### 1. 文件不存在
```bash
# 检查文件是否存在
find /data/wwwroot/mainpage -name "*.css" -o -name "*.js"
```

### 2. 权限问题
```bash
# 修复权限
sudo chown -R nginx:nginx /data/wwwroot/mainpage
sudo chmod -R 755 /data/wwwroot/mainpage
```

### 3. 缓存问题
```bash
# 清除nginx缓存
sudo rm -rf /var/cache/nginx/*
sudo nginx -s reload
```

## 紧急回滚
如果修复后问题更严重：
```bash
# 回滚到备份配置
sudo cp /etc/nginx/conf.d/default.conf.backup /etc/nginx/conf.d/default.conf
sudo nginx -s reload
```

## 预防措施

### 1. 配置测试
每次修改配置前先测试：
```bash
sudo nginx -t
```

### 2. 备份配置
修改前自动备份：
```bash
sudo cp /etc/nginx/conf.d/default.conf /etc/nginx/conf.d/default.conf.backup.$(date +%Y%m%d_%H%M%S)
```

### 3. 监控日志
定期检查nginx日志：
```bash
tail -f /var/log/nginx/error.log
```

## 联系支持
如果问题仍然存在，请提供：
1. nginx错误日志
2. 当前nginx配置
3. 文件结构信息
4. 浏览器开发者工具的网络请求日志
