const { query, execute } = require('./src/config/database');
const { v4: uuidv4 } = require('uuid');

async function initDatabase() {
    try {
        console.log('开始初始化数据库...');

        // 创建loose_orders表
        const createTableSQL = `
            CREATE TABLE IF NOT EXISTS loose_orders (
                id VARCHAR(36) PRIMARY KEY,
                order_no VARCHAR(50) UNIQUE NOT NULL,
                customer_name VARCHAR(50) NOT NULL,
                customer_code VARCHAR(50),
                community_name VARCHAR(100),
                building VARCHAR(50),
                room_number VARCHAR(50),
                phone VARCHAR(20),
                contact_person VARCHAR(50),
                order_type VARCHAR(50),
                project_name VARCHAR(100),
                appeal_description TEXT,
                total_amount DECIMAL(10,2) DEFAULT 0.00,
                batch_id VARCHAR(36),
                party_address TEXT,
                party_appeal_description TEXT,
                party_total_amount DECIMAL(10,2) DEFAULT 0.00,
                party_remarks TEXT,
                status VARCHAR(20) DEFAULT 'pending',
                installation_date DATE,
                assigned_worker_id VARCHAR(36),
                remarks TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `;

        await execute(createTableSQL);
        console.log('loose_orders表创建成功');

        // 检查是否有数据
        const countResult = await query('SELECT COUNT(*) as total FROM loose_orders');
        console.log('当前数据总数:', countResult[0].total);

        // 如果没有数据，插入一些测试数据
        if (countResult[0].total === 0) {
            console.log('插入测试数据...');

            const testData = [{
                    id: uuidv4(),
                    order_no: 'WO202501001',
                    customer_name: '张三',
                    customer_code: 'YH001',
                    phone: '13800138001',
                    contact_person: '张三',
                    party_address: '广州市天河区珠江新城花城大道123号',
                    party_appeal_description: '燃气管道安装',
                    party_total_amount: 1500.00,
                    party_remarks: '客户要求尽快安装',
                    status: 'pending'
                },
                {
                    id: uuidv4(),
                    order_no: 'WO202501002',
                    customer_name: '李四',
                    customer_code: 'YH002',
                    phone: '13800138002',
                    contact_person: '李四',
                    party_address: '广州市越秀区中山路456号',
                    party_appeal_description: '燃气表更换',
                    party_total_amount: 800.00,
                    party_remarks: '旧表损坏需要更换',
                    status: 'in_progress'
                },
                {
                    id: uuidv4(),
                    order_no: 'WO202501003',
                    customer_name: '王五',
                    customer_code: 'YH003',
                    phone: '13800138003',
                    contact_person: '王五',
                    party_address: '广州市海珠区江南大道789号',
                    party_appeal_description: '燃气管道维修',
                    party_total_amount: 1200.00,
                    party_remarks: '管道漏气需要维修',
                    status: 'completed'
                }
            ];

            for (const data of testData) {
                const insertSQL = `
                    INSERT INTO loose_orders (
                        id, order_no, customer_name, customer_code, phone, contact_person,
                        party_address, party_appeal_description, party_total_amount, party_remarks, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `;

                await execute(insertSQL, [
                    data.id, data.order_no, data.customer_name, data.customer_code,
                    data.phone, data.contact_person, data.party_address, data.party_appeal_description,
                    data.party_total_amount, data.party_remarks, data.status
                ]);
            }

            console.log('测试数据插入完成');
        }

        // 验证数据
        const finalCount = await query('SELECT COUNT(*) as total FROM loose_orders');
        console.log('最终数据总数:', finalCount[0].total);

        const sampleData = await query('SELECT * FROM loose_orders LIMIT 3');
        console.log('示例数据:', sampleData);

        console.log('数据库初始化完成');

    } catch (error) {
        console.error('数据库初始化失败:', error);
    }
}

initDatabase();