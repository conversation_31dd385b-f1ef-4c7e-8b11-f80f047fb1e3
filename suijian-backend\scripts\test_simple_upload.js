#!/usr/bin/env node

/**
 * 简单的文件上传测试脚本
 */

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

// 配置
const API_BASE_URL = 'http://localhost:3000';
const UPLOAD_ENDPOINT = '/api/loose-orders/import';

/**
 * 创建测试文件
 */
function createTestFile(filePath) {
    console.log(`📁 创建测试文件: ${filePath}`);

    // 创建一个简单的CSV文件
    const csvContent = `工单号,工单类型,工单状态,工单来源,工单标题,工单内容,创建人,创建时间
WO001,维修,待处理,系统,设备故障,设备无法启动,张三,2025-08-08
WO002,安装,进行中,项目,新设备安装,安装新设备,李四,2025-08-08`;

    fs.writeFileSync(filePath, csvContent);
    console.log('✅ 测试文件创建完成');
}

/**
 * 测试文件上传
 */
async function testUpload(filePath) {
    console.log('\n🚀 开始测试文件上传...');

    try {
        const formData = new FormData();
        formData.append('file', fs.createReadStream(filePath), {
            filename: path.basename(filePath),
            contentType: 'application/vnd.ms-excel'
        });

        console.log('📤 发送上传请求...');
        console.log('文件路径:', filePath);
        console.log('文件大小:', fs.statSync(filePath).size, 'bytes');

        const response = await axios.post(`${API_BASE_URL}${UPLOAD_ENDPOINT}`, formData, {
            headers: {
                ...formData.getHeaders(),
                'Authorization': 'Bearer test-token'
            },
            timeout: 30000,
            maxContentLength: Infinity,
            maxBodyLength: Infinity
        });

        console.log('\n✅ 上传测试成功');
        console.log('📄 响应状态:', response.status);
        console.log('📄 响应数据:', response.data);

    } catch (error) {
        console.error('\n❌ 上传测试失败');
        console.error('错误类型:', error.code || 'Unknown');
        console.error('错误消息:', error.message);

        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }

        if (error.request) {
            console.error('请求错误:', error.request);
        }
    }
}

/**
 * 测试服务器连接
 */
async function testConnection() {
    console.log('🔍 测试服务器连接...');

    try {
        const response = await axios.get(`${API_BASE_URL}/health`, {
            timeout: 5000
        });
        console.log('✅ 服务器连接正常');
        return true;
    } catch (error) {
        console.error('❌ 服务器连接失败:', error.message);
        return false;
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🧪 简单文件上传测试');
    console.log('='.repeat(50));

    // 测试连接
    const connected = await testConnection();
    if (!connected) {
        console.log('\n❌ 无法连接到服务器，请确保服务器正在运行');
        return;
    }

    // 创建测试文件
    const testFilePath = path.join(__dirname, '../uploads/test_simple.xls');
    const uploadDir = path.dirname(testFilePath);

    if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
    }

    if (!fs.existsSync(testFilePath)) {
        createTestFile(testFilePath);
    } else {
        console.log('📁 使用现有测试文件');
    }

    // 测试上传
    await testUpload(testFilePath);

    console.log('\n📝 测试完成');
}

// 运行主函数
if (require.main === module) {
    main().catch(console.error);
}