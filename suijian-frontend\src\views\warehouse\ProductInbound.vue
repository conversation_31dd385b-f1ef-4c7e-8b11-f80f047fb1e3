<template>
  <div class="product-inbound-container">
    <el-card class="main-card">
      <!-- 入库信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="12">
          <el-form-item label="入库单号:" required>
            <el-input 
              v-model="inboundOrderId" 
              placeholder="请输入入库单号"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="入库日期:">
            <el-date-picker
              v-model="inboundForm.inboundDate"
              type="date"
              placeholder="请选择入库日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 商品列表 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <el-button type="primary" @click="openMaterialDialog" style="margin-bottom: 16px;">选择商品</el-button>
          <el-button @click="clearSelectedMaterials" style="margin-left: 8px; margin-bottom: 16px;">清空</el-button>
          <el-table :data="selectedMaterials" border style="width: 100%; margin-bottom: 16px;">
            <el-table-column prop="companyCode" label="商品编码" width="200" />
            <el-table-column prop="name" label="商品名称" min-width="150" />
            <el-table-column prop="clientCodes" label="甲料编码" width="150" />
            <el-table-column prop="specification" label="规格" min-width="120" />
            <el-table-column prop="stockQuantity" label="库存" width="100" />
            <el-table-column prop="unit" label="单位" width="90" />
            <el-table-column label="数量" width="150">
              <template #default="scope">
                <el-input-number v-model="scope.row.quantity" :min="1" style="width: 100%;" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="scope">
                <el-button type="danger" link @click="removeMaterial(scope.$index)">移除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      
      <!-- 备注信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <el-form-item label="备注:">
            <el-input
              v-model="inboundForm.remarks"
              type="textarea"
              :rows="2"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button type="primary" @click="handleSubmit" :disabled="selectedMaterials.length === 0">提交</el-button>
        <el-button @click="handleReset">重置</el-button>
      </div>
    </el-card>

    <!-- 商品选择组件 -->
    <MaterialSelector
      v-model="materialDialogVisible"
      :title="`选择商品 (已选: ${selectedMaterials.length})`"
      category="商品"
      :selected-ids="selectedMaterialIds"
      :disable-category-switch="true"
      @selection-change="handleMaterialSelectionChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import MaterialSelector from '@/components/MaterialSelector.vue'
import request from '@/utils/request'

// 入库表单
const inboundForm = reactive({
  inboundDate: new Date().toISOString().split('T')[0],
  remarks: ''
})

// 生成入库单号
const generateInboundOrderId = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `RK${year}${month}${day}${random}`
}

// 入库单号
const inboundOrderId = ref(generateInboundOrderId())

// 商品选择相关
const materialDialogVisible = ref(false)
const selectedMaterials = ref([])

// 计算已选商品的ID列表
const selectedMaterialIds = computed(() => {
  return selectedMaterials.value.map(m => m.id)
})

// 本地存储key
const SELECTED_MATERIALS_KEY = 'product_inbound_selected_materials'
const REMARKS_KEY = 'product_inbound_remarks'

// 页面加载时恢复已选商品和备注
onMounted(() => {
  const saved = localStorage.getItem(SELECTED_MATERIALS_KEY)
  if (saved) {
    try {
      selectedMaterials.value = JSON.parse(saved)
    } catch {}
  }
  
  const savedRemarks = localStorage.getItem(REMARKS_KEY)
  if (savedRemarks) {
    try {
      inboundForm.remarks = savedRemarks
    } catch {}
  }
})

// 监听selectedMaterials变化，自动保存
watch(selectedMaterials, (val) => {
  localStorage.setItem(SELECTED_MATERIALS_KEY, JSON.stringify(val))
}, { deep: true })

// 监听备注变化，自动保存
watch(() => inboundForm.remarks, (val) => {
  localStorage.setItem(REMARKS_KEY, val || '')
})

// 打开商品选择器
const openMaterialDialog = () => {
  materialDialogVisible.value = true
}

// 处理商品选择变化
const handleMaterialSelectionChange = (materials: any[]) => {
  const newSelectedMaterials = materials.map(m => ({
    ...m,
    quantity: selectedMaterials.value.find(existing => existing.id === m.id)?.quantity || 1,
    remarks: selectedMaterials.value.find(existing => existing.id === m.id)?.remarks || ''
  }))
  
  selectedMaterials.value = newSelectedMaterials
}

// 移除商品
const removeMaterial = (idx: number) => {
  selectedMaterials.value.splice(idx, 1)
}

// 清空所有商品
const clearSelectedMaterials = () => {
  selectedMaterials.value = []
  localStorage.removeItem(SELECTED_MATERIALS_KEY)
  localStorage.removeItem(REMARKS_KEY)
  ElMessage.success('已清空所有商品')
}

// 提交
const handleSubmit = async () => {
  if (!inboundOrderId.value.trim()) {
    ElMessage.error('请输入入库单号')
    return
  }
  
  if (selectedMaterials.value.length === 0) {
    ElMessage.warning('请选择商品')
    return
  }
  
  // 校验数量
  for (const m of selectedMaterials.value) {
    if (!m.quantity || m.quantity <= 0) {
      ElMessage.error('请填写所有商品的数量')
      return
    }
  }
  
  // 构造请求数据
  const items = selectedMaterials.value.map(m => ({
    id: m.id,
    quantity: m.quantity
  }))
  
  try {
    const res = await request.post('/api/products/inbound', {
      items,
      orderId: inboundOrderId.value,
      inboundDate: inboundForm.inboundDate,
      remarks: inboundForm.remarks
    })
    
    if (res && res.success) {
      ElMessage.success('商品入库成功')
      // 清除当前数据
      selectedMaterials.value = []
      inboundForm.remarks = ''
      localStorage.removeItem(SELECTED_MATERIALS_KEY)
      localStorage.removeItem(REMARKS_KEY)
    } else {
      ElMessage.error(res.message || '商品入库失败')
    }
  } catch (e) {
    console.error('商品入库失败:', e)
    ElMessage.error('商品入库失败')
  }
}

// 重置
const handleReset = () => {
  selectedMaterials.value = []
  inboundForm.remarks = ''
  inboundOrderId.value = generateInboundOrderId()
  localStorage.removeItem(SELECTED_MATERIALS_KEY)
  localStorage.removeItem(REMARKS_KEY)
  ElMessage.success('已重置')
}
</script>

<style lang="scss" scoped>
.product-inbound-container {
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  margin: 0;
  padding: 0;
  
  .main-card {
    margin-bottom: 20px;
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
    }
  }
}
</style>