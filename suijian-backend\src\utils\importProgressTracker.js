/**
 * 通用导入进度跟踪模块
 * 基于物料列表的导入Excel实现，提供统一的进度跟踪功能
 * 
 * 使用方式：
 * 1. 创建任务：createTask(taskId, total)
 * 2. 更新进度：updateTask(taskId, updates)
 * 3. 查询进度：getTask(taskId)
 * 4. 完成任务：completeTask(taskId, status, message)
 * 5. 清理任务：cleanupTask(taskId)
 */

class ImportProgressTracker {
    constructor() {
        // 全局内存存储导入任务进度
        this.importTasks = {};

        // 定期清理已完成的任务（20秒后自动清理）
        this.cleanupInterval = setInterval(() => {
            this.cleanupCompletedTasks();
        }, 10000); // 每10秒检查一次
    }

    /**
     * 创建新的导入任务
     * @param {string} taskId - 任务ID
     * @param {number} total - 总数据条数
     * @returns {Object} 任务对象
     */
    createTask(taskId, total = 0) {
        const task = {
            taskId,
            status: 'processing', // processing, done, error
            total: total,
            current: 0,
            successCount: 0,
            fail: 0,
            message: '',
            startTime: Date.now(),
            endTime: null,
            // 扩展字段，支持更详细的进度信息
            phase: 'reading', // reading, processing, inserting, completed
            phaseProgress: 0, // 0-100
            errors: [],
            successDetails: [],
            skipDetails: []
        };

        this.importTasks[taskId] = task;
        return task;
    }

    /**
     * 更新任务进度
     * @param {string} taskId - 任务ID
     * @param {Object} updates - 更新内容
     * @returns {Object} 更新后的任务对象
     */
    updateTask(taskId, updates) {
        const task = this.importTasks[taskId];
        if (task) {
            Object.assign(task, updates);

            // 自动计算进度百分比
            if (task.total > 0 && updates.current !== undefined) {
                task.phaseProgress = Math.floor((task.current / task.total) * 100);
            }

            // 确保进度不超过100%
            if (task.phaseProgress > 100) {
                task.phaseProgress = 100;
            }
        }
        return task;
    }

    /**
     * 获取任务进度
     * @param {string} taskId - 任务ID
     * @returns {Object|null} 任务对象
     */
    getTask(taskId) {
        return this.importTasks[taskId] || null;
    }

    /**
     * 完成任务
     * @param {string} taskId - 任务ID
     * @param {string} status - 完成状态 (done, error)
     * @param {string} message - 完成消息
     * @returns {Object} 完成后的任务对象
     */
    completeTask(taskId, status = 'done', message = '') {
        const task = this.importTasks[taskId];
        if (task) {
            task.status = status;
            task.endTime = Date.now();
            task.phase = 'completed';
            task.phaseProgress = 100;
            if (message) {
                task.message = message;
            }
        }
        return task;
    }

    /**
     * 添加错误信息
     * @param {string} taskId - 任务ID
     * @param {Object} error - 错误对象
     */
    addError(taskId, error) {
        const task = this.importTasks[taskId];
        if (task) {
            task.errors.push({
                ...error,
                timestamp: Date.now()
            });

            // 限制错误数量，避免内存溢出
            if (task.errors.length > 50) {
                task.errors = task.errors.slice(-50);
            }
        }
    }

    /**
     * 添加成功记录
     * @param {string} taskId - 任务ID
     * @param {Object} detail - 成功详情
     */
    addSuccessDetail(taskId, detail) {
        const task = this.importTasks[taskId];
        if (task) {
            task.successDetails.push({
                ...detail,
                timestamp: Date.now()
            });

            // 限制成功记录数量
            if (task.successDetails.length > 20) {
                task.successDetails = task.successDetails.slice(-20);
            }
        }
    }

    /**
     * 添加跳过记录
     * @param {string} taskId - 任务ID
     * @param {Object} detail - 跳过详情
     */
    addSkipDetail(taskId, detail) {
        const task = this.importTasks[taskId];
        if (task) {
            task.skipDetails.push({
                ...detail,
                timestamp: Date.now()
            });

            // 限制跳过记录数量
            if (task.skipDetails.length > 20) {
                task.skipDetails = task.skipDetails.slice(-20);
            }
        }
    }

    /**
     * 获取所有任务
     * @returns {Array} 任务列表
     */
    getAllTasks() {
        return Object.values(this.importTasks);
    }

    /**
     * 删除任务
     * @param {string} taskId - 任务ID
     * @returns {boolean} 是否删除成功
     */
    deleteTask(taskId) {
        if (this.importTasks[taskId]) {
            delete this.importTasks[taskId];
            return true;
        }
        return false;
    }

    /**
     * 清理已完成的任务
     * 任务完成后20秒自动清理
     */
    cleanupCompletedTasks() {
        const now = Date.now();
        const cleanupTime = 20000; // 20秒后清理

        for (const [taskId, task] of Object.entries(this.importTasks)) {
            if (task.endTime && (now - task.endTime) > cleanupTime) {
                delete this.importTasks[taskId];
            }
        }
    }

    /**
     * 手动清理任务
     * @param {string} taskId - 任务ID
     */
    cleanupTask(taskId) {
        this.deleteTask(taskId);
    }

    /**
     * 获取任务统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const tasks = Object.values(this.importTasks);
        return {
            total: tasks.length,
            processing: tasks.filter(t => t.status === 'processing').length,
            completed: tasks.filter(t => t.status === 'done').length,
            failed: tasks.filter(t => t.status === 'error').length
        };
    }

    /**
     * 销毁实例，清理定时器
     */
    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
        this.importTasks = {};
    }
}

// 创建全局实例
const importProgressTracker = new ImportProgressTracker();

// 进程退出时清理资源
process.on('exit', () => {
    importProgressTracker.destroy();
});

process.on('SIGINT', () => {
    importProgressTracker.destroy();
    process.exit(0);
});

process.on('SIGTERM', () => {
    importProgressTracker.destroy();
    process.exit(0);
});

module.exports = importProgressTracker;