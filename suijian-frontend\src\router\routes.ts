import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/',
    component: () => import('@/layout/index.vue'),
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: {
          title: '首页',
          requiresAuth: true
        }
      },
      // 首页二级菜单
      {
        path: 'dashboard/material-statistics',
        name: 'MaterialStatistics',
        component: () => import('@/views/dashboard/MaterialStatistics.vue'),
        meta: {
          title: '工程物料统计',
          requiresAuth: true
        }
      },
      {
        path: 'dashboard/project-progress',
        name: 'ProjectProgressStatistics',
        component: () => import('@/views/dashboard/ProjectProgressStatistics.vue'),
        meta: {
          title: '工程进度统计',
          requiresAuth: true
        }
      },
      {
        path: 'dashboard/loose-material-statistics',
        name: 'LooseMaterialStatistics',
        component: () => import('@/views/dashboard/LooseMaterialStatistics.vue'),
        meta: {
          title: '散单物料统计',
          requiresAuth: true
        }
      },
      {
        path: 'dashboard/loose-order-statistics',
        name: 'LooseOrderStatistics',
        component: () => import('@/views/dashboard/LooseOrderStatistics.vue'),
        meta: {
          title: '散单情况统计',
          requiresAuth: true
        }
      },

      // 仓库管理
      {
        path: 'warehouse/material-list',
        name: 'MaterialList',
        component: () => import('@/views/warehouse/MaterialList.vue'),
        meta: {
          title: '物料列表',
          requiresAuth: true
        }
      },
      {
        path: 'warehouse/material-apply',
        name: 'MaterialApply',
        component: () => import('@/views/warehouse/MaterialApply.vue'),
        meta: {
          title: '领料申请',
          requiresAuth: true
        }
      },
      {
        path: 'warehouse/material-inbound',
        name: 'MaterialInbound',
        component: () => import('@/views/warehouse/MaterialInbound.vue'),
        meta: {
          title: '甲料入库',
          requiresAuth: true
        }
      },
      {
        path: 'warehouse/material-return',
        name: 'MaterialReturn',
        component: () => import('@/views/warehouse/MaterialReturn.vue'),
        meta: {
          title: '物料退仓',
          requiresAuth: true
        }
      },
      {
        path: 'warehouse/auxiliary-purchase',
        name: 'AuxiliaryPurchase',
        component: () => import('@/views/warehouse/AuxiliaryPurchase.vue'),
        meta: {
          title: '辅料采购',
          requiresAuth: true
        }
      },
      {
        path: 'warehouse/product-inbound',
        name: 'ProductInbound',
        component: () => import('@/views/warehouse/ProductInbound.vue'),
        meta: {
          title: '商品入库',
          requiresAuth: true
        }
      },
      {
        path: 'warehouse/material-records',
        name: 'MaterialRecords',
        component: () => import('@/views/warehouse/MaterialRecords.vue'),
        meta: {
          title: '进出记录',
          requiresAuth: true
        }
      },

      {
        path: 'warehouse/stock-warning',
        name: 'StockWarning',
        component: () => import('@/views/warehouse/StockWarning.vue'),
        meta: {
          title: '库存预警',
          requiresAuth: true
        }
      },
      // 散户订单
      {
        path: 'loose-orders/order-list',
        name: 'OrderList',
        component: () => import('@/views/looseOrders/OrderList.vue'),
        meta: {
          title: '工单列表',
          requiresAuth: true
        }
      },
      {
        path: 'loose-orders/material-verification',
        name: 'MaterialVerification',
        component: () => import('@/views/looseOrders/MaterialVerification.vue'),
        meta: {
          title: '核对平料',
          requiresAuth: true
        }
      },
      {
        path: 'loose-orders/order-settlement',
        name: 'OrderSettlement',
        component: () => import('@/views/looseOrders/OrderSettlement.vue'),
        meta: {
          title: '工单结算',
          requiresAuth: true
        }
      },
      {
        path: 'loose-orders/order-assign',
        name: 'OrderAssign',
        component: () => import('@/views/looseOrders/OrderAssign.vue'),
        meta: {
          title: '工单录入',
          requiresAuth: true
        }
      },



      // 安检录入
      {
        path: 'loose-orders/safety-inspection-entry',
        name: 'SafetyInspectionEntry',
        component: () => import('@/views/looseOrders/SafetyInspectionEntry.vue'),
        meta: {
          title: '安检录入',
          requiresAuth: true
        }
      },
      // 安检列表
      {
        path: 'loose-orders/safety-inspection-list',
        name: 'SafetyInspectionList',
        component: () => import('@/views/looseOrders/SafetyInspectionList.vue'),
        meta: {
          title: '安检列表',
          requiresAuth: true
        }
      },
      // 维修结算
      {
        path: 'loose-orders/repair-settlement',
        name: 'RepairSettlement',
        component: () => import('@/views/looseOrders/RepairSettlement.vue'),
        meta: {
          title: '维修结算',
          requiresAuth: true
        }
      },
      // 工程订单
      {
        path: 'projects/project-list',
        name: 'ProjectList',
        component: () => import('@/views/projects/ProjectList.vue'),
        meta: {
          title: '工程订单列表',
          requiresAuth: true
        }
      },
      {
        path: 'projects/project-assign',
        name: 'ProjectAssign',
        component: () => import('@/views/projects/PartyDispatch.vue'),
        meta: {
          title: '甲方派单',
          requiresAuth: true
        }
      },
      {
        path: 'projects/project-start',
        name: 'ProjectStart',
        component: () => import('@/views/projects/ProjectStart.vue'),
        meta: {
          title: '工程开始',
          requiresAuth: true
        }
      },
      {
        path: 'projects/project-progress',
        name: 'ProjectProgress',
        component: () => import('@/views/projects/ProjectProgress.vue'),
        meta: {
          title: '工程推进',
          requiresAuth: true
        }
      },
      {
        path: 'projects/project-pause',
        name: 'ProjectPause',
        component: () => import('@/views/projects/ProjectPause.vue'),
        meta: {
          title: '工程暂停',
          requiresAuth: true
        }
      },
      {
        path: 'projects/project-finish',
        name: 'ProjectFinish',
        component: () => import('@/views/projects/ProjectFinish.vue'),
        meta: {
          title: '工程结算',
          requiresAuth: true
        }
      },
      // 员工管理
      {
        path: 'employees/employee-list',
        name: 'EmployeeList',
        component: () => import('@/views/employees/EmployeeList.vue'),
        meta: {
          title: '员工列表',
          requiresAuth: true
        }
      },
      {
        path: 'employees/performance-setting',
        name: 'PerformanceSetting',
        component: () => import('@/views/employees/PerformanceSetting.vue'),
        meta: {
          title: '绩效设置',
          requiresAuth: true
        }
      },
      // 系统设置
      {
        path: 'system/user-management',
        name: 'UserManagement',
        component: () => import('@/views/system/UserManagement.vue'),
        meta: {
          title: '用户管理',
          requiresAuth: true
        }
      },
      {
        path: 'system/add-user',
        name: 'AddUser',
        component: () => import('@/views/system/AddUser.vue'),
        meta: {
          title: '增加用户',
          requiresAuth: true
        }
      },
      {
        path: 'system/permission-management',
        name: 'PermissionManagement',
        component: () => import('@/views/system/PermissionManagement.vue'),
        meta: {
          title: '权限管理',
          requiresAuth: true
        }
      },
      {
        path: 'system/system-log',
        name: 'SystemLog',
        component: () => import('@/views/system/SystemLog.vue'),
        meta: {
          title: '系统日志',
          requiresAuth: true
        }
      },
      {
        path: 'system/data-import',
        name: 'DataImport',
        component: () => import('@/views/system/DataImport.vue'),
        meta: {
          title: '基础数据导入',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      requiresAuth: false
    }
  }
]

export default routes 