<!--
  MaterialSelector 物料选择器组件
  
  功能特性：
  - 支持实时搜索和分页浏览物料
  - 支持多选和单选模式
  - 支持跨页面保持选择状态
  - 支持按物料类别筛选
  - 支持锁定分类不可切换
  - 自动记忆和恢复选择状态
  - 实时同步选择结果到父组件
  
  Props 参数：
  @param {boolean} modelValue - 弹窗显示状态，支持 v-model
  @param {string} title - 弹窗标题，默认 "选择物料"
  @param {string} searchPlaceholder - 搜索框占位符，默认 "请输入物料名称或编码搜索"
  @param {string} category - 物料类别筛选，如 "甲料"、"乙料" 等，默认为空（不筛选）
  @param {boolean} multiple - 是否支持多选，默认 true
  @param {string[]} selectedIds - 已选物料ID列表，用于恢复选择状态
  @param {number} maxSelect - 最大选择数量，0 表示无限制，默认 0
  @param {boolean} disableCategorySwitch - 是否禁用分类切换，默认 false
  
  Events 事件：
  @event update:modelValue - 弹窗显示状态变化时触发
  @event selection-change - 选择变化时实时触发，参数为选中的物料列表
  
  使用示例：
  
  基础用法：
  <MaterialSelector
    v-model="dialogVisible"
    title="选择物料"
    @selection-change="handleSelectionChange"
  />
  
  完整用法：
  <MaterialSelector
    v-model="materialDialogVisible"
    :title="`选择物料 (已选: ${selectedMaterials.length})`"
    category="甲料"
    :selected-ids="selectedMaterialIds"
    :max-select="10"
    :disable-category-switch="true"
    search-placeholder="请输入物料名称或编码"
    @selection-change="handleMaterialSelectionChange"
  />
  
  父组件代码示例：
  <script setup>
  import { ref, computed } from 'vue'
  import MaterialSelector from '@/components/MaterialSelector.vue'
  
  const materialDialogVisible = ref(false)
  const selectedMaterials = ref([])
  
  // 计算已选物料ID列表，用于恢复选择状态
  const selectedMaterialIds = computed(() => selectedMaterials.value.map(m => m.id))
  
  // 处理物料选择变化（实时同步）
  const handleMaterialSelectionChange = (materials) => {
    selectedMaterials.value = materials.map(m => ({
      ...m,
      quantity: selectedMaterials.value.find(existing => existing.id === m.id)?.quantity || 1,
      remarks: selectedMaterials.value.find(existing => existing.id === m.id)?.remarks || ''
    }))
  }
  
  // 打开物料选择器
  const openMaterialDialog = () => {
    materialDialogVisible.value = true
  }
  </script>
  
  注意事项：
  1. 使用 v-model 绑定弹窗显示状态
  2. 通过 selected-ids 传入已选物料ID列表以恢复选择状态
  3. 监听 selection-change 事件实时获取选择结果
  4. 选择结果会在弹窗关闭重新打开时自动恢复
  5. 支持跨页面保持选择状态
  6. 当 disableCategorySwitch 为 true 时，分类选择框将被禁用
-->

<template>
  <el-dialog 
    v-model="visible" 
    :title="title" 
    width="1100px" 
    @close="handleClose"
    @open="() => console.log('MaterialSelector 弹窗已打开')"
    :destroy-on-close="false"
  >
    <div class="material-search">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-input
            v-model="searchKeyword"
            :placeholder="searchPlaceholder"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="selectedCategory"
            placeholder="请选择分类"
            clearable
            style="width: 200px"
            @change="handleCategoryChange"
            :disabled="props.disableCategorySwitch"
          >
            <el-option label="全部" value="" />
            <el-option label="甲料" value="甲料" />
            <el-option label="乙料" value="乙料" />
            <el-option label="商品" value="商品" />
            <el-option label="辅料" value="辅料" />
          </el-select>
        </el-col>
      </el-row>
    </div>
    
    <el-table
      ref="materialTableRef"
      :data="materialList"
      border
      style="width: 100%; min-width: 1000px;"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column type="selection" width="50" />
      <el-table-column prop="companyCode" label="物料编码" min-width="120" />
      <el-table-column prop="name" label="物料名称" min-width="150" />
      <el-table-column prop="clientCodes" label="甲料编码" min-width="120" />
      <el-table-column prop="specification" label="规格" min-width="120" />
      <el-table-column prop="stockQuantity" label="数量" min-width="90" />
      <el-table-column prop="unit" label="单位" width="90" />
    </el-table>
    
    <el-pagination
      v-model:current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      layout="prev, pager, next, jumper"
      style="margin-top: 16px; text-align: right;"
      @current-change="handlePageChange"
      @size-change="handleSizeChange"
    />
    
    <template #footer>
      <el-button @click="handleClear">清空</el-button>
      <el-button type="primary" @click="handleClose">
        关闭
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/**
 * MaterialSelector 物料选择器组件
 * 
 * 快速开始：
 * 1. 导入组件：import MaterialSelector from '@/components/MaterialSelector.vue'
 * 2. 使用 v-model 控制弹窗显示：<MaterialSelector v-model="visible" />
 * 3. 监听选择变化：@selection-change="handleChange"
 * 4. 传入已选ID恢复状态：:selected-ids="selectedIds"
 * 
 * 更多详细用法请查看上方的组件注释
 */

import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import request from '@/utils/request'

/**
 * Props 接口定义
 * 定义了组件所有可接收的属性及其类型
 */
interface Props {
  /** 弹窗显示状态，支持 v-model 双向绑定 */
  modelValue: boolean
  /** 弹窗标题 */
  title?: string
  /** 搜索框占位符文本 */
  searchPlaceholder?: string
  /** 物料类别筛选条件 */
  category?: string
  /** 是否支持多选 */
  multiple?: boolean
  /** 已选中的物料ID列表，用于恢复选择状态 */
  selectedIds?: string[]
  /** 最大选择数量限制，0表示无限制 */
  maxSelect?: number
  /** 是否禁用分类切换 */
  disableCategorySwitch?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '选择物料',
  searchPlaceholder: '请输入物料名称或编码搜索',
  category: '',
  multiple: true,
  selectedIds: () => [],
  maxSelect: 0,
  disableCategorySwitch: false
})

/**
 * Events 事件定义
 * 定义了组件会触发的所有事件及其参数类型
 */
const emit = defineEmits<{
  /** 弹窗显示状态变化时触发，支持 v-model */
  'update:modelValue': [value: boolean]
  /** 物料选择变化时触发，实时同步选择结果 */
  'selection-change': [materials: any[]]
}>()

// 响应式数据
const visible = computed({
  get: () => {
    console.log('MaterialSelector visible getter:', props.modelValue)
    return props.modelValue
  },
  set: (value) => {
    console.log('MaterialSelector visible setter:', value)
    emit('update:modelValue', value)
  }
})

const searchKeyword = ref('')
const selectedCategory = ref(props.category || '')
const currentPage = ref(1)
const pageSize = ref(10)
const materialList = ref([])
const total = ref(0)
const loading = ref(false)
const selectedMaterials = ref([])
const materialTableRef = ref()

// 防重复触发标志
const isSettingSelection = ref(false)
// 弹窗初始化标志
const isInitializing = ref(false)

// 搜索防抖
let searchTimer: NodeJS.Timeout | null = null

// 加载物料列表
const loadMaterialList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchKeyword.value,
      ...(selectedCategory.value && { category: selectedCategory.value })
    }
    
    console.log('=== loadMaterialList 开始 ===')
    console.log('当前selectedCategory.value:', selectedCategory.value)
    console.log('请求参数:', params)
    
    const response = await request.get('/api/materials', { params })
    if (response && response.success && response.data) {
      materialList.value = response.data.list || []
      total.value = response.data.pagination?.total || 0
      
      console.log('加载到的物料数量:', materialList.value.length)
      
      // 根据已选ID自动勾选
      if (visible.value && materialTableRef.value) {
        await nextTick()
        isSettingSelection.value = true
        
        console.log('=== 开始恢复选择状态 ===')
        console.log('当前页面物料数量:', materialList.value.length)
        console.log('props.selectedIds:', props.selectedIds)
        console.log('initialSelectedIds:', initialSelectedIds)
        console.log('当前selectedMaterials.value:', selectedMaterials.value)
        
        // 使用保存的initialSelectedIds来恢复选择状态，避免异步过程中的变化
        const targetSelectedIds = initialSelectedIds.length > 0 ? initialSelectedIds : props.selectedIds
        console.log('使用的targetSelectedIds:', targetSelectedIds)
        
        const currentPageSelections = []
        
        materialList.value.forEach(row => {
          const isSelected = targetSelectedIds.includes(row.id)
          console.log(`检查物料 ${row.id} (${row.name})，是否在targetSelectedIds中:`, isSelected)
          if (isSelected) {
            materialTableRef.value.toggleRowSelection(row, true)
            currentPageSelections.push(row)
            console.log(`已选中物料:`, row.name)
          }
        })
        
        console.log('当前页面选中的物料数量:', currentPageSelections.length)
        
        // 获取当前页面的所有物料ID
        const currentPageIds = materialList.value.map(item => item.id)
        
        // 保留其他页面已选择的物料（不在当前页面的），添加当前页面新选择的
        const otherPagesSelections = (selectedMaterials.value || []).filter(item => 
          !currentPageIds.includes(item.id) && targetSelectedIds.includes(item.id)
        )
        
        console.log('其他页面的选择数量:', otherPagesSelections.length)
        
        // 如果当前页面没有找到任何已选择的物料，但有targetSelectedIds，说明选择的物料在其他页面
        // 需要从targetSelectedIds中构建完整的selectedMaterials
        if (currentPageSelections.length === 0 && targetSelectedIds.length > 0) {
          // 从targetSelectedIds构建完整的selectedMaterials
          const allSelectedMaterials = targetSelectedIds.map(id => {
            // 尝试从当前页面物料中找到
            const currentPageMaterial = materialList.value.find(item => item.id === id)
            if (currentPageMaterial) {
              return currentPageMaterial
            }
            // 如果不在当前页面，从其他页面选择中找
            const otherPageMaterial = otherPagesSelections.find(item => item.id === id)
            if (otherPageMaterial) {
              return otherPageMaterial
            }
            // 如果都找不到，创建一个占位对象（这种情况不应该发生，但为了安全）
            return { id, name: `物料${id}`, company_code: '', client_code: '', specification: '', unit: '' }
          }).filter(item => item) // 过滤掉无效项
          
          selectedMaterials.value = allSelectedMaterials
          console.log('从targetSelectedIds重建选择:', selectedMaterials.value)
        } else {
          selectedMaterials.value = [...otherPagesSelections, ...currentPageSelections]
        }
        
        console.log('=== 恢复选择状态完成 ===')
        console.log('最终selectedMaterials.value:', selectedMaterials.value)
        console.log('页面加载后的总选择:', selectedMaterials.value)
        isSettingSelection.value = false
        
        // 如果正在初始化，等待恢复完成后再结束初始化状态
        if (isInitializing.value) {
          await nextTick()
          isInitializing.value = false
          console.log('选择状态恢复完成，初始化结束')
        }
      }
    } else {
      materialList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('加载物料列表失败:', error)
    materialList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    currentPage.value = 1
    loadMaterialList()
  }, 300)
}

// 分类变化处理
const handleCategoryChange = () => {
  // 如果分类切换被禁用，则不处理变化
  if (props.disableCategorySwitch) {
    console.log('分类切换被禁用，忽略分类变化')
    return
  }
  
  currentPage.value = 1
  loadMaterialList()
}

// 分页处理
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadMaterialList()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadMaterialList()
}

// 选择变化处理
const handleSelectionChange = (selection: any[]) => {
  console.log('=== handleSelectionChange 被调用 ===')
  console.log('isSettingSelection.value:', isSettingSelection.value)
  console.log('isInitializing.value:', isInitializing.value)
  console.log('selection:', selection)
  console.log('selection.length:', selection.length)
  
  if (isSettingSelection.value) {
    console.log('正在设置选择中，跳过处理')
    return
  }
  
  if (isInitializing.value) {
    console.log('正在初始化中，跳过处理')
    return
  }
  
  // 确保 selectedMaterials.value 存在
  if (!selectedMaterials.value) {
    selectedMaterials.value = []
  }
  
  // 获取当前页面的所有物料ID
  const currentPageIds = materialList.value.map(item => item.id)
  
  // 保留其他页面的选择，更新当前页面的选择
  const otherPagesSelections = selectedMaterials.value.filter(item => !currentPageIds.includes(item.id))
  selectedMaterials.value = [...otherPagesSelections, ...selection]
  
  // 检查最大选择数量
  if (props.maxSelect > 0 && selectedMaterials.value && selectedMaterials.value.length > props.maxSelect) {
    ElMessage.warning(`最多只能选择${props.maxSelect}个物料`)
    // 恢复到之前的状态
    selectedMaterials.value = selectedMaterials.value.slice(0, props.maxSelect)
    return
  }
  
  console.log('当前总选择:', selectedMaterials.value)
  console.log('准备emit selection-change事件')
  emit('selection-change', selectedMaterials.value)
  console.log('=== handleSelectionChange 结束 ===')
}

// 行点击处理
const handleRowClick = (row: any) => {
  if (materialTableRef.value) {
    materialTableRef.value.toggleRowSelection(row)
  }
}

// 清空选择
const handleClear = () => {
  selectedMaterials.value = []
  if (materialTableRef.value) {
    isSettingSelection.value = true
    materialTableRef.value.clearSelection()
    isSettingSelection.value = false
  }
  console.log('清空所有选择')
  emit('selection-change', [])
}

// 关闭处理
const handleClose = () => {
  console.log('关闭物料选择器')
  visible.value = false
}

// 根据selectedIds重建selectedMaterials
const prepareSelectedMaterials = () => {
  console.log('=== prepareSelectedMaterials 开始 ===')
  console.log('props.selectedIds:', props.selectedIds)
  console.log('props.selectedIds.length:', props.selectedIds?.length)
  console.log('当前selectedMaterials.value:', selectedMaterials.value)
  
  if (!props.selectedIds || props.selectedIds.length === 0) {
    console.log('没有selectedIds，清空选择')
    selectedMaterials.value = []
  } else {
    // 保留现有的selectedMaterials，只添加新的selectedIds
    console.log('有selectedIds，准备重新构建选择')
    console.log('准备从selectedIds恢复选择，IDs:', props.selectedIds)
    
    // 如果selectedMaterials为空，说明是重新打开弹窗，需要从selectedIds重建
    if (selectedMaterials.value.length === 0) {
      console.log('selectedMaterials为空，需要从selectedIds重建')
      // 这里不清空，让loadMaterialList来处理重建
    } else {
      console.log('selectedMaterials不为空，保留现有选择')
    }
  }
  console.log('=== prepareSelectedMaterials 结束 ===')
}

// 保存初始的selectedIds，防止在异步过程中被改变
let initialSelectedIds: string[] = []

// 监听弹窗显示状态
watch(visible, async (val) => {
  if (val) {
    // 设置初始化标志，防止在初始化过程中触发selection-change事件
    isInitializing.value = true
    console.log('开始弹窗初始化')
    
    // 弹窗打开时重置搜索条件和页码，但保持分类设置
    currentPage.value = 1
    searchKeyword.value = ''
    
    // 确保分类设置正确
    if (props.category && props.category !== selectedCategory.value) {
      selectedCategory.value = props.category
      console.log('设置分类为:', props.category)
    }
    
    console.log('弹窗打开，传入的selectedIds:', props.selectedIds)
    console.log('当前selectedCategory.value:', selectedCategory.value)
    
    // 保存初始的selectedIds
    initialSelectedIds = [...props.selectedIds]
    console.log('保存初始selectedIds:', initialSelectedIds)
    
    // 准备选择状态
    prepareSelectedMaterials()
    
    // 然后加载物料列表
    await loadMaterialList()
    
    // 注意：初始化状态会在loadMaterialList中的选择恢复完成后自动设置为false
    console.log('物料列表加载完成')
  } else {
    // 弹窗关闭时的清理工作
    console.log('弹窗已关闭')
    // 不清空initialSelectedIds，保持选择状态
    isInitializing.value = false
  }
})

// 监听搜索关键词变化
watch(searchKeyword, () => {
  if (visible.value) {
    handleSearch()
  }
})

// 监听分类变化
watch(() => props.category, (newCategory) => {
  if (newCategory !== selectedCategory.value) {
    selectedCategory.value = newCategory || ''
    console.log('props.category变化，更新selectedCategory为:', newCategory)
    // 如果弹窗是打开的且分类切换未被禁用，重新加载物料列表
    if (visible.value && !props.disableCategorySwitch) {
      currentPage.value = 1
      loadMaterialList()
    }
  }
})

// 组件挂载时的调试
onMounted(() => {
  console.log('MaterialSelector 组件已挂载')
  console.log('初始 props:', props)
  console.log('初始 selectedMaterials:', selectedMaterials.value)
  console.log('初始 selectedCategory:', selectedCategory.value)
  
  // 确保分类设置正确
  if (props.category && props.category !== selectedCategory.value) {
    selectedCategory.value = props.category
    console.log('组件挂载时设置分类为:', props.category)
  }
})

/**
 * 暴露给父组件的方法
 * 父组件可以通过 ref 访问这些方法
 * 
 * 使用示例：
 * <MaterialSelector ref="materialSelectorRef" />
 * 
 * const materialSelectorRef = ref()
 * materialSelectorRef.value.loadMaterialList() // 重新加载物料列表
 * materialSelectorRef.value.getSelectedMaterials() // 获取当前选中的物料
 * materialSelectorRef.value.clearSelection() // 清空选择
 */
defineExpose({
  /** 重新加载物料列表 */
  loadMaterialList,
  /** 获取当前选中的物料列表 */
  getSelectedMaterials: () => selectedMaterials.value,
  /** 清空所有选择 */
  clearSelection: handleClear
})
</script>

<style scoped lang="scss">
.material-search {
  margin-bottom: 20px;
}
</style> 