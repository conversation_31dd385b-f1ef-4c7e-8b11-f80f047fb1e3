工程管理系统 - API 规划设计
==========================================

1. 首页相关
- GET    /api/dashboard/materials/unused         获取未使用物料统计（项数、总价、明细）
- GET    /api/dashboard/materials/used           获取已使用物料统计（项数、总价、明细）
- GET    /api/dashboard/materials/issued         获取已领出物料统计（项数、总价）
- GET    /api/dashboard/projects/status          获取工程进度统计（未开始/在建/暂停/完成数量及明细）
- GET    /api/dashboard/loose-materials          获取散单物料统计（平账时间、甲料分类统计、库存分布）

- GET    /api/dashboard/sales/statistics         获取销售统计（总销售额、订单数、平均单价、增长率）
- GET    /api/dashboard/loose-materials/stats    获取散户物料统计（申请数量、金额、增长率）

2. 仓库管理
- GET    /api/materials                          获取物料列表（支持搜索）
- POST   /api/materials/apply                    提交领料申请
- POST   /api/materials/apply/save-print         保存并打印领料申请
- POST   /api/materials/inbound                  物料入库
- POST   /api/materials/inbound/save-print       保存并打印物料入库
- POST   /api/materials/return                   物料退货
- POST   /api/materials/return/save-print        保存并打印物料退货
- POST   /api/materials/auxiliary/purchase       辅助采购
- POST   /api/materials/auxiliary/save-print     保存并打印辅助采购
- POST   /api/products/inbound                   产品入库
- POST   /api/products/inbound/save-print        保存并打印产品入库

- GET    /api/materials/records                  获取进出记录
- GET    /api/materials/prices                   获取物料价格列表
- PUT    /api/materials/prices/{id}              修改物料价格

- GET    /api/materials/base                     获取物料基础库
- POST   /api/materials/base/import              导入物料基础库（excel）
- POST   /api/materials/base                     新增物料基础信息
- PUT    /api/materials/base/{id}                编辑物料基础信息
- DELETE /api/materials/base/{id}                删除物料基础信息
- GET    /api/materials/warning                  获取库存预警列表
- PUT    /api/materials/warning/{id}             修改预警数量
- GET    /api/materials/verification             获取物料核验列表
- POST   /api/materials/verification             新增物料核验
- PUT    /api/materials/verification/{id}        编辑物料核验
- DELETE /api/materials/verification/{id}        删除物料核验

3. 散户订单
- GET    /api/loose-orders                       获取散户订单列表（支持多条件搜索）
- POST   /api/loose-orders                       新增散户订单
- PUT    /api/loose-orders/{id}                  编辑散户订单
- DELETE /api/loose-orders/{id}                  删除散户订单
- POST   /api/loose-orders/assign                订单派发（分派师傅、物料、用时）
- GET    /api/loose-orders/balance/month         获取月度平账信息
- POST   /api/loose-orders/balance/confirm       确认平账完成
- GET    /api/loose-orders/balance/records       获取平账记录列表
- GET    /api/loose-orders/balance/{id}/detail   获取平账详情
- POST   /api/loose-orders/execute                散户订单执行（提交完成时间、实际用时、物料消耗明细、备注）
- GET    /api/loose-orders/{id}/execution         获取散户订单执行详情
- PUT    /api/loose-orders/{id}/execution         编辑散户订单执行信息
- GET    /api/loose-orders/settlement             获取订单结算列表
- POST   /api/loose-orders/settlement             新增订单结算
- PUT    /api/loose-orders/settlement/{id}        编辑订单结算
- GET    /api/loose-orders/repair-settlement      获取维修结算列表
- POST   /api/loose-orders/repair-settlement      新增维修结算
- PUT    /api/loose-orders/repair-settlement/{id} 编辑维修结算
- GET    /api/loose-orders/safety-inspection      获取安检列表
- POST   /api/loose-orders/safety-inspection      新增安检记录
- PUT    /api/loose-orders/safety-inspection/{id} 编辑安检记录
- GET    /api/loose-orders/project-cost-summary   获取项目成本汇总表

4. 工程订单
- GET    /api/projects                           获取工程订单列表（支持多条件搜索）
- POST   /api/projects                           新增工程订单
- PUT    /api/projects/{id}                      编辑工程订单
- DELETE /api/projects/{id}                      删除工程订单
- GET    /api/projects/types                     获取工种设置列表
- POST   /api/projects/types                     新增工种
- PUT    /api/projects/types/{id}                编辑工种
- DELETE /api/projects/types/{id}                删除工种
- POST   /api/projects/start                     工程启动
- POST   /api/projects/progress                  工程推进（进度、人员、物料）
- POST   /api/projects/pause                     工程暂停
- POST   /api/projects/finish                    工程完工
- POST   /api/projects/costs/external            外部成本录入
- GET    /api/projects/party-dispatch             获取甲方派单列表
- POST   /api/projects/party-dispatch             新增甲方派单
- PUT    /api/projects/party-dispatch/{id}        编辑甲方派单

5. 员工管理
- GET    /api/employees                          获取员工列表
- POST   /api/employees                          新增员工
- PUT    /api/employees/{id}                     修改员工信息
- DELETE /api/employees/{id}                     删除员工
- PUT    /api/employees/{id}/work-type           修改员工工种、工价
- GET    /api/employees/types                    获取工种设置列表
- POST   /api/employees/types                    新增工种
- PUT    /api/employees/types/{id}               编辑工种
- DELETE /api/employees/types/{id}               删除工种
- POST   /api/employees/types/import             导入工种（excel）
- GET    /api/employees/performance              获取绩效参数列表
- POST   /api/employees/performance              新增绩效参数
- PUT    /api/employees/performance/{id}         修改绩效参数
- DELETE /api/employees/performance/{id}         删除绩效参数

6. 系统设置
- GET    /api/users                              获取用户列表
- POST   /api/users                              新增用户
- PUT    /api/users/{id}                         修改用户信息
- DELETE /api/users/{id}                         删除用户
- GET    /api/users/{id}/permissions             获取用户权限
- POST   /api/users/{id}/permissions             分配用户权限
- GET    /api/permissions                        获取权限列表
- POST   /api/permissions                        新增权限
- PUT    /api/permissions/{id}                   修改权限
- DELETE /api/permissions/{id}                   删除权限
- GET    /api/roles                              获取角色列表
- POST   /api/roles                              新增角色
- PUT    /api/roles/{id}                         修改角色
- DELETE /api/roles/{id}                         删除角色
- POST   /api/roles/{id}/permissions             分配角色权限
- GET    /api/logs                               获取系统日志（支持多条件搜索）
- POST   /api/data/import                        基础数据导入（物料、产品、员工等）
- GET    /api/data/import/history                获取数据导入历史
- GET    /api/data/import/{id}/detail            获取数据导入详情

7. 文件上传
- POST   /api/upload/image                       上传图片文件
- POST   /api/upload/document                    上传文档文件
- POST   /api/upload/excel                       上传Excel文件
- DELETE /api/upload/{fileId}                    删除上传文件

8. 通用接口
- GET    /api/common/departments                 获取部门列表
- GET    /api/common/suppliers                   获取供应商列表
- GET    /api/common/customers                   获取客户列表
- GET    /api/common/regions                     获取地区列表
- GET    /api/common/status-options              获取状态选项
- POST   /api/common/export                     导出数据
- POST   /api/common/print                      打印数据

API 设计说明：
1. 所有API采用RESTful风格设计
2. 使用HTTP标准方法：GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
3. 统一使用JSON格式进行数据交换
4. 支持分页查询的接口统一使用page、pageSize参数
5. 支持搜索的接口统一使用search参数
6. 文件上传接口使用multipart/form-data格式
7. 所有接口都需要进行身份验证和权限校验
8. 错误响应统一使用HTTP状态码和错误信息
9. 支持批量操作的接口使用batch前缀
10. 支持导入导出的接口使用import/export后缀 