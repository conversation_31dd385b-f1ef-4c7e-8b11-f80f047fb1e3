# 物料记录表重构说明

## 变更概述

将原来的 `material_records` 表拆分为两个表：
- `material_records` (主表)：记录基本信息
- `material_record_items` (明细表)：记录物料和数量

## 表结构对比

### 原表结构 (material_records)
```sql
CREATE TABLE material_records (
    id VARCHAR(36) PRIMARY KEY,
    material_id VARCHAR(36) NOT NULL,
    type VARCHAR(20) NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price REAL,
    total_amount REAL,
    operator_id VARCHAR(36) NOT NULL,
    order_id VARCHAR(36),
    supplier VARCHAR(100),
    batch_number VARCHAR(50),
    recipient VARCHAR(50),
    purpose TEXT,
    remarks TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 新表结构

#### material_records (主表)
```sql
CREATE TABLE material_records (
    id VARCHAR(36) PRIMARY KEY,
    type VARCHAR(20) NOT NULL,
    operator_id VARCHAR(36) NOT NULL,
    order_id VARCHAR(36),
    supplier VARCHAR(100),
    batch_number VARCHAR(50),
    recipient VARCHAR(50),
    purpose TEXT,
    remarks TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### material_record_items (明细表)
```sql
CREATE TABLE material_record_items (
    id VARCHAR(36) PRIMARY KEY,
    record_id VARCHAR(36) NOT NULL,
    material_id VARCHAR(36) NOT NULL,
    quantity INTEGER NOT NULL,
    current_quantity INTEGER NOT NULL,
    unit_price REAL,
    total_amount REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 变更原因

1. **数据规范化**：将物料信息与记录信息分离
2. **支持批量操作**：一个记录可以包含多个物料
3. **提高查询效率**：可以独立查询记录信息和物料信息
4. **减少数据冗余**：避免重复存储相同的基本信息

## 索引设计

### material_records 索引
- `idx_material_records_type`: 按类型查询
- `idx_material_records_operator`: 按操作员查询
- `idx_material_records_created`: 按创建时间查询

### material_record_items 索引
- `idx_material_record_items_record`: 按记录ID查询
- `idx_material_record_items_material`: 按物料ID查询

## 代码变更

### 批量入库功能更新
原来的代码：
```javascript
// 写入入库日志
await execute(
    `INSERT INTO material_records (id, material_id, type, quantity, remarks, operator_id, created_at) VALUES (?, ?, 'in', ?, ?, ?, datetime('now'))`,
    [generatePrefixedId('mr'), item.id, parseInt(item.quantity) || 0, remark || '', req.user.id]
);
```

更新后的代码：
```javascript
// 创建入库记录主表
const recordId = generatePrefixedId('mr');
await execute(
    `INSERT INTO material_records (id, type, operator_id, remarks, created_at) VALUES (?, 'in', ?, ?, datetime('now'))`,
    [recordId, req.user.id, remark || '']
);

// 获取当前库存并写入入库明细
const currentStockSql = 'SELECT stock_quantity FROM materials WHERE id = ?';
const currentStockResult = await query(currentStockSql, [item.id]);
const currentQuantity = currentStockResult[0] && currentStockResult[0].stock_quantity ? currentStockResult[0].stock_quantity : 0;

await execute(
    `INSERT INTO material_record_items (id, record_id, material_id, quantity, current_quantity, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))`,
    [generatePrefixedId('mri'), recordId, item.id, parseInt(item.quantity) || 0, currentQuantity]
);
```

## 数据迁移

如果需要从旧表迁移数据，可以使用以下SQL：

```sql
-- 迁移主表数据
INSERT INTO material_records (id, type, operator_id, order_id, supplier, batch_number, recipient, purpose, remarks, created_at)
SELECT 
    id,
    type,
    operator_id,
    order_id,
    supplier,
    batch_number,
    recipient,
    purpose,
    remarks,
    created_at
FROM material_records_old;

-- 迁移明细表数据
INSERT INTO material_record_items (id, record_id, material_id, quantity, unit_price, total_amount, created_at)
SELECT 
    generatePrefixedId('mri'),
    id as record_id,
    material_id,
    quantity,
    unit_price,
    total_amount,
    created_at
FROM material_records_old;
```

## 应用步骤

1. **备份数据库**：
   ```bash
   cp database/suijian.db database/suijian.db.backup
   ```

2. **执行更新脚本**：
   ```bash
   cd suijian-backend
   node scripts/update_material_records.js
   ```

3. **验证表结构**：
   ```bash
   sqlite3 database/suijian.db ".schema material_records"
   sqlite3 database/suijian.db ".schema material_record_items"
   ```

4. **重启服务**：
   ```bash
   npm run dev
   ```

## 注意事项

1. **无外键约束**：按照要求，新表结构不包含外键约束
2. **事务处理**：批量入库操作使用事务确保数据一致性
3. **向后兼容**：新的API接口保持向后兼容
4. **数据完整性**：通过应用层逻辑确保数据完整性

## 查询示例

### 查询入库记录及明细
```sql
SELECT 
    mr.id,
    mr.type,
    mr.operator_id,
    mr.remarks,
    mr.created_at,
    mri.material_id,
    mri.quantity,
    mri.current_quantity,
    m.name as material_name
FROM material_records mr
LEFT JOIN material_record_items mri ON mr.id = mri.record_id
LEFT JOIN materials m ON mri.material_id = m.id
WHERE mr.type = 'in'
ORDER BY mr.created_at DESC;
```

### 查询特定物料的出入库记录
```sql
SELECT 
    mr.id,
    mr.type,
    mr.operator_id,
    mr.created_at,
    mri.quantity,
    mri.current_quantity
FROM material_records mr
JOIN material_record_items mri ON mr.id = mri.record_id
WHERE mri.material_id = 'material-xxx'
ORDER BY mr.created_at DESC;
``` 