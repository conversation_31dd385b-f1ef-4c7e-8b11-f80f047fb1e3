#!/usr/bin/env node

/**
 * 测试智能列映射功能
 */

// 模拟Excel数据
const testHeaders = [
    '新显示', '类别', '子类别', '时间', '状态', '类型', '数量', '金额',
    '描述', '操作', '结果', '备注', '用户', 'IP', '设备', '版本', '来源'
];

const testData = [
    ['1', '维修', '设备', '2024-05-12 10:11:01', '已完成', '维修', '1', '100',
        '设备无法启动', '已完成', '成功', '正常', '张三', '***********', '设备A', 'v1.0', '系统'
    ],
    ['2', '安装', '新设备', '2024-05-12 10:12:01', '进行中', '安装', '2', '200',
        '安装新设备', '进行中', '进行中', '正常', '李四', '***********', '设备B', 'v1.1', '项目'
    ]
];

// 智能列映射逻辑
const columnMapping = {
    '工单号': ['工单号', '订单号', '编号', 'ID', '序号', '新显示'],
    '工单类型': ['工单类型', '类型', 'Type', '订单类型'],
    '工单状态': ['工单状态', '状态', 'Status', '订单状态'],
    '工单来源': ['工单来源', '来源', 'Source', '订单来源'],
    '工单标题': ['工单标题', '标题', 'Title', '订单标题'],
    '工单内容': ['工单内容', '内容', '描述', 'Description', '订单内容'],
    '创建人': ['创建人', '用户', 'User', '处理人', '负责人'],
    '创建时间': ['创建时间', '时间', 'Time', '下单时间', '创建日期']
};

function testColumnMapping() {
    console.log('🧪 测试智能列映射功能');
    console.log('='.repeat(50));

    console.log('📋 测试表头:', testHeaders);
    console.log('');

    // 查找匹配的列
    const foundColumns = {};
    const missingColumns = [];

    for (const [requiredCol, possibleNames] of Object.entries(columnMapping)) {
        const foundCol = testHeaders.find(header =>
            possibleNames.some(name =>
                header.toLowerCase().includes(name.toLowerCase()) ||
                name.toLowerCase().includes(header.toLowerCase())
            )
        );

        if (foundCol) {
            foundColumns[requiredCol] = foundCol;
            console.log(`✅ ${requiredCol} -> ${foundCol}`);
        } else {
            missingColumns.push(requiredCol);
            console.log(`❌ ${requiredCol} -> 未找到匹配列`);
        }
    }

    console.log('');
    console.log('📊 映射结果:');
    console.log('找到的列:', foundColumns);
    console.log('缺失的列:', missingColumns);

    // 检查工单号列
    const hasOrderNo = foundColumns['工单号'] || testHeaders.includes('工单号');

    if (!hasOrderNo) {
        console.log('❌ 缺少工单号列');
    } else {
        console.log('✅ 工单号列已找到');
    }

    return { foundColumns, missingColumns, hasOrderNo };
}

function testDataMapping(foundColumns) {
    console.log('\n📝 测试数据映射:');
    console.log('='.repeat(30));

    testData.forEach((row, index) => {
        console.log(`\n第${index + 1}行数据:`);

        // 使用智能映射获取数据，只有工单号是必需的
        const orderNo = foundColumns['工单号'] ? row[testHeaders.indexOf(foundColumns['工单号'])] || '' :
            testHeaders.includes('工单号') ? row[testHeaders.indexOf('工单号')] || '' :
            `WO${Date.now()}_${index + 1}`;

        // 其他字段都提供默认值
        const orderType = foundColumns['工单类型'] ? row[testHeaders.indexOf(foundColumns['工单类型'])] || '' : '普通工单';
        const status = foundColumns['工单状态'] ? row[testHeaders.indexOf(foundColumns['工单状态'])] || '' : '待处理';
        const orderSource = foundColumns['工单来源'] ? row[testHeaders.indexOf(foundColumns['工单来源'])] || '' : '系统导入';
        const orderContent = foundColumns['工单内容'] ? row[testHeaders.indexOf(foundColumns['工单内容'])] || '' : '从Excel导入的工单';
        const orderTitle = foundColumns['工单标题'] ? row[testHeaders.indexOf(foundColumns['工单标题'])] || '' :
            orderContent ? orderContent.substring(0, 50) : `工单${orderNo}`;
        const creator = foundColumns['创建人'] ? row[testHeaders.indexOf(foundColumns['创建人'])] || '' : '系统';
        const createTime = foundColumns['创建时间'] ? row[testHeaders.indexOf(foundColumns['创建时间'])] || '' : new Date().toISOString();

        console.log(`  工单号: ${orderNo}`);
        console.log(`  工单类型: ${orderType}`);
        console.log(`  工单状态: ${status}`);
        console.log(`  工单来源: ${orderSource}`);
        console.log(`  工单标题: ${orderTitle}`);
        console.log(`  工单内容: ${orderContent}`);
        console.log(`  创建人: ${creator}`);
        console.log(`  创建时间: ${createTime}`);
    });
}

// 主函数
function main() {
    const { foundColumns, missingColumns, hasOrderNo } = testColumnMapping();

    if (hasOrderNo) {
        testDataMapping(foundColumns);
        console.log('\n✅ 智能列映射测试通过！');
    } else {
        console.log('\n❌ 智能列映射测试失败，缺少工单号列');
    }
}

// 运行测试
if (require.main === module) {
    main();
}

module.exports = { testColumnMapping, testDataMapping };