#!/usr/bin/env node

/**
 * 大文件上传测试脚本
 * 用于测试200MB文件的上传功能
 */

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

// 配置
const API_BASE_URL = 'http://localhost:3000';
const TEST_FILE_SIZE = 50 * 1024 * 1024; // 50MB测试文件
const UPLOAD_ENDPOINT = '/api/loose-orders/import';

/**
 * 创建测试文件
 */
function createTestFile(filePath, size) {
    console.log(`📁 创建测试文件: ${filePath} (${(size / 1024 / 1024).toFixed(2)}MB)`);

    const buffer = Buffer.alloc(size);
    // 填充一些测试数据
    for (let i = 0; i < size; i++) {
        buffer[i] = Math.floor(Math.random() * 256);
    }

    fs.writeFileSync(filePath, buffer);
    console.log('✅ 测试文件创建完成');
}

/**
 * 测试文件上传
 */
async function testFileUpload(filePath) {
    console.log('\n🚀 开始测试文件上传...');

    try {
        const formData = new FormData();
        formData.append('file', fs.createReadStream(filePath), {
            filename: path.basename(filePath),
            contentType: 'application/vnd.ms-excel'
        });

        const startTime = Date.now();

        const response = await axios.post(`${API_BASE_URL}${UPLOAD_ENDPOINT}`, formData, {
            headers: {
                ...formData.getHeaders(),
                'Authorization': 'Bearer test-token'
            },
            timeout: 600000, // 10分钟超时
            maxContentLength: Infinity,
            maxBodyLength: Infinity,
            onUploadProgress: (progressEvent) => {
                if (progressEvent.total) {
                    const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                    const elapsed = Date.now() - startTime;
                    const speed = (progressEvent.loaded / 1024 / 1024 / (elapsed / 1000)).toFixed(2);
                    console.log(`📤 上传进度: ${percentCompleted}% (${speed} MB/s)`);
                }
            }
        });

        const endTime = Date.now();
        const duration = endTime - startTime;

        console.log('\n✅ 上传测试完成');
        console.log(`⏱️  总耗时: ${duration}ms (${(duration / 1000).toFixed(2)}s)`);
        console.log(`📊 平均速度: ${(TEST_FILE_SIZE / 1024 / 1024 / (duration / 1000)).toFixed(2)} MB/s`);
        console.log('📄 响应数据:', response.data);

    } catch (error) {
        console.error('\n❌ 上传测试失败');
        console.error('错误类型:', error.code || 'Unknown');
        console.error('错误消息:', error.message);

        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }

        if (error.request) {
            console.error('请求错误:', error.request);
        }
    }
}

/**
 * 测试服务器连接
 */
async function testServerConnection() {
    console.log('🔍 测试服务器连接...');

    try {
        const response = await axios.get(`${API_BASE_URL}/api/health`, {
            timeout: 5000
        });
        console.log('✅ 服务器连接正常');
        return true;
    } catch (error) {
        console.error('❌ 服务器连接失败:', error.message);
        return false;
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🧪 大文件上传测试工具');
    console.log('='.repeat(50));

    // 检查服务器连接
    const serverConnected = await testServerConnection();
    if (!serverConnected) {
        console.log('\n❌ 无法连接到服务器，请确保服务器正在运行');
        return;
    }

    // 创建测试文件
    const testFilePath = path.join(__dirname, '../uploads/test_large_file.xls');
    const uploadDir = path.dirname(testFilePath);

    if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
    }

    if (!fs.existsSync(testFilePath)) {
        createTestFile(testFilePath, TEST_FILE_SIZE);
    } else {
        console.log('📁 使用现有测试文件');
    }

    // 测试上传
    await testFileUpload(testFilePath);

    console.log('\n📝 测试完成');
    console.log('💡 提示:');
    console.log('  - 如果上传成功，说明大文件上传功能正常');
    console.log('  - 如果上传失败，请检查服务器配置和网络连接');
    console.log('  - 可以调整 TEST_FILE_SIZE 来测试不同大小的文件');
}

// 运行主函数
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    createTestFile,
    testFileUpload,
    testServerConnection
};