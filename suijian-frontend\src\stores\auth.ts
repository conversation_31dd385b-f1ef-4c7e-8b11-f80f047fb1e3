import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import request from '@/utils/request'

export interface User {
  id: number
  username: string
  role: string
  phone?: string
  email?: string
}

export const useAuthStore = defineStore('auth', () => {
  // 从 localStorage 初始化状态
  const token = ref<string>(localStorage.getItem('auth_token') || '')
  const user = ref<User | null>(null)
  const loading = ref(false)

  // 初始化用户信息
  const initUser = () => {
    const userStr = localStorage.getItem('auth_user')
    if (userStr) {
      try {
        user.value = JSON.parse(userStr)
      } catch (error) {
        console.error('Failed to parse user data:', error)
        localStorage.removeItem('auth_user')
      }
    }
  }

  // 初始化时加载用户信息
  initUser()

  const isAuthenticated = computed(() => !!token.value)

  const login = async (username: string, password: string) => {
    loading.value = true
    try {
      // 使用request工具，登录接口会使用真实后端API
      const data = await request.post('/api/auth/login', { username, password })
      
      // 后端API返回格式：{ success: true, message: '登录成功', data: { token, user } }
      if (data.success) {
        token.value = data.data.token
        user.value = data.data.user
        
        // 保存到 localStorage
        localStorage.setItem('auth_token', data.data.token)
        localStorage.setItem('auth_user', JSON.stringify(data.data.user))
        
        return { success: true }
      } else {
        return { success: false, message: data.message || '登录失败' }
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, message: '网络错误' }
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    token.value = ''
    user.value = null
    
    // 清除 localStorage
    localStorage.removeItem('auth_token')
    localStorage.removeItem('auth_user')
  }

  const checkAuth = async () => {
    if (!token.value) return false
    
    try {
      const data = await request.get('/api/auth/check')
      
      if (data.code === 200) {
        user.value = data.data.user
        // 更新 localStorage 中的用户信息
        localStorage.setItem('auth_user', JSON.stringify(data.data.user))
        return true
      } else {
        logout()
        return false
      }
    } catch (error) {
      console.error('Check auth error:', error)
      logout()
      return false
    }
  }

  return {
    token,
    user,
    loading,
    isAuthenticated,
    login,
    logout,
    checkAuth
  }
}) 