# 工单列表Excel导入模板说明

## 文件格式要求
- 文件格式：`.xlsx` 或 `.xls`
- 文件大小：不超过200MB
- 编码格式：UTF-8

## 必要列说明

Excel文件必须包含以下列（列名必须完全匹配）：

| 列名 | 说明 | 是否必填 | 示例 |
|------|------|----------|------|
| 工单号 | 工单的唯一标识号 | 是 | 202405100001 |
| 工单类型 | 工单的类型分类 | 是 | 日常工单 |
| 工单状态 | 工单的当前状态 | 是 | 已完成 |
| 工单来源 | 工单的来源渠道 | 是 | APP |
| 工单标题 | 工单的标题 | 是 | 测试工单 |
| 工单内容 | 工单的详细内容 | 是 | 测试工单内容 |
| 创建人 | 工单的创建人 | 是 | 张三 |
| 创建时间 | 工单的创建时间 | 是 | 2024-05-10 10:00:00 |

## 可选列说明

以下列为可选列，如果存在会被导入到相应字段：

| 列名 | 说明 | 对应数据库字段 |
|------|------|----------------|
| 处理人 | 工单的处理人 | contact_person |
| 处理时间 | 工单的处理时间 | - |
| 完成时间 | 工单的完成时间 | - |
| 所属部门 | 工单所属部门 | customer_code |
| 所属区域 | 工单所属区域 | 用于构建address |
| 所属公司 | 工单所属公司 | 用于构建address |
| 所属项目 | 工单所属项目 | project_name |
| 所属设备 | 工单所属设备 | 用于构建address |
| 所属系统 | 工单所属系统 | 用于构建address |
| 所属模块 | 工单所属模块 | 用于构建address |
| 所属功能 | 工单所属功能 | 用于构建address |
| 所属页面 | 工单所属页面 | 用于构建address |
| 所属接口 | 工单所属接口 | 用于构建address |
| 所属数据 | 工单所属数据 | 用于构建address |
| 所属文件 | 工单所属文件 | 用于构建address |
| 所属图片 | 工单所属图片 | 用于构建address |
| 所属视频 | 工单所属视频 | 用于构建address |
| 所属音频 | 工单所属音频 | 用于构建address |
| 所属其他 | 工单所属其他 | 用于构建address |
| 备注 | 工单备注信息 | remarks |

## 导入逻辑说明

### 1. 重复检查
- 系统会根据`工单号`检查是否已存在相同工单
- 如果工单号已存在，该条记录会被跳过，不会重复导入

### 2. 字段映射
- `工单号` → `order_no`
- `工单类型` → `order_type`
- `工单状态` → `status`
- `创建人` → `customer_name`
- `工单内容` → `appeal_description`（诉求描述）
- `所属项目` → `project_name`
- `备注` → `remarks`

### 3. 地址构建
系统会将以下字段组合构建地址信息：
- 所属区域
- 所属公司
- 所属项目
- 所属设备
- 所属系统
- 所属模块
- 所属功能
- 所属页面
- 所属接口
- 所属数据
- 所属文件
- 所属图片
- 所属视频
- 所属音频
- 所属其他

### 4. 批次管理
- 每次导入会生成唯一的`batch_id`
- 同一批次导入的数据具有相同的`batch_id`
- 便于后续的数据管理和查询

## 示例数据

| 工单号 | 工单类型 | 工单状态 | 工单来源 | 工单标题 | 工单内容 | 创建人 | 创建时间 | 处理人 | 所属部门 | 所属区域 | 所属公司 | 所属项目 | 备注 |
|--------|----------|----------|----------|----------|----------|--------|----------|--------|----------|----------|----------|----------|------|
| 202405100001 | 日常工单 | 已完成 | APP | 测试工单 | 测试工单内容 | 张三 | 2024-05-10 10:00:00 | 李四 | 研发部 | 华东区 | 总公司 | 项目A | 备注内容 |

## 导入结果说明

导入完成后，系统会返回以下信息：
- `total`: 总记录数
- `success`: 成功导入的记录数
- `skip`: 跳过的记录数（工单号重复）
- `error`: 导入失败的记录数
- `errors`: 错误详情列表（最多显示前10个错误）
- `batchId`: 批次ID

## 注意事项

1. **列名必须完全匹配**：Excel中的列名必须与模板中的列名完全一致
2. **数据格式**：日期时间格式建议使用 `YYYY-MM-DD HH:MM:SS`
3. **编码问题**：确保Excel文件使用UTF-8编码，避免中文乱码
4. **数据验证**：系统会对必要字段进行验证，缺失必要字段的记录会被标记为错误
5. **性能考虑**：建议单次导入不超过1000条记录，以保证导入性能 