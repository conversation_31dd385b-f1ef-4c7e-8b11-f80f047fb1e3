const multer = require('multer');
const path = require('path');
const fs = require('fs');
const Response = require('../utils/response');

// 确保上传目录存在
const uploadDir = path.join(process.cwd(), 'uploads');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置存储
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        let uploadPath = uploadDir;

        // 根据文件类型选择不同的目录
        if (file.fieldname === 'excel') {
            uploadPath = path.join(uploadDir, 'excel');
        } else if (file.fieldname === 'pdf') {
            uploadPath = path.join(uploadDir, 'pdf');
        } else if (file.fieldname === 'image') {
            uploadPath = path.join(uploadDir, 'images');
        } else {
            uploadPath = path.join(uploadDir, 'temp');
        }

        // 确保目录存在
        if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true });
        }

        cb(null, uploadPath);
    },
    filename: (req, file, cb) => {
        // 生成唯一文件名
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname);
        cb(null, file.fieldname + '-' + uniqueSuffix + ext);
    }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
    const allowedTypes = process.env.ALLOWED_FILE_TYPES ? process.env.ALLOWED_FILE_TYPES.split(',') : [
        'jpg', 'jpeg', 'png', 'gif', 'pdf', 'xlsx', 'xls', 'doc', 'docx'
    ];

    const fileExt = path.extname(file.originalname).toLowerCase().substring(1);

    if (allowedTypes.includes(fileExt)) {
        cb(null, true);
    } else {
        cb(new Error(`不支持的文件类型: ${fileExt}`), false);
    }
};

// 创建multer实例
const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: parseInt(process.env.MAX_FILE_SIZE) || 200 * 1024 * 1024, // 默认200MB
        files: 1 // 一次只能上传一个文件
    }
});

// 单文件上传中间件
const uploadSingle = (fieldName) => {
    return upload.single(fieldName);
};

// 多文件上传中间件
const uploadMultiple = (fieldName, maxCount = 5) => {
    return upload.array(fieldName, maxCount);
};

// 错误处理中间件
const handleUploadError = (error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return Response.error(res, '文件大小超出限制（最大200MB）', 400);
        } else if (error.code === 'LIMIT_FILE_COUNT') {
            return Response.error(res, '文件数量超出限制', 400);
        } else if (error.code === 'LIMIT_UNEXPECTED_FILE') {
            return Response.error(res, '不支持的文件类型', 400);
        }
    } else if (error.message.includes('不支持的文件类型')) {
        return Response.error(res, error.message, 400);
    }

    next(error);
};

// 特定文件类型上传中间件
const uploadImage = uploadSingle('image');
const uploadExcel = uploadSingle('excel');
const uploadPdf = uploadSingle('pdf');
const uploadDocument = uploadSingle('document');

module.exports = {
    upload,
    uploadSingle,
    uploadMultiple,
    uploadImage,
    uploadExcel,
    uploadPdf,
    uploadDocument,
    handleUploadError
};