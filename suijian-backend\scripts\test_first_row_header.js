const path = require('path');
const fs = require('fs');
const excel = require('../src/utils/excel');

/**
 * 测试第一行固定作为标题的功能
 */
async function testFirstRowHeader() {
    console.log('=== 测试第一行固定作为标题功能 ===');

    try {
        // 查找测试用的Excel文件
        const uploadsDir = path.join(__dirname, '../src/uploads/excel');
        const files = fs.readdirSync(uploadsDir).filter(file =>
            file.endsWith('.xlsx') || file.endsWith('.xls')
        );

        if (files.length === 0) {
            console.log('❌ 未找到测试用的Excel文件');
            return;
        }

        const testFile = path.join(uploadsDir, files[0]);
        console.log(`📁 使用测试文件: ${files[0]}`);

        // 读取Excel文件
        console.log('📖 开始读取Excel文件...');
        const workbook = await excel.readWorkbook(testFile);

        // 获取第一个工作表
        let worksheet, sheetName;
        if (workbook.worksheets) {
            // ExcelJS格式
            worksheet = workbook.worksheets[0];
            sheetName = worksheet.name;
        } else {
            // XLSX格式
            const sheetNames = workbook.SheetNames;
            sheetName = sheetNames[0];
            worksheet = workbook.Sheets[sheetName];
        }

        console.log(`📋 工作表名称: ${sheetName}`);

        // 转换为JSON数组（包含所有行）
        console.log('🔄 转换为JSON数组...');
        const data = excel.sheet_to_json(worksheet, { header: 1 });

        console.log(`📊 总行数: ${data.length}`);

        if (data.length === 0) {
            console.log('❌ Excel文件为空');
            return;
        }

        // 验证第一行作为标题
        const headers = data[0];
        const dataRows = data.slice(1);

        console.log('\n📋 第一行（标题行）:');
        console.log('='.repeat(50));
        headers.forEach((header, index) => {
            console.log(`${index + 1}. ${header || '(空)'}`);
        });

        console.log(`\n📊 数据行数: ${dataRows.length}`);

        if (dataRows.length > 0) {
            console.log('\n📝 前3行数据示例:');
            console.log('='.repeat(50));
            dataRows.slice(0, 3).forEach((row, index) => {
                console.log(`第${index + 2}行: [${row.map(cell => cell || '(空)').join(', ')}]`);
            });
        }

        // 验证标题行有效性
        const nonEmptyHeaders = headers.filter(h => h && h.toString().trim() !== '');
        console.log(`\n✅ 有效标题列数: ${nonEmptyHeaders.length}/${headers.length}`);

        if (nonEmptyHeaders.length === 0) {
            console.log('⚠️  警告: 标题行中没有有效的列标题');
        } else {
            console.log('✅ 标题行验证通过');
        }

        // 测试列映射
        console.log('\n🔍 测试列映射:');
        console.log('='.repeat(50));

        const columnMapping = {
            '工单号': ['工单号', '订单号', '编号', 'ID', '序号'],
            '工单类型': ['工单类别', '类型', 'Type', '订单类型'],
            '工单状态': ['工单状态', '状态', 'Status', '订单状态'],
            '工单来源': ['工单来源', '来源', 'Source', '订单来源'],
            '工单标题': ['工单标题', '标题', 'Title', '订单标题'],
            '工单内容': ['工单内容', '内容', '描述', 'Description', '订单内容'],
            '创建人': ['创建人', '用户', 'User', '处理人', '负责人'],
            '创建时间': ['创建时间', '时间', 'Time', '下单时间', '创建日期']
        };

        const foundColumns = {};
        const missingColumns = [];

        for (const [requiredCol, possibleNames] of Object.entries(columnMapping)) {
            const foundCol = headers.find(header =>
                possibleNames.some(name =>
                    header && header.toString().toLowerCase().includes(name.toLowerCase()) ||
                    name.toLowerCase().includes(header.toString().toLowerCase())
                )
            );

            if (foundCol) {
                foundColumns[requiredCol] = foundCol;
                console.log(`✅ ${requiredCol} -> ${foundCol}`);
            } else {
                missingColumns.push(requiredCol);
                console.log(`❌ ${requiredCol} -> 未找到匹配列`);
            }
        }

        console.log(`\n📊 映射结果: 找到 ${Object.keys(foundColumns).length} 个匹配列，缺失 ${missingColumns.length} 个`);

        // 检查是否有工单号列
        const hasOrderNo = foundColumns['工单号'] || headers.includes('工单号');
        if (hasOrderNo) {
            console.log('✅ 找到工单号列，可以继续导入');
        } else {
            console.log('❌ 未找到工单号列，导入可能失败');
        }

        console.log('\n=== 测试完成 ===');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error(error.stack);
    }
}

// 运行测试
if (require.main === module) {
    testFirstRowHeader();
}

module.exports = { testFirstRowHeader };