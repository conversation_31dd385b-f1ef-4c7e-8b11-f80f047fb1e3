{"name": "suijian-backend", "version": "1.0.0", "description": "工程管理系统后端API服务", "main": "src/app.js", "scripts": {"start": "node --max-old-space-size=4096 src/app.js", "dev": "nodemon --max-old-space-size=4096 src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/"}, "keywords": ["engineering", "management", "api", "express", "nodejs"], "author": "Engineering Team", "license": "MIT", "dependencies": {"axios": "^1.11.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "form-data": "^4.0.4", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^2.0.0-rc.3", "pdf-lib": "^1.17.1", "sqlite3": "^5.1.6", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1", "winston": "^3.11.0", "xlsx": "^0.18.5"}, "devDependencies": {"eslint": "^9.0.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "supertest": "^7.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}