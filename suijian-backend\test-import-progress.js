const progressTracker = require('./src/utils/importProgress');

async function testImportProgress() {
    console.log('测试导入进度跟踪功能...');

    try {
        const batchId = 'test-batch-' + Date.now();
        const totalRows = 1000;

        console.log('1. 创建导入任务...');
        const task = progressTracker.createTask(batchId, totalRows);
        console.log('✓ 任务创建成功:', {
            batchId: task.batchId,
            totalRows: task.totalRows,
            status: task.status,
            currentPhase: task.currentPhase
        });

        console.log('2. 模拟文件读取阶段...');
        progressTracker.updateProgress(batchId, {
            currentPhase: 'reading',
            phaseProgress: 10
        });
        await sleep(1000);

        console.log('3. 模拟数据处理阶段...');
        for (let i = 0; i <= 10; i++) {
            const processedRows = i * 100;
            const progress = Math.round((processedRows / totalRows) * 60) + 20; // 20-80%

            progressTracker.updateProgress(batchId, {
                processedRows,
                successCount: processedRows * 0.9,
                skipCount: processedRows * 0.05,
                errorCount: processedRows * 0.05,
                currentPhase: 'processing',
                phaseProgress: progress
            });

            console.log(`  处理进度: ${processedRows}/${totalRows} (${progress}%)`);
            await sleep(500);
        }

        console.log('4. 模拟数据插入阶段...');
        progressTracker.updateProgress(batchId, {
            currentPhase: 'inserting',
            phaseProgress: 80
        });
        await sleep(1000);

        for (let i = 0; i <= 5; i++) {
            const progress = 80 + Math.round((i / 5) * 20); // 80-100%
            progressTracker.updateProgress(batchId, {
                phaseProgress: progress
            });

            console.log(`  插入进度: ${i}/5 (${progress}%)`);
            await sleep(300);
        }

        console.log('5. 完成任务...');
        progressTracker.completeTask(batchId, 'completed');

        console.log('6. 查询最终进度...');
        const finalProgress = progressTracker.getProgress(batchId);
        console.log('✓ 最终进度:', {
            batchId: finalProgress.batchId,
            status: finalProgress.status,
            totalRows: finalProgress.totalRows,
            processedRows: finalProgress.processedRows,
            successCount: finalProgress.successCount,
            skipCount: finalProgress.skipCount,
            errorCount: finalProgress.errorCount,
            currentPhase: finalProgress.currentPhase,
            phaseProgress: finalProgress.phaseProgress,
            startTime: finalProgress.startTime,
            endTime: finalProgress.endTime
        });

        console.log('7. 测试错误处理...');
        const errorBatchId = 'error-batch-' + Date.now();
        progressTracker.createTask(errorBatchId, 100);
        progressTracker.addError(errorBatchId, {
            message: '测试错误',
            rowNumber: 50,
            timestamp: new Date().toISOString()
        });
        progressTracker.completeTask(errorBatchId, 'failed');

        const errorProgress = progressTracker.getProgress(errorBatchId);
        console.log('✓ 错误任务:', {
            status: errorProgress.status,
            errors: errorProgress.errors
        });

        console.log('8. 获取所有任务...');
        const allTasks = progressTracker.getAllTasks();
        console.log(`✓ 总共有 ${allTasks.length} 个任务`);

        console.log('\n🎉 导入进度跟踪测试通过！');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('错误详情:', error);
    }
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 运行测试
testImportProgress().then(() => {
    console.log('测试完成');
    process.exit(0);
}).catch(error => {
    console.error('测试异常:', error);
    process.exit(1);
});