<template>
  <div class="new-project-entry">
    <el-card class="form-card">
      
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="project-form"
      >
        <!-- 基本信息 -->
        <el-divider content-position="left">基本信息</el-divider>
        
        <el-form-item label="合同名" prop="contractName">
          <el-input
            v-model="formData.contractName"
            placeholder="请输入合同名称"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="派单日期" prop="orderDate">
              <el-date-picker
                v-model="formData.orderDate"
                type="date"
                placeholder="选择派单日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同编码" prop="contractCode">
              <el-input
                v-model="formData.contractCode"
                placeholder="请输入合同编码"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="CEA编号" prop="ceaNumber">
              <el-input
                v-model="formData.ceaNumber"
                placeholder="请输入CEA编号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="施工单位" prop="constructionUnit">
              <el-input
                v-model="formData.constructionUnit"
                placeholder="请输入施工单位"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="监理单位" prop="supervisionUnit">
              <el-input
                v-model="formData.supervisionUnit"
                placeholder="请输入监理单位"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设计单位" prop="designUnit">
              <el-select
                v-model="formData.designUnit"
                placeholder="请选择设计单位"
                style="width: 100%"
              >
                <el-option label="长春燃气热力设计院" value="changchun" />
                <el-option label="上海燃气工程设计研究有限公司" value="shanghai" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="工程名称" prop="projectName">
          <el-input
            v-model="formData.projectName"
            placeholder="请输入工程名称"
          />
        </el-form-item>
        
        <el-form-item label="工程地点" prop="projectLocation">
          <el-input
            v-model="formData.projectLocation"
            placeholder="请输入工程地点"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input
                v-model="formData.contactPerson"
                placeholder="请输入联系人姓名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input
                v-model="formData.contactPhone"
                placeholder="请输入联系电话"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开工日期" prop="startDate">
              <el-date-picker
                v-model="formData.startDate"
                type="date"
                placeholder="选择开工日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="完工日期" prop="completionDate">
              <el-date-picker
                v-model="formData.completionDate"
                type="date"
                placeholder="选择完工日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 服务清单 -->
        <el-divider content-position="left">服务清单</el-divider>
        
        <div class="service-list">
          <div class="service-header">
            <el-button type="primary" @click="addServiceItem">
              <el-icon><Plus /></el-icon>
              添加服务项
            </el-button>
          </div>
          
          <el-table :data="formData.serviceList" border style="width: 100%">
            <el-table-column label="行号" width="80">
              <template #default="{ $index }">
                {{ ($index + 1) * 10 }}
              </template>
            </el-table-column>
            <el-table-column label="服务名称" min-width="200">
              <template #default="{ row }">
                <el-input v-model="row.serviceName" placeholder="服务名称" />
              </template>
            </el-table-column>
            <el-table-column label="规格" width="120">
              <template #default="{ row }">
                <el-input v-model="row.specification" placeholder="规格" />
              </template>
            </el-table-column>
            <el-table-column label="型号" width="120">
              <template #default="{ row }">
                <el-input v-model="row.model" placeholder="型号" />
              </template>
            </el-table-column>
            <el-table-column label="数量" width="120">
              <template #default="{ row }">
                <el-input-number
                  v-model="row.quantity"
                  :min="0"
                  :precision="2"
                  controls-position="right"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column label="单位" width="100">
              <template #default="{ row }">
                <el-select v-model="row.unit" placeholder="单位" style="width: 100%">
                  <el-option label="套" value="set" />
                  <el-option label="件" value="piece" />
                  <el-option label="米" value="meter" />
                  <el-option label="口" value="mouth" />
                  <el-option label="台" value="unit" />
                  <el-option label="只" value="only" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="{ $index }">
                <el-button
                  type="danger"
                  size="small"
                  @click="removeServiceItem($index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <!-- 备注信息 -->
        <el-divider content-position="left">备注信息</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设计" prop="designer">
              <el-input
                v-model="formData.designer"
                placeholder="请输入设计人员"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工商负责人" prop="businessManager">
              <el-input
                v-model="formData.businessManager"
                placeholder="请输入工商负责人"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="质量要求" prop="qualityRequirements">
          <el-input
            v-model="formData.qualityRequirements"
            type="textarea"
            :rows="3"
            placeholder="请输入质量要求"
          />
        </el-form-item>
        
        <el-form-item label="注意事项" prop="precautions">
          <el-input
            v-model="formData.precautions"
            type="textarea"
            :rows="4"
            placeholder="请输入注意事项"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="报" prop="reportTo">
              <el-input
                v-model="formData.reportTo"
                placeholder="报"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="抄" prop="copyTo">
              <el-input
                v-model="formData.copyTo"
                placeholder="抄"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="送" prop="sendTo">
              <el-input
                v-model="formData.sendTo"
                placeholder="送"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="签收人" prop="signee">
          <el-input
            v-model="formData.signee"
            placeholder="请输入签收人"
          />
        </el-form-item>
        
        <!-- 合同图片上传 -->
        <el-divider content-position="left">合同图片上传</el-divider>
        
        <el-form-item label="合同图片">
          <el-upload
            ref="uploadRef"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :file-list="fileList"
            :limit="1"
            accept="image/*"
            list-type="picture-card"
            :auto-upload="false"
          >
            <el-icon><Plus /></el-icon>
            <template #tip>
              <div class="el-upload__tip">
                支持jpg/png格式，文件大小不超过10MB
              </div>
            </template>
          </el-upload>
          
          <div class="upload-actions" v-if="fileList.length > 0">
            <el-button type="primary" @click="handleOCR" :loading="ocrLoading">
              <el-icon><Document /></el-icon>
              OCR识别
            </el-button>
            <el-button @click="clearUpload">清空</el-button>
          </div>
        </el-form-item>
        
        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="printForm">打印</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Document } from '@element-plus/icons-vue'

// 表单数据
const formData = reactive({
  // 基本信息
  orderDate: '',
  contractCode: '',
  contractName: '',
  ceaNumber: '',
  constructionUnit: '',
  supervisionUnit: '',
  designUnit: '',
  projectName: '',
  projectLocation: '',
  contactPerson: '',
  contactPhone: '',
  startDate: '',
  completionDate: '',
  
  // 服务清单
  serviceList: [] as Array<{
    serviceName: string
    specification: string
    model: string
    quantity: number
    unit: string
  }>,
  
  // 备注信息
  designer: '',
  businessManager: '',
  qualityRequirements: '',
  precautions: '',
  reportTo: '',
  copyTo: '',
  sendTo: '',
  signee: ''
})

// 表单验证规则
const rules = {
  orderDate: [
    { required: true, message: '请选择派单日期', trigger: 'change' }
  ],
  contractCode: [
    { required: true, message: '请输入合同编码', trigger: 'blur' }
  ],
  contractName: [
    { required: true, message: '请输入合同名称', trigger: 'blur' }
  ],
  ceaNumber: [
    { required: true, message: '请输入CEA编号', trigger: 'blur' }
  ],
  constructionUnit: [
    { required: true, message: '请输入施工单位', trigger: 'blur' }
  ],
  supervisionUnit: [
    { required: true, message: '请输入监理单位', trigger: 'blur' }
  ],
  designUnit: [
    { required: true, message: '请选择设计单位', trigger: 'change' }
  ],
  projectName: [
    { required: true, message: '请输入工程名称', trigger: 'blur' }
  ],
  projectLocation: [
    { required: true, message: '请输入工程地点', trigger: 'blur' }
  ],
  contactPerson: [
    { required: true, message: '请输入联系人', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  startDate: [
    { required: true, message: '请选择开工日期', trigger: 'change' }
  ],
  completionDate: [
    { required: true, message: '请选择完工日期', trigger: 'change' }
  ]
}

const formRef = ref()
const uploadRef = ref()

// 上传相关
const fileList = ref([])
const ocrLoading = ref(false)
const uploadUrl = '/api/upload' // 上传接口
const uploadHeaders = {
  'Authorization': 'Bearer ' + localStorage.getItem('token') || ''
}

// 上传前验证
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过 10MB!')
    return false
  }
  return true
}

// 上传成功
const handleUploadSuccess = (response: any, file: any) => {
  ElMessage.success('图片上传成功')
  console.log('上传成功:', response)
}

// 上传失败
const handleUploadError = (error: any) => {
  ElMessage.error('图片上传失败')
  console.error('上传失败:', error)
}

// OCR识别
const handleOCR = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先上传图片')
    return
  }

  ocrLoading.value = true
  try {
    // 调用OCR API
    const response = await fetch('/api/ocr/contract', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + localStorage.getItem('token') || ''
      },
      body: JSON.stringify({
        imageUrl: fileList.value[0].url || fileList.value[0].response?.url
      })
    })

    if (!response.ok) {
      throw new Error('OCR识别失败')
    }

    const result = await response.json()
    
    // 自动填充表单
    if (result.success) {
      autoFillForm(result.data)
      ElMessage.success('OCR识别成功，已自动填充表单')
    } else {
      ElMessage.error(result.message || 'OCR识别失败')
    }
  } catch (error) {
    console.error('OCR识别错误:', error)
    ElMessage.error('OCR识别失败，请检查网络连接')
  } finally {
    ocrLoading.value = false
  }
}

// 自动填充表单
const autoFillForm = (ocrData: any) => {
  // 根据OCR识别结果填充表单
  if (ocrData.contractName) formData.contractName = ocrData.contractName
  if (ocrData.contractCode) formData.contractCode = ocrData.contractCode
  if (ocrData.ceaNumber) formData.ceaNumber = ocrData.ceaNumber
  if (ocrData.constructionUnit) formData.constructionUnit = ocrData.constructionUnit
  if (ocrData.supervisionUnit) formData.supervisionUnit = ocrData.supervisionUnit
  if (ocrData.designUnit) formData.designUnit = ocrData.designUnit
  if (ocrData.projectName) formData.projectName = ocrData.projectName
  if (ocrData.projectLocation) formData.projectLocation = ocrData.projectLocation
  if (ocrData.contactPerson) formData.contactPerson = ocrData.contactPerson
  if (ocrData.contactPhone) formData.contactPhone = ocrData.contactPhone
  if (ocrData.startDate) formData.startDate = ocrData.startDate
  if (ocrData.completionDate) formData.completionDate = ocrData.completionDate
  if (ocrData.designer) formData.designer = ocrData.designer
  if (ocrData.businessManager) formData.businessManager = ocrData.businessManager
  if (ocrData.qualityRequirements) formData.qualityRequirements = ocrData.qualityRequirements
  if (ocrData.precautions) formData.precautions = ocrData.precautions
  if (ocrData.reportTo) formData.reportTo = ocrData.reportTo
  if (ocrData.copyTo) formData.copyTo = ocrData.copyTo
  if (ocrData.sendTo) formData.sendTo = ocrData.sendTo
  if (ocrData.signee) formData.signee = ocrData.signee
  
  // 填充服务清单
  if (ocrData.serviceList && ocrData.serviceList.length > 0) {
    formData.serviceList = ocrData.serviceList
  }
}

// 清空上传
const clearUpload = () => {
  fileList.value = []
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 添加服务项
const addServiceItem = () => {
  formData.serviceList.push({
    serviceName: '',
    specification: '',
    model: '',
    quantity: 0,
    unit: ''
  })
}

// 删除服务项
const removeServiceItem = (index: number) => {
  formData.serviceList.splice(index, 1)
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // TODO: 调用API保存数据
    console.log('提交的数据:', formData)
    
    ElMessage.success('保存成功')
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  if (!formRef.value) return
  
  ElMessageBox.confirm('确定要重置表单吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    formRef.value.resetFields()
    formData.serviceList = []
    ElMessage.success('表单已重置')
  })
}

// 打印表单
const printForm = () => {
  // TODO: 实现打印功能
  ElMessage.info('打印功能开发中')
}
</script>

<style scoped>
.new-project-entry {
  padding: 20px;
}

.form-card {
  max-width: 1200px;
  margin: 0 auto;
}

.project-form {
  margin-top: 20px;
}

.service-list {
  margin: 20px 0;
}

.service-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 15px;
}

:deep(.el-divider__text) {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.upload-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

:deep(.el-upload--picture-card) {
  width: 148px;
  height: 148px;
  line-height: 146px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 148px;
  height: 148px;
}
</style> 