# 工单列表导入Excel弹窗功能说明

## 功能概述

工单列表页面已添加导入Excel弹窗功能，与物料列表的导入功能保持一致，提供统一的用户体验。

## 功能特性

### ✅ 弹窗界面
- **拖拽上传**：支持拖拽Excel文件到指定区域
- **点击上传**：支持点击选择Excel文件
- **文件验证**：自动验证文件格式（.xlsx, .xls）
- **进度显示**：实时显示导入进度和状态

### ✅ 进度跟踪
- **实时进度**：显示当前处理进度百分比
- **状态反馈**：显示处理状态（处理中、成功、失败）
- **详细信息**：显示处理结果（成功条数、失败条数等）
- **错误处理**：显示错误信息和处理建议

### ✅ 用户体验
- **响应式设计**：适配不同屏幕尺寸
- **状态管理**：导入过程中禁用上传，防止重复操作
- **取消确认**：导入过程中取消会弹出确认对话框
- **自动刷新**：导入完成后自动刷新列表数据

## 使用方法

### 1. 打开导入弹窗
点击工单列表页面右上角的"导入Excel"按钮，弹出导入对话框。

### 2. 上传Excel文件
- **方式一**：将Excel文件拖拽到虚线框内
- **方式二**：点击虚线框选择Excel文件

### 3. 查看导入进度
上传成功后，页面会显示：
- 进度条：显示当前处理进度
- 状态信息：显示处理状态和详细信息
- 处理结果：显示成功、失败、跳过的条数

### 4. 完成导入
- **成功**：显示成功消息，自动刷新列表
- **失败**：显示错误信息，可重新尝试
- **部分成功**：显示成功和失败的统计信息

## 支持的Excel格式

### 必需字段
- **工单号**：唯一标识，必填
- **用户姓名**：客户姓名，必填

### 可选字段
- **用户编号**：客户编码
- **移动电话**：联系电话
- **地址**：详细地址（映射到甲单地址）
- **诉求描述**：工单内容（映射到甲单诉求描述）
- **备注**：备注信息（映射到甲单备注）
- **费用合计金额**：费用金额（映射到甲单费用合计金额）
- **任务状态**：订单状态（只导入状态为"已完成"的记录）

### 字段映射说明
Excel中的以下字段会直接映射到甲单字段：
- 地址 → 甲单地址
- 诉求描述 → 甲单诉求描述
- 备注 → 甲单备注
- 费用合计金额 → 甲单费用合计金额

### 导入条件
- **任务状态筛选**：只导入任务状态为"已完成"、"完成"、"finished"或"done"的记录
- **状态字段支持**：支持"任务状态"、"状态"、"订单状态"等字段名
- **自动跳过**：非已完成状态的记录会被自动跳过，并在日志中记录跳过原因

## 技术实现

### 前端组件
- **弹窗组件**：使用Element Plus的Dialog组件
- **上传组件**：使用Element Plus的Upload组件
- **进度组件**：使用Element Plus的Progress组件
- **状态管理**：使用Vue 3的响应式API

### 后端接口
- **上传接口**：`POST /api/loose-orders/import`
- **进度查询**：`GET /api/import-progress/:taskId`
- **通用进度模块**：使用统一的进度跟踪系统

### 进度跟踪
- **任务创建**：上传时创建进度跟踪任务
- **实时更新**：处理过程中实时更新进度
- **状态同步**：前端轮询获取最新状态
- **自动清理**：任务完成后自动清理

## 错误处理

### 文件格式错误
- 提示：只能上传Excel文件
- 解决：选择正确的Excel文件格式

### 文件大小限制
- 限制：最大200MB
- 提示：文件太大，请选择较小的文件

### 网络错误
- 提示：上传失败，请检查网络连接
- 解决：检查网络连接后重试

### 数据格式错误
- 提示：文件格式错误，请检查Excel文件
- 解决：检查Excel文件格式和内容

### 重复数据
- 处理：自动跳过重复的工单号
- 提示：显示跳过的条数

### 状态筛选
- 处理：自动跳过非已完成状态的记录
- 提示：显示跳过的条数和原因
- 支持状态：已完成、完成、finished、done

## 最佳实践

### 1. Excel文件准备
- 确保第一行包含字段标题
- 工单号必须唯一
- 必填字段不能为空
- 数据格式正确
- 任务状态字段包含"已完成"等有效状态

### 2. 导入操作
- 选择合适的时间进行批量导入
- 避免在业务高峰期导入大量数据
- 导入前备份重要数据

### 3. 错误处理
- 仔细查看错误信息
- 根据错误提示修正数据
- 分批导入大量数据

## 注意事项

1. **数据安全**：导入前请确认数据准确性
2. **网络稳定**：确保网络连接稳定
3. **浏览器兼容**：建议使用Chrome、Firefox等现代浏览器
4. **文件大小**：单个文件不超过200MB
5. **并发限制**：同时只能进行一个导入任务

## 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 添加导入Excel弹窗功能
- ✅ 集成通用进度跟踪模块
- ✅ 支持拖拽上传和点击上传
- ✅ 实时进度显示和状态反馈
- ✅ 错误处理和用户提示
- ✅ 自动刷新列表数据

这个功能为工单列表提供了完整的Excel导入体验，与物料列表保持一致的交互方式，提升了用户的工作效率。 