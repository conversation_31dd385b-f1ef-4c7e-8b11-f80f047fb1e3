# 月度平账功能 - 技术实现说明

## 1. 技术架构

### 1.1 前端技术栈
- **Vue 3**: 主框架
- **Element Plus**: UI组件库
- **TypeScript**: 类型安全
- **Vite**: 构建工具

### 1.2 核心组件
- **el-table**: 表格展示组件
- **el-tabs**: 选项卡切换组件
- **el-date-picker**: 日期选择组件

## 2. 关键技术实现

### 2.1 合计行功能实现

#### 2.1.1 Element Plus 表格合计配置
```vue
<el-table 
  :data="tableData" 
  show-summary 
  :summary-method="getSummaryMethod"
  border
>
```

#### 2.1.2 合计方法实现模式
```typescript
const getSummaryMethod = (param: any) => {
  const { columns, data } = param
  const sums: any = []
  
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    
    // 跳过文本列
    if (isTextColumn(column.property)) {
      sums[index] = ''
      return
    }
    
    // 处理金额列
    if (isAmountColumn(column.property)) {
      const values = data.map((item: any) => Number(item[column.property]))
      sums[index] = `¥${formatNumber(calculateSum(values))}`
      return
    }
    
    // 处理数量列
    const values = data.map((item: any) => Number(item[column.property]))
    if (!values.every((value: number) => isNaN(value))) {
      sums[index] = calculateSum(values)
    } else {
      sums[index] = ''
    }
  })
  
  return sums
}
```

### 2.2 多级表头实现

#### 2.2.1 嵌套表头结构
```vue
<el-table-column label="户主信息" align="center">
  <el-table-column prop="customerName" label="姓名" width="100" />
  <el-table-column prop="customerCode" label="用户编号" width="120" />
  <el-table-column label="住址" align="center">
    <el-table-column prop="communityName" label="小区名称" width="120" />
    <el-table-column prop="buildingNo" label="楼橦" width="80" />
    <el-table-column prop="roomNo" label="房号" width="80" />
  </el-table-column>
</el-table-column>
```

#### 2.2.2 复杂多级表头示例
```vue
<!-- 工程安装人工费多级表头 -->
<el-table-column label="工程安装人工费(元)" align="center">
  <el-table-column prop="preLaborCost" label="表前安装人工费" width="140" />
  <el-table-column prop="indoorLaborCost" label="户内安装人工费" width="140" />
  <el-table-column prop="totalLaborCost" label="小计(元)" width="120">
    <template #default="{ row }">
      ¥{{ formatNumber(row.totalLaborCost) }}
    </template>
  </el-table-column>
</el-table-column>
```

### 2.3 数据格式化

#### 2.3.1 金额格式化函数
```typescript
const formatNumber = (num: number): string => {
  if (isNaN(num)) return '0'
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}
```

#### 2.3.2 模板中的金额显示
```vue
<el-table-column prop="amount" label="金额(元)" width="120" align="center">
  <template #default="{ row }">
    ¥{{ formatNumber(row.amount) }}
  </template>
</el-table-column>
```

### 2.4 数据结构设计

#### 2.4.1 户内挂表安装工程管件统计数据结构
```typescript
interface MeterInstallationFittingsData {
  serialNo: number
  customerName: string
  customerCode: string
  customerAddress: string
  communityName: string
  buildingNo: string
  roomNo: string
  dispatchTime: string
  installTime: string
  corrugatedQuickConnector: number
  galvanized90Elbow: number
  galvanizedHexMaleThread: number
  galvanizedFemaleThread: number
  galvanizedMaleFemaleElbow: number
  galvanizedTee: number
  stainlessSteelClamp: number
  customGalvanizedBushing: number
  meterCorrugatedPipe: number
  gasMeterBracket: number
  antiTheftGasLock: number
  galvanizedPlug: number
  protectionPlateStraight: number
  protectionPlateBend: number
  decorativeCover: number
  actualMaterialCost: number
}
```

#### 2.4.2 工程决算数据结构
```typescript
interface MeterInstallationSettlement {
  serialNo: number
  customerName: string
  customerCode: string
  communityName: string
  buildingNo: string
  roomNo: string
  dispatchTime: string
  installTime: string
  hasMeterBox: string
  preMeterValve: string
  preMeterPrice: number
  indoorPrice: number
  stoveFrontValveQty: number
  preSteelPipe: number
  preCoatedSteelPipe: number
  preCorrugatedPipe: number
  indoorSteelPipe: number
  indoorCoatedSteelPipe: number
  indoorCorrugatedPipe: number
  mechanicalMeter: string
  lowPressureRegulator: number
  corrugatedConnectLong: number
  corrugatedConnectShort: number
  prefabShort12: number
  prefabShort18: number
  preMeterInstallCost: number
  indoorInstallCost: number
}
```

### 2.5 响应式数据管理

#### 2.5.1 Vue 3 Composition API 使用
```typescript
import { ref, computed, onMounted } from 'vue'

// 响应式数据定义
const meterInstallationFittingsData = ref<MeterInstallationFittingsData[]>([])
const gasMinorSettlementHalfData = ref<GasMinorSettlementData[]>([])
const suppliedMaterialsReceiptData = ref<SuppliedMaterialsData[]>([])

// 计算属性
const totalAmount = computed(() => {
  return meterInstallationFittingsData.value.reduce((sum, item) => {
    return sum + item.actualMaterialCost
  }, 0)
})

// 生命周期钩子
onMounted(() => {
  loadMonthlyBalanceData()
})
```

#### 2.5.2 数据加载和更新
```typescript
const loadMonthlyBalanceData = async () => {
  try {
    const response = await api.getMonthlyBalanceData(selectedMonth.value)
    
    // 更新各个表格数据
    meterInstallationFittingsData.value = response.meterInstallationFittingsData || []
    gasMinorSettlementHalfData.value = response.gasMinorSettlementHalfData || []
    suppliedMaterialsReceiptData.value = response.suppliedMaterialsReceiptData || []
    
    // 触发合计重新计算
    nextTick(() => {
      recalculateSummaries()
    })
  } catch (error) {
    console.error('加载月度平账数据失败:', error)
    ElMessage.error('数据加载失败，请重试')
  }
}
```

## 3. 性能优化

### 3.1 表格虚拟滚动
```vue
<el-table
  :data="tableData"
  :height="600"
  :virtual-scrolling="true"
  :estimated-row-height="50"
>
```

### 3.2 数据懒加载
```typescript
const loadTableData = async (page: number, pageSize: number) => {
  const response = await api.getTableData({
    page,
    pageSize,
    month: selectedMonth.value
  })
  
  if (page === 1) {
    tableData.value = response.data
  } else {
    tableData.value.push(...response.data)
  }
}
```

### 3.3 合计计算优化
```typescript
// 使用防抖优化合计计算
import { debounce } from 'lodash-es'

const debouncedRecalculate = debounce(() => {
  recalculateSummaries()
}, 300)

// 数据变更时调用
watch(tableData, () => {
  debouncedRecalculate()
}, { deep: true })
```

## 4. 错误处理

### 4.1 数据验证
```typescript
const validateTableData = (data: any[]): boolean => {
  return data.every(item => {
    return item.serialNo && 
           item.customerName && 
           typeof item.actualMaterialCost === 'number'
  })
}
```

### 4.2 异常处理
```typescript
const handleTableError = (error: Error, tableName: string) => {
  console.error(`${tableName}表格错误:`, error)
  ElMessage.error(`${tableName}数据处理失败，请检查数据格式`)
  
  // 重置为默认数据
  resetTableData(tableName)
}
```

## 5. 测试策略

### 5.1 单元测试
```typescript
import { describe, it, expect } from 'vitest'

describe('合计计算功能', () => {
  it('应该正确计算数值列的合计', () => {
    const testData = [
      { amount: 100, quantity: 5 },
      { amount: 200, quantity: 3 }
    ]
    
    const result = calculateSummary(testData, 'amount')
    expect(result).toBe(300)
  })
  
  it('应该正确格式化金额显示', () => {
    const result = formatNumber(1234.56)
    expect(result).toBe('1,234.56')
  })
})
```

### 5.2 集成测试
```typescript
describe('月度平账页面', () => {
  it('应该正确加载所有表格数据', async () => {
    const wrapper = mount(MonthlyBalance)
    
    await wrapper.vm.loadMonthlyBalanceData()
    
    expect(wrapper.vm.meterInstallationFittingsData).toHaveLength(2)
    expect(wrapper.vm.gasMinorSettlementHalfData).toHaveLength(2)
  })
  
  it('应该正确显示合计行', async () => {
    const wrapper = mount(MonthlyBalance)
    
    await nextTick()
    
    const summaryRow = wrapper.find('.el-table__footer-wrapper')
    expect(summaryRow.exists()).toBe(true)
  })
})
```

## 6. 部署和维护

### 6.1 构建配置
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'element-plus': ['element-plus'],
          'vue-vendor': ['vue', 'vue-router']
        }
      }
    }
  }
})
```

### 6.2 监控和日志
```typescript
// 性能监控
const performanceMonitor = {
  startTime: 0,
  
  start() {
    this.startTime = performance.now()
  },
  
  end(operation: string) {
    const duration = performance.now() - this.startTime
    console.log(`${operation} 耗时: ${duration.toFixed(2)}ms`)
    
    if (duration > 1000) {
      console.warn(`${operation} 性能警告: 耗时超过1秒`)
    }
  }
}
```

## 7. 未来扩展

### 7.1 数据导出功能
```typescript
const exportToExcel = async (tableName: string, data: any[]) => {
  const workbook = new ExcelJS.Workbook()
  const worksheet = workbook.addWorksheet(tableName)
  
  // 添加表头
  worksheet.addRow(getTableHeaders(tableName))
  
  // 添加数据
  data.forEach(row => {
    worksheet.addRow(Object.values(row))
  })
  
  // 导出文件
  const buffer = await workbook.xlsx.writeBuffer()
  saveAs(new Blob([buffer]), `${tableName}.xlsx`)
}
```

### 7.2 实时数据更新
```typescript
// WebSocket 连接
const ws = new WebSocket('ws://localhost:8080/monthly-balance')

ws.onmessage = (event) => {
  const update = JSON.parse(event.data)
  
  if (update.type === 'data-update') {
    updateTableData(update.tableName, update.data)
  }
}
```
