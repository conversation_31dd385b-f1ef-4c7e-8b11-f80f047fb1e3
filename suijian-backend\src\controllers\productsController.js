const { logger } = require('../utils/logger');
const { query, queryOne, execute } = require('../config/database');
const { generatePrefixedId } = require('../utils/uuid');

/**
 * 商品入库
 * POST /products/inbound
 * body: { items: [{ id, quantity }], inboundDate, remarks }
 */
const productInbound = async(req, res) => {
    try {
        const { items, orderId, inboundDate, remarks } = req.body;

        if (!orderId || !orderId.trim()) {
            return res.status(400).json({ success: false, message: '入库单号不能为空' });
        }

        if (!Array.isArray(items) || items.length === 0) {
            return res.status(400).json({ success: false, message: '入库商品不能为空' });
        }

        // 校验所有商品ID（只允许category为"商品"的记录）
        const ids = items.map(i => i.id);
        const placeholders = ids.map(() => '?').join(',');
        const sql = `SELECT id, name, stock_quantity FROM materials WHERE id IN (${placeholders}) AND category = '商品' AND status = 1`;
        const rows = await query(sql, ids);
        const validIds = rows.map(r => r.id);
        const invalidIds = ids.filter(id => !validIds.includes(id));

        if (invalidIds.length > 0) {
            return res.status(400).json({ success: false, message: '部分商品ID无效或不是商品类型', invalidIds });
        }

        // 批量更新库存和写入日志
        console.log('【商品入库】items:', items);
        console.log('【商品入库】operator_id:', req.user.id);
        console.log('【商品入库】inboundDate:', inboundDate);
        console.log('【商品入库】remarks:', remarks);

        // 开始事务
        await execute('BEGIN TRANSACTION');

        try {
            // 创建入库记录主表
            const recordId = generatePrefixedId('mr');
            await execute(
                `INSERT INTO material_records (id, type, operator_id, order_id, remarks, created_at) VALUES (?, 'product_in', ?, ?, ?, datetime('now'))`, [recordId, req.user.id, orderId, remarks || '']
            );

            // 批量更新库存和写入明细
            for (const item of items) {
                // 获取当前库存数量
                const currentStockSql = 'SELECT stock_quantity FROM materials WHERE id = ?';
                const currentStockResult = await query(currentStockSql, [item.id]);
                const currentQuantity = currentStockResult[0] && currentStockResult[0].stock_quantity ? currentStockResult[0].stock_quantity : 0;

                // 更新库存
                const newQuantity = currentQuantity + (parseInt(item.quantity) || 0);
                await execute('UPDATE materials SET stock_quantity = ? WHERE id = ?', [newQuantity, item.id]);

                // 写入入库明细
                await execute(
                    `INSERT INTO material_record_items (id, record_id, material_id, quantity, current_quantity, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))`, [generatePrefixedId('mri'), recordId, item.id, parseInt(item.quantity) || 0, currentQuantity]
                );
            }

            // 提交事务
            await execute('COMMIT');
        } catch (error) {
            // 回滚事务
            await execute('ROLLBACK');
            throw error;
        }

        res.json({ success: true, message: '商品入库成功' });
    } catch (error) {
        logger.error('商品入库失败', error);
        res.status(500).json({ success: false, message: '商品入库失败' });
    }
};

module.exports = {
    productInbound
};