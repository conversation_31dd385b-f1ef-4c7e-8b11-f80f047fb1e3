import type { Router } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

export default function guards(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore()
    
    // 设置页面标题
    document.title = `${to.meta.title || '工程管理系统'}`
    
    // 检查是否需要认证
    if (to.meta.requiresAuth) {
      if (!authStore.isAuthenticated) {
        next('/login')
        return
      }
    }
    
    // 如果已登录且访问登录页，重定向到首页
    if (to.path === '/login' && authStore.isAuthenticated) {
      next('/dashboard')
      return
    }
    
    next()
  })
} 