<template>
  <div class="material-return-container">
    <el-card class="main-card">
      
      <!-- 退仓信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="12">
          <el-form-item label="退仓单号:">
            <span>TC20240115001</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="退仓日期:">
            <el-date-picker
              v-model="returnForm.returnDate"
              type="date"
              placeholder="请选择退仓日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="退仓人:">
            <el-select v-model="returnForm.returner" placeholder="请选择退仓人" style="width: 100%">
              <el-option
                v-for="employee in employeeList"
                :key="employee.id"
                :label="`${employee.name} - ${employee.phone || '无电话'}`"
                :value="employee.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="退仓类型:">
            <el-select v-model="returnForm.returnType" placeholder="请选择退仓类型" style="width: 100%">
              <el-option label="安检" value="安检" />
              <el-option label="维修" value="维修" />
              <el-option label="工程" value="工程" />
              <el-option label="商品售卖" value="商品售卖" />
              <el-option label="其它" value="其它" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联工程:">
            <el-select 
              v-model="returnForm.relatedProject" 
              placeholder="请选择关联工程" 
              style="width: 100%"
              :disabled="returnForm.returnType !== '工程' && returnForm.returnType !== '商品售卖'"
            >
              <el-option
                v-for="project in projectList"
                :key="project.id"
                :label="project.name"
                :value="project.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 退仓物料 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <el-table :data="returnItems" border class="return-table">
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="companyCode" label="公司物料编码" />
            <el-table-column prop="name" label="物料名称" />
            <el-table-column prop="clientCodes" label="甲料编码" width="120" />
            <el-table-column prop="specification" label="规格" />
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column prop="stockQuantity" label="库存数量" width="100" />
            <el-table-column prop="returnQuantity" label="退仓数量" width="150">
              <template #default="scope">
                <el-input-number v-model="scope.row.returnQuantity" :min="1" style="width: 100%;" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" fixed="right">
              <template #default="scope">
                <el-button type="danger" link @click="removeItem(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 15px;">
            <el-button type="primary" @click="openMaterialSelector">选择物料</el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 备注信息 -->
      <el-row :gutter="20" class="form-section">
        <el-col :span="24">
          <el-form-item label="备注:">
            <el-input
              v-model="returnForm.remarks"
              type="textarea"
              :rows="2"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button @click="handlePrint">打印</el-button>
        <el-button type="success" @click="handleSubmit">提交</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </el-card>

    <!-- 物料选择器 -->
    <MaterialSelector
      v-model="materialDialogVisible"
      title="选择退仓物料"
      :selected-ids="selectedMaterialIds"
      @selection-change="handleMaterialSelectionChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import MaterialSelector from '@/components/MaterialSelector.vue'
import request from '@/utils/request'

// 退仓表单
const returnForm = reactive({
  returnDate: new Date().toISOString().split('T')[0],
  returner: '',
  returnType: '工程',
  relatedProject: '',
  remarks: ''
})

// 工程列表
const projectList = ref([])

// 员工列表
const employeeList = ref([])

// 退仓物料
const returnItems = ref([])

// 物料选择器相关
const materialDialogVisible = ref(false)

// 本地存储键名
const RETURN_ITEMS_KEY = 'material_return_items'
const RETURN_FORM_KEY = 'material_return_form'

// 计算已选物料ID列表，用于恢复选择状态
const selectedMaterialIds = computed(() => returnItems.value.map(item => item.id))

// 检查是否有未保存的数据
const hasUnsavedData = computed(() => {
  return returnItems.value.length > 0 || 
         returnForm.returner !== '' || 
         returnForm.relatedProject !== '' || 
         returnForm.remarks !== '' ||
         returnForm.returnType !== '工程'
})

// 监听returnItems变化，自动保存到本地存储
watch(returnItems, (val) => {
  localStorage.setItem(RETURN_ITEMS_KEY, JSON.stringify(val))
}, { deep: true })

// 监听returnForm变化，自动保存到本地存储
watch(returnForm, (val) => {
  localStorage.setItem(RETURN_FORM_KEY, JSON.stringify(val))
}, { deep: true })

// 从本地存储恢复数据
const restoreFromLocalStorage = () => {
  try {
    // 恢复退仓物料列表
    const savedItems = localStorage.getItem(RETURN_ITEMS_KEY)
    if (savedItems) {
      returnItems.value = JSON.parse(savedItems)
    }
    
    // 恢复表单数据
    const savedForm = localStorage.getItem(RETURN_FORM_KEY)
    if (savedForm) {
      const formData = JSON.parse(savedForm)
      Object.assign(returnForm, formData)
    }
  } catch (error) {
    console.error('恢复本地存储数据失败:', error)
  }
}

// 清除本地存储数据
const clearLocalStorage = () => {
  try {
    localStorage.removeItem(RETURN_ITEMS_KEY)
    localStorage.removeItem(RETURN_FORM_KEY)
  } catch (error) {
    console.error('清除本地存储数据失败:', error)
  }
}

// 重置所有数据
const resetAllData = () => {
  // 清除本地存储
  clearLocalStorage()
  
  // 清空物料列表
  returnItems.value = []
  
  // 重置表单数据
  Object.assign(returnForm, {
    returnDate: new Date().toISOString().split('T')[0],
    returner: '',
    returnType: '工程',
    relatedProject: '',
    remarks: ''
  })
  
  console.log('所有数据已重置')
}

// 加载工程列表
const loadProjectList = async () => {
  try {
    const response = await request.get('/api/dashboard/projects/status')
    
    if (response.success && response.data && response.data.details) {
      projectList.value = response.data.details.map(project => ({
        id: project.id,
        name: project.name
      }))
    } else {
      ElMessage.error('加载工程列表失败')
    }
  } catch (error) {
    console.error('加载工程列表失败:', error)
    ElMessage.error('加载工程列表失败')
  }
}

// 加载员工列表
const loadEmployeeList = async () => {
  try {
    const response = await request.get('/api/employees/list', {
      params: {
        page: 1,
        pageSize: 1000, // 获取所有员工
        status: 1 // 只获取在职员工
      }
    })
    
    if (response.success) {
      employeeList.value = response.data.list || []
    } else {
      ElMessage.error('加载员工列表失败')
    }
  } catch (error) {
    console.error('加载员工列表失败:', error)
    ElMessage.error('加载员工列表失败')
  }
}

// 打开物料选择器
const openMaterialSelector = () => {
  materialDialogVisible.value = true
}

// 处理物料选择变化
const handleMaterialSelectionChange = (materials: any[]) => {
  returnItems.value = materials.map(material => ({
    ...material,
    returnQuantity: returnItems.value.find(existing => existing.id === material.id)?.returnQuantity || 1
  }))
}

// 删除物料
const removeItem = (index: number) => {
  returnItems.value.splice(index, 1)
  ElMessage.success('删除成功')
}

// 打印
const handlePrint = () => {
  ElMessage.success('开始打印')
}

// 提交
const handleSubmit = async () => {
  try {
    // 验证必填字段
    if (!returnForm.returnDate || !returnForm.returner || !returnForm.returnType) {
      ElMessage.error('请填写完整的退仓信息')
      return
    }

    if (returnItems.value.length === 0) {
      ElMessage.error('请至少选择一个退仓物料')
      return
    }

    // 验证退仓数量
    const invalidItems = returnItems.value.filter(item => !item.returnQuantity || item.returnQuantity <= 0)
    if (invalidItems.length > 0) {
      ElMessage.error('请填写有效的退仓数量')
      return
    }

    const submitData = {
      returnDate: returnForm.returnDate,
      returnerId: returnForm.returner,
      returnType: returnForm.returnType,
      relatedProject: returnForm.relatedProject || null,
      remarks: returnForm.remarks,
      items: returnItems.value.map(item => ({
        id: item.id,
        returnQuantity: item.returnQuantity
      }))
    }

    console.log('提交数据:', submitData)

    const response = await request.post('/api/materials/return', submitData)
    
    if (response.success) {
      ElMessage.success('物料退仓成功，页面数据已清除')
      // 提交成功后清除本地存储和重置表单
      resetAllData()
    } else {
      ElMessage.error(response.message || '物料退仓失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请稍后重试')
  }
}

// 取消
const handleCancel = () => {
  if (hasUnsavedData.value) {
    ElMessageBox.confirm('当前有未保存的数据，确定要取消退仓操作吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      ElMessage.info('已取消')
      // 重置所有数据
      resetAllData()
    }).catch(() => {
      // 用户取消
    })
  } else {
    ElMessage.info('已取消')
    resetAllData()
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadEmployeeList()
  loadProjectList()
  restoreFromLocalStorage()
  
  // 设置默认日期为今天
  if (!returnForm.returnDate) {
    returnForm.returnDate = new Date().toISOString().split('T')[0]
  }
})
</script>

<style lang="scss" scoped>
.material-return-container {
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  
  .main-card {
    margin-bottom: 20px;
  }
  
  .form-section {
    margin-bottom: 20px;
    
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  
  .return-table {
    margin-top: 10px;
  }
  
  .form-actions {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 10px;
      padding: 12px 30px;
    }
  }
}
</style>