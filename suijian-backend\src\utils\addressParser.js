const { query } = require('../config/database');
const { logger } = require('./logger');

/**
 * 地址解析工具类
 * 用于从地址中提取小区名称、楼栋和房号
 */
class AddressParser {
    constructor() {
        this.communityCache = new Map(); // 小区缓存
        this.cacheExpireTime = 30 * 60 * 1000; // 30分钟缓存过期
        this.lastCacheUpdate = 0;
    }

    /**
     * 获取 communities 表的列信息
     */
    async getCommunitiesColumns() {
        try {
            const columns = await query("PRAGMA table_info(communities)");
            const columnNames = columns.map(c => c.name);
            return new Set(columnNames);
        } catch (error) {
            logger.error('读取communities表结构失败', error);
            return new Set();
        }
    }

    /**
     * 为小区名识别清洗地址：去除省/市/区/县/镇/乡/街道/开发区，以及“路/街/巷/弄/道/大道/大街”+门牌号
     */
    sanitizeAddressForCommunity(address) {
        if (!address) return '';
        let text = String(address);

        // 统一去除空白
        text = text.replace(/[\s\t]+/g, '');

        // 去掉省、市、自治区、州、地区、盟
        text = text.replace(/[\u4e00-\u9fa5A-Za-z0-9]+(?:省|市|自治区|自治州|地区|盟|州)/g, '');
        // 去掉区、县、镇、乡、街道、开发区、新区、高新区等行政后缀
        text = text.replace(/[\u4e00-\u9fa5A-Za-z0-9]+(?:区|县|镇|乡|街道|开发区|新区|高新区|工业园区|经济开发区)/g, '');
        // 去掉道路+门牌（如 XX大道XX号、XX路12号、XX街45-1号、XX巷3号等）
        text = text.replace(/[\u4e00-\u9fa5A-Za-z0-9]+(?:大道|大街|路|街|巷|弄|道)[A-Za-z0-9\-一二三四五六七八九十百千万]*号?/g, '');
        // 去掉纯门牌号（防止残留）
        text = text.replace(/\d+号/g, '');

        return text;
    }

    /**
     * 清洗地址中与“道路+门牌”有关的信息，避免被误识别为楼栋
     */
    sanitizeAddressForBuilding(address) {
        if (!address) return '';
        let text = String(address);
        // 移除“XX路/街/巷/弄/道/大道/大街 + 门牌号”
        text = text.replace(/[\u4e00-\u9fa5A-Za-z0-9]+(?:大道|大街|路|街|巷|弄|道)[A-Za-z0-9\-一二三四五六七八九十百千万]*号/g, '');
        return text;
    }

    /**
     * 从数据库加载小区信息到缓存
     */
    async loadCommunities() {
        try {
            const now = Date.now();
            // 如果缓存未过期，直接返回
            if (now - this.lastCacheUpdate < this.cacheExpireTime && this.communityCache.size > 0) {
                return;
            }

            // 动态构建可用列
            const cols = await this.getCommunitiesColumns();
            const selectable = ['id', 'name'];
            if (cols.has('alias')) selectable.push('alias');
            if (cols.has('district')) selectable.push('district');
            if (cols.has('city')) selectable.push('city');
            if (cols.has('province')) selectable.push('province');
            if (cols.has('address')) selectable.push('address');

            const sql = `SELECT ${selectable.join(', ')} FROM communities WHERE status = 1`;
            const communities = await query(sql);

            this.communityCache.clear();
            communities.forEach(community => {
                if (community.name) {
                    // 主名称
                    this.communityCache.set(String(community.name).toLowerCase(), community);
                }

                // 别名
                if (community.alias) {
                    const aliases = String(community.alias).split(',').map(alias => alias.trim());
                    aliases.forEach(alias => {
                        if (alias) {
                            this.communityCache.set(alias.toLowerCase(), community);
                        }
                    });
                }
            });

            this.lastCacheUpdate = now;
            logger.info(`地址解析器已加载 ${communities.length} 个小区信息到缓存`);
        } catch (error) {
            logger.error('加载小区信息失败', error);
        }
    }

    /**
     * 解析地址，提取小区名称、楼栋和房号
     * @param {string} address 完整地址
     * @returns {object} 解析结果 {communityName, building, roomNumber}
     */
    async parseAddress(address) {
        if (!address || typeof address !== 'string') {
            return { communityName: '', building: '', roomNumber: '' };
        }

        // 加载小区信息
        await this.loadCommunities();

        const result = {
            communityName: '',
            building: '',
            roomNumber: ''
        };

        try {
            // 1. 提取小区名称（先清洗再识别）
            result.communityName = this.extractCommunityName(address);

            // 2. 提取楼栋号（保持原文单位）
            result.building = this.extractBuilding(address);

            // 3. 提取房号（保持原文单位）
            result.roomNumber = this.extractRoomNumber(address);

            logger.debug('地址解析结果', {
                originalAddress: address,
                parsedResult: result
            });

        } catch (error) {
            logger.error('地址解析失败', { address, error: error.message });
        }

        return result;
    }

    /**
     * 提取小区名称
     * @param {string} address 地址
     * @returns {string} 小区名称
     */
    extractCommunityName(address) {
        const addressLower = address.toLowerCase();
        const cleanedLower = this.sanitizeAddressForCommunity(address).toLowerCase();

        // 仅使用数据库 name 做匹配；匹配成功后优先返回 alias（第一个别名），否则返回 name。
        for (const [key, community] of this.communityCache) {
            // 只把缓存中由 name 产生的 key 用于匹配：我们在 loadCommunities 中将 name 和 alias 都塞入了 cache，
            // 这里限制只使用 community.name 来构造比较。
            const nameKey = String(community.name || '').toLowerCase();
            if (!nameKey) continue;

            if (cleanedLower.includes(nameKey) || addressLower.includes(nameKey)) {
                if (community.alias && String(community.alias).trim().length > 0) {
                    // alias 可能是“别名1,别名2”；取第一个非空别名作为输出
                    const firstAlias = String(community.alias)
                        .split(',')
                        .map(s => s.trim())
                        .find(s => s.length > 0);
                    return firstAlias || community.name;
                }
                return community.name;
            }
        }

        // 未命中数据库，则不做通用关键词匹配，返回空
        return '';
    }

    /**
     * 从地址中提取小区名称（当数据库中没有匹配时）
     * 需求变更：不再做通用匹配，保留空实现，避免误判
     */
    extractCommunityFromAddress() {
        return '';
    }

    /**
     * 提取楼栋号
     * @param {string} address 地址
     * @returns {string} 楼栋号
     */
    extractBuilding(address) {
        // 先清洗道路门牌，避免如“石府路3号”被误判为楼栋
        const cleaned = this.sanitizeAddressForBuilding(address);

        // 楼栋号的正则表达式模式（优先识别 #楼、号楼、栋、座 等，不再匹配孤立“数字+号”）
        const buildingPatterns = [
            /([0-9]+)#楼/,
            /([0-9]+)号楼/,
            /([0-9]+)栋/,
            /([0-9]+)座/,
            /([0-9]+)幢/,
            /([A-Z]+)([0-9]+)栋/,
            /([A-Z]+)([0-9]+)号楼/,
            /([A-Z]+)([0-9]+)座/
        ];

        for (const pattern of buildingPatterns) {
            const match = cleaned.match(pattern);
            if (match) {
                return match[0];
            }
        }

        return '';
    }

    /**
     * 提取房号
     * @param {string} address 地址
     * @returns {string} 房号
     */
    extractRoomNumber(address) {
        // 先去掉可能的“建筑物编号”以免并入房号，例如“36#楼501室”
        let addr = address
            .replace(/([0-9]+)#楼/g, '')
            .replace(/([0-9]+)号楼/g, '')
            .replace(/([0-9]+)栋/g, '')
            .replace(/([0-9]+)座/g, '')
            .replace(/([0-9]+)幢/g, '')
            .replace(/([A-Z]+)([0-9]+)栋/g, '')
            .replace(/([A-Z]+)([0-9]+)号楼/g, '')
            .replace(/([A-Z]+)([0-9]+)座/g, '');

        // 房号的正则表达式模式（补充 #楼 场景）
        const roomPatterns = [
            /([0-9]+)室/,
            /([0-9]+)号/,
            /([0-9]+)房/,
            /([0-9]+)-([0-9]+)/,
            /([0-9]+)楼([0-9]+)室/,
            /([0-9]+)楼([0-9]+)号/,
            /([0-9]+)层([0-9]+)室/,
            /([0-9]+)层([0-9]+)号/,
            /([0-9]+)#楼([0-9]+)室/, // 兼容未被清洗情况
            /([0-9]+)#楼([0-9]+)号/
        ];

        for (const pattern of roomPatterns) {
            const match = addr.match(pattern);
            if (match) {
                return match[0];
            }
        }

        return '';
    }

    /**
     * 批量解析地址
     * @param {Array} addresses 地址数组
     * @returns {Array} 解析结果数组
     */
    async parseAddresses(addresses) {
        const results = [];

        for (const address of addresses) {
            const result = await this.parseAddress(address);
            results.push(result);
        }

        return results;
    }

    /**
     * 添加小区信息到数据库（自适应表结构）
     * @param {object} communityInfo 小区信息
     * @returns {boolean} 是否添加成功
     */
    async addCommunity(communityInfo) {
        try {
            const { v4: uuidv4 } = require('uuid');
            const { execute } = require('../config/database');

            const cols = await this.getCommunitiesColumns();
            const id = uuidv4();
            const now = new Date().toISOString();

            // 构建可用字段
            const fields = ['id', 'name'];
            const values = [id, communityInfo.name];

            if (cols.has('alias')) {
                fields.push('alias');
                values.push(communityInfo.alias || '');
            }
            if (cols.has('district')) {
                fields.push('district');
                values.push(communityInfo.district || '');
            }
            if (cols.has('city')) {
                fields.push('city');
                values.push(communityInfo.city || '');
            }
            if (cols.has('province')) {
                fields.push('province');
                values.push(communityInfo.province || '');
            }
            if (cols.has('address')) {
                fields.push('address');
                values.push(communityInfo.address || '');
            }

            if (cols.has('status')) {
                fields.push('status');
                values.push(1);
            }
            if (cols.has('created_at')) {
                fields.push('created_at');
                values.push(now);
            }
            if (cols.has('updated_at')) {
                fields.push('updated_at');
                values.push(now);
            }

            const placeholders = fields.map(() => '?').join(', ');
            const sql = `INSERT INTO communities (${fields.join(', ')}) VALUES (${placeholders})`;

            await execute(sql, values);

            // 清除缓存，下次加载时会重新加载
            this.communityCache.clear();
            this.lastCacheUpdate = 0;

            logger.info('小区信息添加成功', { communityName: communityInfo.name });
            return true;
        } catch (error) {
            logger.error('添加小区信息失败', error);
            return false;
        }
    }

    /**
     * 获取所有小区信息（自适应表结构）
     * @returns {Array} 小区信息数组
     */
    async getAllCommunities() {
        try {
            const cols = await this.getCommunitiesColumns();
            const selectable = ['id', 'name'];
            if (cols.has('alias')) selectable.push('alias');
            if (cols.has('district')) selectable.push('district');
            if (cols.has('city')) selectable.push('city');
            if (cols.has('province')) selectable.push('province');
            if (cols.has('address')) selectable.push('address');
            if (cols.has('status')) selectable.push('status');
            if (cols.has('created_at')) selectable.push('created_at');
            if (cols.has('updated_at')) selectable.push('updated_at');

            const sql = `SELECT ${selectable.join(', ')} FROM communities WHERE status = 1 ORDER BY name`;

            return await query(sql);
        } catch (error) {
            logger.error('获取小区信息失败', error);
            return [];
        }
    }
}

// 创建单例实例
const addressParser = new AddressParser();

module.exports = addressParser;