<template>
  <div class="repair-settlement">
    <el-card class="settlement-card">
        <!-- 工程选择 -->
      <div class="project-selector">
        <el-form :inline="true">
          <el-form-item label="选择工程">
              <el-select
                v-model="formData.projectId"
              placeholder="请选择要结算的工程"
              style="width: 300px"
                @change="onProjectChange"
              >
                <el-option
                  v-for="project in projectList"
                  :key="project.id"
                  :label="`${project.name} (${project.status})`"
                  :value="project.id"
                  :disabled="project.status !== '在建'"
                />
              </el-select>
            </el-form-item>
        </el-form>
      </div>

      <!-- 详情标签页 -->
      <div class="module-tabs" v-if="formData.projectId">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="封面" name="cover">
            <div class="tab-content">
              <div class="settlement-cover">
                <div class="cover-header">
                  <el-select
                    v-model="coverData.companyName"
                    filterable
                    allow-create
                    clearable
                    placeholder="请输入或选择公司名称"
                    class="company-name-input"
                    style="width: 100%;"
                  >
                    <el-option
                      v-for="item in companyNameOptions"
                      :key="item"
                      :label="item"
                      :value="item"
                    />
                  </el-select>
                  <h2 class="document-title">项目结算书</h2>
                </div>
                <div class="cover-content">
                  <div class="left-column">
                    <div class="form-row">
                      <span class="label">项目名称:</span>
                      <span class="value">{{ coverData.projectName }}</span>
                    </div>
                    <div class="form-row">
                      <span class="label">CEA编码:</span>
                      <el-input v-model="coverData.ceaCode" placeholder="请输入CEA编码" clearable style="max-width: 300px;" />
                    </div>
                    <div class="form-row">
                      <span class="label">送审人工费:</span>
                      <span class="value">{{ coverData.submittedLaborCost }}元</span>
                    </div>
                    <div class="form-row">
                      <span class="label">二审造价:</span>
                      <span class="value">{{ coverData.secondReviewCost }}元</span>
                    </div>
                    <div class="form-row">
                      <span class="label">结算编号:</span>
                      <span class="value">{{ coverData.settlementNumber }}</span>
                    </div>
                    <div class="form-row">
                      <span class="label">应付金额:</span>
                      <span class="value">{{ coverData.amountPayableChinese }}</span>
                      <span class="currency-symbol">¥: {{ coverData.amountPayable }}元</span>
                    </div>
                    <div class="form-row">
                      <span class="label">施工单位:</span>
                      <el-select
                        v-model="coverData.constructionUnit"
                        filterable
                        allow-create
                        clearable
                        placeholder="请输入或选择施工单位"
                        style="max-width: 300px; width: 100%;"
                      >
                        <el-option
                          v-for="item in constructionUnitOptions"
                          :key="item"
                          :label="item"
                          :value="item"
                        />
                      </el-select>
                    </div>
                    <div class="form-row">
                      <span class="label">建设单位:</span>
                      <el-select
                        v-model="coverData.clientUnit"
                        filterable
                        allow-create
                        clearable
                        placeholder="请输入或选择建设单位"
                        style="max-width: 300px; width: 100%;"
                      >
                        <el-option
                          v-for="item in clientUnitOptions"
                          :key="item"
                          :label="item"
                          :value="item"
                        />
                      </el-select>
                    </div>
                  </div>
                  <div class="right-column">
                    <div class="form-row">
                      <span class="label">总造价:</span>
                      <span class="value">{{ coverData.totalCost }}元</span>
                    </div>
                    <div class="form-row">
                      <span class="label">人工结算:</span>
                      <span class="value">{{ coverData.laborSettlement }}元</span>
                    </div>
                    <div class="form-row">
                      <span class="label">材料结算:</span>
                      <span class="value">{{ coverData.materialSettlement }}元</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="分部分项汇总" name="summary">
            <el-table :data="summaryList" border style="width: 100%">
              <el-table-column type="index" label="序号" width="60" />
              <el-table-column prop="desc" label="项目及说明" />
              <el-table-column prop="labor" label="合同内人工(不含材料费)" width="160" />
              <el-table-column prop="material" label="乙供材料" width="120" />
              <el-table-column prop="remark" label="备注" />
            </el-table>
            <div class="table-summary-row">工程总人工：<b>{{ summaryTotalLabor }}</b></div>
          </el-tab-pane>
          <el-tab-pane label="综合单价结算" name="unitPrice">
            <el-table :data="unitPriceList" border style="width: 100%">
            <el-table-column type="index" label="序号" width="60" />
              <el-table-column prop="name" label="名称" />
              <el-table-column prop="model" label="型号" />
              <el-table-column prop="unit" label="单位" width="60" />
              <el-table-column prop="quantity" label="数量" width="80" />
              <el-table-column prop="unitLaborPrice" label="人工单价(元)" width="100" />
              <el-table-column prop="totalLaborPrice" label="人工总价(元)" width="120" />
              <el-table-column prop="remark" label="备注" />
          </el-table>
            <div class="table-summary-row">乙供材料</div>
            <div class="table-summary-row">人工总计：<b>{{ unitPriceTotalLabor }}</b></div>
            <div class="table-summary-row">金额总计：<b>{{ unitPriceTotalAmount }}</b></div>
          </el-tab-pane>
          <el-tab-pane label="乙供材料" name="material">
            <el-table :data="materialList" border style="width: 100%">
            <el-table-column type="index" label="序号" width="60" />
              <el-table-column prop="name" label="名称" />
              <el-table-column prop="model" label="型号" />
              <el-table-column prop="unit" label="单位" width="60" />
              <el-table-column prop="quantity" label="数量" width="80" />
              <el-table-column prop="unitMaterialPrice" label="材料单价(元)" width="100" />
              <el-table-column prop="totalMaterialPrice" label="材料总价(元)" width="120" />
              <el-table-column prop="remark" label="备注" />
          </el-table>
            <div class="table-summary-row">安装小计</div>
            <div class="table-summary-row">材料总计：<b>{{ materialTotal }}</b></div>
            <div class="table-summary-row">金额总计：<b>{{ materialTotal }}</b></div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, watch } from 'vue'
import type { FormRules } from 'element-plus'
import request from '@/utils/request'
import { onMounted } from 'vue'

const activeTab = ref('cover')

const projectList = ref([
  { id: 1, name: '阳光小区A栋', status: '在建' },
  { id: 2, name: '花园广场项目', status: '在建' },
  { id: 3, name: '商业中心B区', status: '在建' }
])

const formData = reactive({
  projectId: ''
})

const formRules: FormRules = {
  projectId: [
    { required: true, message: '请选择工程', trigger: 'change' }
  ]
}

const onProjectChange = (projectId: string) => {
  // 可根据需要处理选中工程后的逻辑
}

async function fetchSettlementData(projectId) {
  // 分部分项汇总
  const summaryRes = await request.get('/api/project/settlement/summary')
  if (summaryRes && summaryRes.code === 200 && Array.isArray(summaryRes.data)) {
    summaryList.value = summaryRes.data
  }
  // 综合单价结算
  const unitPriceRes = await request.get('/api/project/settlement/unit/price')
  if (unitPriceRes && unitPriceRes.code === 200 && Array.isArray(unitPriceRes.data)) {
    unitPriceList.value = unitPriceRes.data
  }
  // 乙供材料
  const materialRes = await request.get('/api/project/settlement/material')
  if (materialRes && materialRes.code === 200 && Array.isArray(materialRes.data)) {
    materialList.value = materialRes.data
  }
}

onMounted(async () => {
  const res = await request.get('/api/projects')
  if (res && res.code === 200 && res.data && res.data.list) {
    projectList.value = res.data.list.map(item => ({
      id: item.id,
      name: item.projectName,
      status: item.projectStatus
    }))
  }
  // 默认加载第一个工程的数据
  if (projectList.value.length > 0) {
    formData.projectId = projectList.value[0].id
    fetchSettlementData(formData.projectId)
  }
})

watch(() => formData.projectId, (newId) => {
  if (newId) fetchSettlementData(newId)
})

// 封面相关数据
const companyNameOptions = ref(['公司A', '公司B'])
const constructionUnitOptions = ref(['施工单位A', '施工单位B'])
const clientUnitOptions = ref(['建设单位A', '建设单位B'])
const coverData = reactive({
  companyName: '',
  projectName: '',
  ceaCode: '',
  submittedLaborCost: '',
  secondReviewCost: '',
  settlementNumber: '',
  amountPayableChinese: '',
  amountPayable: '',
  constructionUnit: '',
  clientUnit: '',
  totalCost: '',
  laborSettlement: '',
  materialSettlement: ''
})

// 其它标签页的表格数据
const summaryList = ref([])
const unitPriceList = ref([])
const materialList = ref([])

const summaryTotalLabor = computed(() => {
  return summaryList.value.reduce((sum, item) => sum + Number(item.labor || 0), 0).toFixed(2)
})

// 在<script setup>中添加 materialTotal 计算属性
const materialTotal = computed(() => {
  return materialList.value.reduce((sum, item) => sum + Number(item.totalMaterialPrice || 0), 0).toFixed(2)
})

// 在<script setup>中添加 unitPriceTotalLabor 和 unitPriceTotalAmount 计算属性
const unitPriceTotalLabor = computed(() => {
  return unitPriceList.value.reduce((sum, item) => sum + Number(item.totalLaborPrice || 0), 0).toFixed(2)
})
const unitPriceTotalAmount = computed(() => {
  // 如有其它金额字段可累加，这里暂用人工总价合计
  return unitPriceTotalLabor.value
})

// 在<script setup>中添加 getSummarySummary 方法
const getSummarySummary = ({ columns, data }) => {
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = ''
    } else if (column.property === 'desc') {
      sums[index] = '工程总人工：'
    } else if (column.property === 'labor') {
      sums[index] = data.reduce((sum, row) => sum + Number(row.labor || 0), 0).toFixed(2)
    } else {
      sums[index] = ''
    }
  })
  return sums
}
</script>

<style lang="scss" scoped>
.repair-settlement {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  .settlement-card {
    margin-bottom: 20px;
    .project-selector {
      margin-bottom: 20px;
    }
    .module-tabs {
      .tab-content {
        padding: 20px 0;
      }
      .settlement-cover {
        .cover-header {
          display: flex;
          align-items: center;
          gap: 20px;
    margin-bottom: 20px;
          .company-name-input {
            max-width: 300px;
          }
          .document-title {
            flex: 1;
            text-align: center;
            font-size: 22px;
      font-weight: bold;
          }
        }
        .cover-content {
          display: flex;
          gap: 40px;
          .left-column, .right-column {
            flex: 1;
          }
          .form-row {
            margin-bottom: 12px;
      .label {
              display: inline-block;
              width: 90px;
              color: #666;
            }
            .value {
        font-weight: bold;
              color: #222;
            }
            .currency-symbol {
              margin-left: 10px;
        color: #409eff;
      }
          }
        }
      }
    }
  }
}
/* 特殊样式 */
.form-row:nth-child(6) .value {
  font-weight: bold;
  color: #409eff;
  font-size: 16px;
}
.form-row:nth-child(6) .currency-symbol {
  font-size: 16px;
  color: #409eff;
  font-weight: bold;
}
/* 公司名称输入框样式 */
.company-name-input {
  font-size: 18px;
  font-weight: normal;
  text-align: center;
  margin: 0 0 10px 0;
  width: 100%;
}
/* 样式部分 */
.summary-table-wrapper {
  max-width: 900px;
  margin: 0 auto;
  background: #fff;
  padding: 32px 24px 24px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.06);
}
.summary-title {
  text-align: center;
  font-size: 26px;
  font-weight: bold;
  letter-spacing: 12px;
  margin-bottom: 18px;
  text-decoration: underline;
  text-underline-offset: 4px;
}
.summary-meta {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 16px;
  margin-bottom: 10px;
}
.summary-label {
  font-weight: bold;
}
.summary-value {
  margin-right: 32px;
  font-weight: bold;
}
.summary-page {
  margin-left: auto;
  font-size: 15px;
}
.summary-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  font-size: 16px;
}
.summary-table th, .summary-table td {
  border: 1px solid #333;
  padding: 8px 6px;
  text-align: center;
}
.summary-table .text-right {
  text-align: right;
  padding-right: 16px;
}
/* 抽查表格样式 */
.spot-check-table-wrapper {
  max-width: 1000px;
  margin: 0 auto;
  background: #fff;
  padding: 32px 24px 24px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.06);
}
.spot-check-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 16px;
}
.spot-check-table th, .spot-check-table td {
  border: 1px solid #333;
  padding: 8px 6px;
  text-align: center;
}
.spot-check-table .remark-col {
  background: #ffd6d6;
  color: #b30000;
  font-weight: bold;
}
/* 封面页面样式 */
.settlement-cover {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px;
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.cover-header {
    text-align: center;
  margin-bottom: 40px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 20px;
}
.company-name {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin: 0 0 10px 0;
}
.document-title {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
  margin: 0;
  text-decoration: underline;
  text-decoration-color: #409eff;
  text-decoration-thickness: 2px;
}
.cover-content {
  display: flex;
  gap: 60px;
}
.left-column,
.right-column {
  flex: 1;
}
.form-row {
  display: flex;
  margin-bottom: 15px;
  align-items: center;
  min-height: 30px;
}
.form-row .label {
  font-weight: bold;
  color: #303133;
  min-width: 120px;
  margin-right: 10px;
}
.form-row .value {
  color: #606266;
  flex: 1;
}
.form-row .currency-symbol {
  color: #409eff;
  font-weight: bold;
  margin-left: 10px;
}
.table-summary-row {
  border: 1px solid #dcdfe6;
  border-top: none;
  padding: 8px 16px;
  font-weight: bold;
  background: #fafafa;
  width: 100%;
}
</style>