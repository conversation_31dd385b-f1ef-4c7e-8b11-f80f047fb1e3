# 工程管理系统 API 文档

## 概述

工程管理系统后端API提供RESTful风格的接口，支持用户认证、仪表板数据、物料管理、订单管理等功能。

## 基础信息

- **基础URL**: `http://localhost:3000/api`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证

### 获取Token

```http
POST /api/auth/login
Content-Type: application/json

{
    "username": "admin",
    "password": "password"
}
```

响应示例：
```json
{
    "success": true,
    "message": "登录成功",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "user": {
            "id": 1,
            "username": "admin",
            "realName": "系统管理员",
            "role": "admin"
        }
    }
}
```

### 使用Token

在请求头中添加：
```
Authorization: Bearer <your-token>
```

## 仪表板API

### 获取未使用物料统计

```http
GET /api/dashboard/materials/unused
Authorization: Bearer <token>
```

响应示例：
```json
{
    "success": true,
    "message": "获取未使用物料统计成功",
    "data": {
        "totalItems": 4,
        "totalPrice": 12000,
        "details": [
            {
                "id": 1,
                "name": "管道",
                "category": "甲料",
                "unusedQuantity": 80,
                "price": 50,
                "totalPrice": 4000
            }
        ]
    }
}
```

### 获取工程进度统计

```http
GET /api/dashboard/projects/status
Authorization: Bearer <token>
```

响应示例：
```json
{
    "success": true,
    "message": "获取工程进度统计成功",
    "data": {
        "counts": {
            "not_started": 1,
            "in_progress": 2,
            "paused": 1,
            "completed": 1
        },
        "details": [
            {
                "id": 2,
                "name": "项目B",
                "status": "in_progress"
            }
        ]
    }
}
```

## 错误处理

### 错误响应格式

```json
{
    "success": false,
    "message": "错误描述",
    "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 常见HTTP状态码

- `200`: 请求成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 分页

支持分页的接口使用以下参数：

- `page`: 页码（从1开始）
- `pageSize`: 每页数量（默认20，最大100）

响应格式：
```json
{
    "success": true,
    "message": "获取成功",
    "data": {
        "list": [...],
        "pagination": {
            "total": 100,
            "page": 1,
            "pageSize": 20,
            "totalPages": 5
        }
    }
}
```

## 文件上传

文件上传接口使用 `multipart/form-data` 格式：

```http
POST /api/upload/image
Content-Type: multipart/form-data
Authorization: Bearer <token>

file: <file-data>
```

## 默认用户

系统预置了以下默认用户：

| 用户名 | 密码 | 角色 | 描述 |
|--------|------|------|------|
| admin | password | admin | 系统管理员 |
| manager | password | manager | 项目经理 |
| worker | password | worker | 普通员工 |

## 开发环境

### 启动服务

```bash
# 安装依赖
npm install

# 开发模式
npm run dev

# 生产模式
npm start
```

### 运行测试

```bash
# 运行所有测试
npm test

# 生成覆盖率报告
npm run test:coverage
```

## 部署

### 使用Docker

```bash
# 构建镜像
docker build -t suijian-backend .

# 运行容器
docker run -p 3000:3000 suijian-backend
```

### 使用PM2

```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start ecosystem.config.js
``` 