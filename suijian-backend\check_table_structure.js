const { query } = require('./src/config/database');

async function checkTableStructure() {
    try {
        console.log('检查loose_orders表结构...');

        // 获取表结构
        const structure = await query("PRAGMA table_info(loose_orders)");
        console.log('表结构:');
        structure.forEach(column => {
            console.log(`  ${column.name} (${column.type})`);
        });

        // 检查是否有address字段
        const hasAddress = structure.some(col => col.name === 'address');
        const hasPartyAddress = structure.some(col => col.name === 'party_address');

        console.log('\n字段检查:');
        console.log('  address字段存在:', hasAddress);
        console.log('  party_address字段存在:', hasPartyAddress);

    } catch (error) {
        console.error('检查表结构失败:', error);
    }
}

checkTableStructure();