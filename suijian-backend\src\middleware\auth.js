const jwt = require('jsonwebtoken');
const Response = require('../utils/response');

/**
 * JWT认证中间件
 */
const authMiddleware = (req, res, next) => {
    try {
        // 从请求头获取token
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return Response.unauthorized(res, '访问被拒绝，需要token');
        }

        const token = authHeader.substring(7); // 去掉 'Bearer ' 前缀

        // 验证token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        // 将用户信息添加到请求对象
        req.user = decoded;

        next();
    } catch (error) {
        if (error.name === 'TokenExpiredError') {
            return Response.unauthorized(res, 'token已过期');
        } else if (error.name === 'JsonWebTokenError') {
            return Response.unauthorized(res, '无效的token');
        } else {
            return Response.serverError(res, 'token验证失败');
        }
    }
};

/**
 * 角色权限中间件
 * @param {Array} allowedRoles - 允许的角色数组
 */
const roleMiddleware = (allowedRoles) => {
    return (req, res, next) => {
        if (!req.user) {
            return Response.unauthorized(res, '用户未认证');
        }

        if (!allowedRoles.includes(req.user.role)) {
            return Response.forbidden(res, '权限不足');
        }

        next();
    };
};

/**
 * 管理员权限中间件
 */
const adminMiddleware = roleMiddleware(['admin']);

/**
 * 管理员或经理权限中间件
 */
const managerMiddleware = roleMiddleware(['admin', 'manager']);

module.exports = {
    authMiddleware,
    roleMiddleware,
    adminMiddleware,
    managerMiddleware
};