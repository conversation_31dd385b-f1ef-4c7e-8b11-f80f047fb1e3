-- 为loose_orders表添加甲单相关字段
-- 执行时间: 2024年

-- 甲单地址字段
ALTER TABLE loose_orders ADD COLUMN party_address TEXT COMMENT '甲单地址';

-- 甲单诉求描述字段
ALTER TABLE loose_orders ADD COLUMN party_appeal_description TEXT COMMENT '甲单诉求描述';

-- 甲单费用合计金额字段
ALTER TABLE loose_orders ADD COLUMN party_total_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '甲单费用合计金额';

-- 甲单备注字段
ALTER TABLE loose_orders ADD COLUMN party_remarks TEXT COMMENT '甲单备注';

-- 为甲单地址创建索引
CREATE INDEX idx_loose_orders_party_address ON loose_orders(party_address(100));

-- 为甲单费用金额创建索引
CREATE INDEX idx_loose_orders_party_total_amount ON loose_orders(party_total_amount);

-- 更新现有记录的甲单字段默认值（可选）
-- UPDATE loose_orders SET 
--     party_address = address,
--     party_appeal_description = appeal_description,
--     party_total_amount = total_amount,
--     party_remarks = remarks
-- WHERE party_address IS NULL; 