const axios = require('axios');

// 测试API端点
const testAPI = async() => {
    const baseURL = 'http://localhost:3000';

    console.log('测试API连接...');

    try {
        // 测试健康检查
        const healthResponse = await axios.get(`${baseURL}/health`);
        console.log('健康检查成功:', healthResponse.data);

        // 测试loose-orders API
        const ordersResponse = await axios.get(`${baseURL}/api/loose-orders`);
        console.log('loose-orders API响应:', ordersResponse.data);

    } catch (error) {
        console.error('API测试失败:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
};

testAPI();