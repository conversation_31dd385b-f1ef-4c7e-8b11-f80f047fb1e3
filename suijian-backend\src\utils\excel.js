const ExcelJS = require('exceljs');
const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');

/**
 * Excel处理工具类
 */
class ExcelUtil {
    /**
     * 读取Excel工作簿
     * @param {string} filePath - 文件路径
     * @returns {Promise<Object>} 返回工作簿对象
     */
    static async readWorkbook(filePath) {
        try {
            // 检查文件是否存在
            if (!require('fs').existsSync(filePath)) {
                throw new Error(`文件不存在: ${filePath}`);
            }

            // 检查文件大小
            const stats = require('fs').statSync(filePath);
            if (stats.size === 0) {
                throw new Error('文件为空');
            }

            console.log(`尝试读取文件: ${filePath}, 大小: ${stats.size} bytes`);

            const ext = require('path').extname(filePath).toLowerCase();

            if (ext === '.xls') {
                // 对于.xls文件，使用xlsx库
                console.log('检测到.xls文件，使用xlsx库读取...');
                const workbook = XLSX.readFile(filePath);
                return workbook;
            } else {
                // 对于.xlsx文件，使用ExcelJS库
                console.log('检测到.xlsx文件，使用ExcelJS库读取...');
                const workbook = new ExcelJS.Workbook();
                await workbook.xlsx.readFile(filePath);
                return workbook;
            }
        } catch (error) {
            console.error('Excel读取错误详情:', {
                filePath,
                error: error.message,
                stack: error.stack
            });

            // 提供更详细的错误信息
            if (error.message.includes('Can\'t find end of central directory')) {
                const ext = require('path').extname(filePath).toLowerCase();
                if (ext === '.xls') {
                    throw new Error('文件格式错误：检测到.xls文件，但ExcelJS库不支持旧版Excel格式。请将文件转换为.xlsx格式后重新上传');
                } else {
                    throw new Error('文件格式错误：请确保上传的是有效的Excel文件(.xlsx格式)，文件可能已损坏或格式不正确');
                }
            } else if (error.message.includes('Invalid file signature')) {
                throw new Error('文件格式错误：请确保上传的是Excel文件，而不是其他格式的文件');
            } else if (error.message.includes('Unexpected end of data')) {
                throw new Error('文件格式错误：文件可能已损坏或不完整');
            } else {
                throw new Error(`读取Excel工作簿失败: ${error.message}`);
            }
        }
    }

    /**
     * 将工作表转换为JSON数组
     * @param {Object} worksheet - 工作表对象
     * @param {Object} options - 选项
     * @returns {Array} 返回JSON数组，包含所有行（第一行作为标题）
     */
    static sheet_to_json(worksheet, options = {}) {
            const data = [];
            const header = options.header || 1;

            // 检查worksheet的类型（ExcelJS vs XLSX）
            if (worksheet.eachRow) {
                // ExcelJS格式 - 包含所有行，第一行作为标题
                worksheet.eachRow((row, rowNumber) => {
                    const rowData = [];
                    row.eachCell((cell, colNumber) => {
                        rowData.push(cell.value || '');
                    });
                    data.push(rowData);
                });
            } else {
                // XLSX格式 - 包含所有行，第一行作为标题
                const range = XLSX.utils.decode_range(worksheet['!ref']);
                for (let row = range.s.r; row <= range.e.r; row++) {
                    const rowData = [];
                    for (let col = range.s.c; col <= range.e.c; col++) {
                        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                        const cell = worksheet[cellAddress];
                        rowData.push(cell ? cell.v : '');
                    }
                    data.push(rowData);
                }
            }

            return data;
        }
        /**
         * 读取Excel文件
         * @param {string} filePath - 文件路径
         * @param {number} sheetIndex - 工作表索引，默认为0
         * @returns {Promise<Array>} 返回数据数组
         */
    static async readExcel(filePath, sheetIndex = 0) {
        try {
            const workbook = new ExcelJS.Workbook();
            await workbook.xlsx.readFile(filePath);

            const worksheet = workbook.worksheets[sheetIndex];
            if (!worksheet) {
                throw new Error('工作表不存在');
            }

            const data = [];
            worksheet.eachRow((row, rowNumber) => {
                if (rowNumber === 1) return; // 跳过表头

                const rowData = {};
                row.eachCell((cell, colNumber) => {
                    rowData[`col${colNumber}`] = cell.value;
                });
                data.push(rowData);
            });

            return data;
        } catch (error) {
            throw new Error(`读取Excel文件失败: ${error.message}`);
        }
    }

    /**
     * 写入Excel文件
     * @param {Array} data - 数据数组
     * @param {Array} headers - 表头数组
     * @param {string} filePath - 输出文件路径
     * @param {string} sheetName - 工作表名称
     * @returns {Promise<string>} 返回文件路径
     */
    static async writeExcel(data, headers, filePath, sheetName = 'Sheet1') {
        try {
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet(sheetName);

            // 添加表头
            worksheet.addRow(headers);

            // 添加数据
            data.forEach(row => {
                const rowData = headers.map(header => row[header] || '');
                worksheet.addRow(rowData);
            });

            // 确保目录存在
            const dir = path.dirname(filePath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            // 保存文件
            await workbook.xlsx.writeFile(filePath);
            return filePath;
        } catch (error) {
            throw new Error(`写入Excel文件失败: ${error.message}`);
        }
    }

    /**
     * 导出物料数据到Excel
     * @param {Array} materials - 物料数据
     * @param {string} fileName - 文件名
     * @returns {Promise<string>} 返回文件路径
     */
    static async exportMaterials(materials, fileName = 'materials.xlsx') {
        const headers = [
            '公司物料编码',
            '甲方编码',
            '物料名称',
            '型号',
            '规格',
            '单位',
            '物料分类',
            '单价',
            '库存数量',
            '预警数量',
            '状态'
        ];

        const data = materials.map(material => ({
            '公司物料编码': material.companyCode,
            '甲方编码': material.partyCode,
            '物料名称': material.name,
            '型号': material.model,
            '规格': material.specification,
            '单位': material.unit,
            '物料分类': material.category,
            '单价': material.price,
            '库存数量': material.stockQuantity,
            '预警数量': material.warningQuantity,
            '状态': material.status === 1 ? '启用' : '禁用'
        }));

        const filePath = path.join(process.cwd(), 'uploads', 'excel', fileName);
        return await this.writeExcel(data, headers, filePath, '物料列表');
    }

    /**
     * 导出订单数据到Excel
     * @param {Array} orders - 订单数据
     * @param {string} fileName - 文件名
     * @returns {Promise<string>} 返回文件路径
     */
    static async exportOrders(orders, fileName = 'orders.xlsx') {
        const headers = [
            '订单号',
            '客户姓名',
            '用户编号',
            '小区名称',
            '楼栋',
            '房号',
            '电话',
            '联系人',
            '订单分类',
            '工程名称',
            '订单状态',
            '安装时间',
            '备注',
            '跟进师傅'
        ];

        const data = orders.map(order => ({
            '订单号': order.orderNo,
            '客户姓名': order.customerName,
            '用户编号': order.customerCode,
            '小区名称': order.communityName,
            '楼栋': order.building,
            '房号': order.roomNumber,
            '电话': order.phone,
            '联系人': order.contactPerson,
            '订单分类': order.orderType,
            '工程名称': order.projectName,
            '订单状态': order.status,
            '安装时间': order.installationDate,
            '备注': order.remarks,
            '跟进师傅': order.assignedWorkerName
        }));

        const filePath = path.join(process.cwd(), 'uploads', 'excel', fileName);
        return await this.writeExcel(data, headers, filePath, '订单列表');
    }

    /**
     * 导出员工数据到Excel
     * @param {Array} employees - 员工数据
     * @param {string} fileName - 文件名
     * @returns {Promise<string>} 返回文件路径
     */
    static async exportEmployees(employees, fileName = 'employees.xlsx') {
        const headers = [
            '员工姓名',
            '工种',
            '工价',
            '手机号',
            '状态'
        ];

        const data = employees.map(employee => ({
            '员工姓名': employee.name,
            '工种': employee.workType,
            '工价': employee.wage,
            '手机号': employee.phone,
            '状态': employee.status === 1 ? '在职' : '离职'
        }));

        const filePath = path.join(process.cwd(), 'uploads', 'excel', fileName);
        return await this.writeExcel(data, headers, filePath, '员工列表');
    }

    /**
     * 导入物料数据
     * @param {string} filePath - Excel文件路径
     * @returns {Promise<Array>} 返回物料数据数组
     */
    static async importMaterials(filePath) {
        const data = await this.readExcel(filePath);
        return data.map(row => ({
            companyCode: row.col1,
            partyCode: row.col2,
            name: row.col3,
            model: row.col4,
            specification: row.col5,
            unit: row.col6,
            category: row.col7,

            stockQuantity: parseInt(row.col8) || 0, // 第9列为库存数量
            warningQuantity: parseInt(row.col9) || 0, // 第10列为预警数量
            status: 1 // 导入时全部为启用
        }));
    }

    /**
     * 导入员工数据
     * @param {string} filePath - Excel文件路径
     * @returns {Promise<Array>} 返回员工数据数组
     */
    static async importEmployees(filePath) {
        const data = await this.readExcel(filePath);

        return data.map(row => ({
            name: row.col1,
            workType: row.col2,
            wage: parseFloat(row.col3) || 0,
            phone: row.col4,
            status: row.col5 === '在职' ? 1 : 0
        }));
    }
}

module.exports = ExcelUtil;