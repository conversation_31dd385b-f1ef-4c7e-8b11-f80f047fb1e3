# 应用配置
NODE_ENV=development
PORT=3000
APP_NAME=工程管理系统
APP_VERSION=1.0.0

# JWT配置
JWT_SECRET=your_jwt_secret_key_development
JWT_EXPIRES_IN=24h

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=209715200
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,xlsx,xls,doc,docx

# 日志配置
LOG_LEVEL=debug
LOG_FILE=./logs/app.log

# 跨域配置
CORS_ORIGIN=http://localhost:8080

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# Redis配置（可选，用于会话存储）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 第三方服务配置
ALIYUN_OSS_ACCESS_KEY_ID=
ALIYUN_OSS_ACCESS_KEY_SECRET=
ALIYUN_OSS_BUCKET=
ALIYUN_OSS_REGION=

# 安全配置
BCRYPT_ROUNDS=10
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100 