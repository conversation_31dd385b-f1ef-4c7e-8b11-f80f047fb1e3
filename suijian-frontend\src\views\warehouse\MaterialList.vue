<template>
  <div class="page-container">
    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键字">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入物料编码、名称、型号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="searchForm.category" placeholder="请选择分类" clearable style="width: 150px">
            <el-option label="甲料" value="甲料" />
            <el-option label="乙料" value="乙料" />
            <el-option label="商品" value="商品" />
            <el-option label="辅料" value="辅料" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" @click="handleAdd">新增物料</el-button>
          <el-button type="warning" @click="handleExport">导出Excel</el-button>
          <el-button type="success" @click="importDialogVisible = true">导入Excel</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="companyCode" label="公司物料编码" width="150" sortable="custom" />
        <el-table-column prop="clientCodes" label="甲方编码" width="120" sortable="custom" />

        <el-table-column prop="category" label="分类" width="90" sortable="custom">
          <template #default="{ row }">
            <el-tag :type="getCategoryType(row.category)">{{ row.category }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称" min-width="150" sortable="custom" />
        <el-table-column prop="specification" label="规格" min-width="150" sortable="custom" />
        <el-table-column prop="unit" label="单位" width="80" sortable="custom" />
        <el-table-column prop="stockQuantity" label="数量" width="90" sortable="custom">
          <template #default="{ row }">
            <span :class="{ 'text-danger': row.stockQuantity <= 10 }">
              {{ row.stockQuantity }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" sortable="custom">
          <template #default="{ row }">
            <el-tag :type="row.status === '正常' ? 'success' : 'warning'">
              {{ row.status === '正常' ? '正常' : '需进仓' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="success" size="small" @click="handleAddSpecification(row)">新增甲方编码</el-button>
            <el-button type="info" size="small" @click="handleViewStockRecords(row)">查看库存记录</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="700px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="公司物料编码" prop="companyCode">
              <el-input 
                v-model="form.companyCode" 
                :placeholder="form.id ? '请输入公司物料编码' : '后台自动生成'"
                :disabled="!form.id || isAddClientCode"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入物料名称" :disabled="isAddClientCode" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="甲方编码" prop="clientCodes">
          <el-input
            v-model="form.clientCodes"
            placeholder="请输入甲方编码"
            style="width: 100%"
          />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="规格" prop="specification">
              <el-input v-model="form.specification" placeholder="请输入规格" :disabled="isAddClientCode" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-input v-model="form.unit" placeholder="请输入单位" :disabled="isAddClientCode" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类" prop="category">
              <el-select v-model="form.category" placeholder="请选择分类" :disabled="isAddClientCode">
                <el-option label="甲料" value="甲料" />
                <el-option label="乙料" value="乙料" />
                <el-option label="商品" value="商品" />
                <el-option label="辅料" value="辅料" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>


      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 导入Excel弹窗 -->
    <el-dialog v-model="importDialogVisible" title="导入物料Excel" width="400px">
      <el-upload
        class="upload-demo"
        drag
        :action="importExcelUrl"
        :show-file-list="false"
        :before-upload="beforeImportExcel"
        :on-success="handleImportSuccess"
        :on-error="handleImportError"
        :headers="uploadHeaders"
        :disabled="importing || importDone"
        name="file"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <div v-if="importing || importDone" style="margin-top: 20px;">
        <el-progress :percentage="importProgressPercent" :status="importProgressStatus" :text-inside="true" :stroke-width="20">
          <span v-if="importProgressStatus==='success'">{{importProgressMessage}}</span>
          <span v-else-if="importProgressStatus==='exception'">{{importProgressMessage}}</span>
          <span v-else>{{importProgressMessage}}</span>
        </el-progress>
      </div>
      <template #footer>
        <el-button @click="handleImportDialogClose">{{ importDone ? '完成' : '取消' }}</el-button>
      </template>
    </el-dialog>

    <!-- 库存记录弹窗 -->
    <el-dialog
      v-model="stockRecordsDialogVisible"
      title="库存记录"
      width="1200px"
      :close-on-click-modal="true"
    >
      <div v-if="currentMaterial">
        <div style="margin-bottom: 20px;">
          <h3>{{ currentMaterial.name }} - 库存记录</h3>
          <p>物料编码: {{ currentMaterial.companyCode }}</p>
        </div>
        
        <el-table :data="stockRecords" border style="width: 100%" v-loading="stockRecordsLoading">
          <el-table-column prop="created_at" label="日期" width="160">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column prop="order_id" label="单号" width="150" />
          <el-table-column prop="type" label="进出记录" width="120">
            <template #default="{ row }">
              <el-tag :type="getOperationTypeTag(row.type)">
                {{ getOperationTypeText(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="current_quantity" label="操作前数量" width="120" />
          <el-table-column prop="quantity" label="数量" width="100" />
          <el-table-column prop="new_quantity" label="操作后数量" width="120" />
          <el-table-column prop="recipient_name" label="申请人" width="100" />
          <el-table-column prop="remarks" label="备注" min-width="150" />
        </el-table>
      </div>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'

// 搜索表单
const searchForm = reactive({
  keyword: '',
  category: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 排序状态
const sortState = reactive({
  prop: '',
  order: ''
})



// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 对话框
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const isAddClientCode = ref(false) // 标识是否为新增甲方编码

// 库存记录相关
const stockRecordsDialogVisible = ref(false)
const currentMaterial = ref(null)
const stockRecords = ref([])
const stockRecordsLoading = ref(false)

// 表单数据
const form = reactive({
  id: '',
  companyCode: '',
  clientCodes: '',
  name: '',
  specification: '',
  unit: '',
  category: ''
})

// 表单验证规则
const rules = {
  companyCode: [{ required: false, message: '公司物料编码由后台自动生成', trigger: 'blur' }],
  name: [{ required: true, message: '请输入物料名称', trigger: 'blur' }],
  category: [{ required: true, message: '请选择分类', trigger: 'change' }],
  unit: [{ required: true, message: '请输入单位', trigger: 'blur' }],
  clientCodes: [{ required: false, message: '请输入甲方编码', trigger: 'blur' }]
}

// 获取分类标签类型
const getCategoryType = (category: string) => {
  const types = {
    '甲料': 'primary',
    '乙料': 'danger',
    '商品': 'success',
    '辅料': 'warning'
  }
  return types[category] || 'info'
}

// 格式化日期 - 显示北京时间
const formatDate = (timestamp: string) => {
  if (!timestamp) return ''
  
  // 创建日期对象并添加8小时转换为北京时间
  const date = new Date(timestamp)
  date.setHours(date.getHours() + 8)
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 获取操作类型标签
const getOperationTypeTag = (type: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  switch (type) {
    case 'in':
      return 'success'
    case 'product_in':
      return 'success'
    case 'out':
      return 'primary'
    case 'return':
      return 'warning'
    case 'apply':
      return 'info'
    default:
      return 'info'
  }
}

// 获取操作类型文本
const getOperationTypeText = (type: string): string => {
  switch (type) {
    case 'in':
      return '甲料入库'
    case 'product_in':
      return '商品入库'
    case 'out':
      return '出库'
    case 'return':
      return '退库'
    case 'apply':
      return '申请'
    default:
      return '未知'
  }
}





// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 排序处理
const handleSortChange = ({ prop, order }: { prop: string, order: string }) => {
  sortState.prop = prop
  sortState.order = order
  pagination.page = 1
  loadData()
}



// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    category: ''
  })
  // 重置排序状态
  sortState.prop = ''
  sortState.order = ''
  handleSearch()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增物料'
  isAddClientCode.value = false
  Object.assign(form, {
    id: '',
    companyCode: '',
    clientCodes: '',
    name: '',
    specification: '',
    unit: '',
    category: ''
  })
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: any) => {
  dialogTitle.value = '编辑物料'
  isAddClientCode.value = false
  Object.assign(form, {
    ...row
  })
  dialogVisible.value = true
}

// 增加不同型号规格
const handleAddSpecification = (row: any) => {
  dialogTitle.value = '新增甲方编码'
  isAddClientCode.value = true
  Object.assign(form, {
    id: '',
    companyCode: row.companyCode, // 保持相同的公司物料编码
    clientCodes: '', // 清空甲方编码，让用户输入新的
    name: row.name, // 自动填写相同的名称
    specification: row.specification, // 自动填写相同的规格
    unit: row.unit, // 自动填写相同的单位
    category: row.category // 自动填写相同的分类
  })
  dialogVisible.value = true
}

// 查看库存记录
const handleViewStockRecords = async (row: any) => {
  try {
    currentMaterial.value = row
    stockRecordsDialogVisible.value = true
    stockRecordsLoading.value = true
    
    // 加载库存记录
    const response = await request.get(`/api/materials/${row.id}/stock-records`)
    
    if (response && response.success && response.data) {
      stockRecords.value = response.data || []
    } else {
      ElMessage.error('加载库存记录失败')
      stockRecords.value = []
    }
  } catch (error) {
    console.error('加载库存记录失败:', error)
    ElMessage.error('加载库存记录失败')
    stockRecords.value = []
  } finally {
    stockRecordsLoading.value = false
  }
}

// 导出
const handleExport = async () => {
  try {
    const params = {
      category: searchForm.category,
      keyword: searchForm.keyword
    }
    // 关键：responseType: 'blob'
    const res = await request.instance.get('/api/materials/export', {
      params,
      responseType: 'blob'
    })
    // 获取文件名
    let fileName = '库存.xlsx'
    const disposition = res.headers && res.headers['content-disposition']
      console.log( res.headers)
    if (disposition) {
      const match = disposition.match(/filename="?([^";]+)"?/)
      if (match) fileName = decodeURIComponent(match[1])
    }
    // 创建Blob并下载
    const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(link.href)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error(error.message || '导出失败')
  }
}

// 导入Excel
const importDialogVisible = ref(false)
const importExcelUrl = '/api/materials/import' // 后端接口路径
const uploadHeaders = {
  Authorization: 'Bearer ' + (localStorage.getItem('auth_token') || '')
}

const importing = ref(false)
const importDone = ref(false)
const importTaskId = ref('')
const importProgressPercent = ref(0)
const importProgressStatus = ref<'success'|'exception'|'active'>('active')
const importProgressMessage = ref('')
let importProgressTimer: any = null

const beforeImportExcel = (file: File) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件')
  }
  return isExcel
}
const handleImportSuccess = (response: any) => {
  if (response && response.success && response.taskId) {
    importTaskId.value = response.taskId
    importing.value = true
    importDone.value = false
    importProgressPercent.value = 0
    importProgressStatus.value = 'active'
    importProgressMessage.value = '正在处理...'
    pollImportProgress()
  } else {
    ElMessage.error(response.message || '导入失败')
  }
}

const pollImportProgress = async () => {
  if (!importTaskId.value) return
  try {
    const res = await request.get('/api/materials/import/progress', {
      params: { taskId: importTaskId.value },
      headers: {
        Authorization: 'Bearer ' + (localStorage.getItem('auth_token') || '')
      }
    })
    if (res && res.success) {
      const { total, current, successCount, fail, status, message } = res
      if (total > 0) {
        importProgressPercent.value = Math.floor((current / total) * 100)
      } else {
        importProgressPercent.value = 0
      }
      if (status === 'done') {
        importProgressPercent.value = 100
        importProgressStatus.value = 'success'
        importProgressMessage.value = message || '导入完成'
        importing.value = false
        importDone.value = true
        importTaskId.value = ''
        clearTimeout(importProgressTimer)
        loadData()
        ElMessage.success(importProgressMessage.value)
      } else if (status === 'error') {
        importProgressStatus.value = 'exception'
        importProgressMessage.value = message || '导入失败'
        importing.value = false
        importDone.value = true
        importTaskId.value = ''
        clearTimeout(importProgressTimer)
        ElMessage.error(importProgressMessage.value)
      } else {
        if (total === 0) {
          importProgressMessage.value = '正在解析文件...'
        } else {
          importProgressMessage.value = `已处理 ${current}/${total}，成功${successCount}条，失败${fail}条...`
        }
        importProgressStatus.value = 'active'
        importProgressTimer = setTimeout(pollImportProgress, 1000)
      }
    } else {
      importProgressStatus.value = 'exception'
      importProgressMessage.value = res.message || '进度查询失败'
      importing.value = false
      importDone.value = true
      importTaskId.value = ''
      clearTimeout(importProgressTimer)
      ElMessage.error(importProgressMessage.value)
    }
  } catch (error: any) {
    importProgressStatus.value = 'exception'
    importProgressMessage.value = error.message || '进度查询失败'
    importing.value = false
    importDone.value = true
    importTaskId.value = ''
    clearTimeout(importProgressTimer)
    ElMessage.error(importProgressMessage.value)
  }
}

const handleImportError = (err: any) => {
  importing.value = false
  importDone.value = true
  importTaskId.value = ''
  importProgressStatus.value = 'exception'
  importProgressMessage.value = '导入失败'
  ElMessage.error('导入失败')
}

const handleImportDialogClose = () => {
  importing.value = false
  importDone.value = false
  importTaskId.value = ''
  importProgressPercent.value = 0
  importProgressStatus.value = 'active'
  importProgressMessage.value = ''
  importDialogVisible.value = false
  clearTimeout(importProgressTimer)
}

// 关闭弹窗时清理定时器
watch(() => importDialogVisible.value, (val) => {
  if (!val) {
    importing.value = false
    importDone.value = false
    importTaskId.value = ''
    clearTimeout(importProgressTimer)
  }
})

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 准备提交数据
    const submitData = {
      name: form.name,
      category: form.category,
      unit: form.unit,
      specification: form.specification,
      clientCodes: form.clientCodes
    }
    
    // 如果是编辑模式且有公司物料编码，或者新增甲方编码模式，则包含编码
    if ((form.id && form.companyCode) || isAddClientCode.value) {
      submitData.companyCode = form.companyCode
    }
    
    if (form.id) {
      // 更新物料
      await request.put(`/api/materials/${form.id}`, submitData)
      ElMessage.success('更新成功')
    } else if (isAddClientCode.value) {
      // 新增甲方编码
      await request.post('/api/materials/client-code', submitData)
      ElMessage.success('新增甲方编码成功')
    } else {
      // 新增物料
      await request.post('/api/materials', submitData)
      ElMessage.success('新增成功')
    }
    
    dialogVisible.value = false
    loadData()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(error.message || '操作失败')
  }
}

// 对话框关闭
const handleDialogClose = () => {
  formRef.value?.resetFields()
  isAddClientCode.value = false
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      keyword: searchForm.keyword,
      category: searchForm.category
    }
    
    // 添加排序参数
    if (sortState.prop && sortState.order) {
      // 将驼峰命名转换为下划线命名
      const fieldMapping = {
        'companyCode': 'company_code',
        'clientCodes': 'client_code',
        'stockQuantity': 'stock_quantity'
      }
      params.sortBy = fieldMapping[sortState.prop] || sortState.prop
      params.sortOrder = sortState.order === 'ascending' ? 'asc' : 'desc'
    }
    
    const response = await request.get('/api/materials', { params })
    
    // 检查响应结构
    if (response && response.success && response.data) {
      tableData.value = response.data.list || []
      pagination.total = response.data.pagination?.total || 0
    } else {
      console.error('API响应格式错误:', response)
      ElMessage.error('数据格式错误')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error(error.message || '加载数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.text-danger {
  color: #f56c6c;
  font-weight: bold;
}
</style> 