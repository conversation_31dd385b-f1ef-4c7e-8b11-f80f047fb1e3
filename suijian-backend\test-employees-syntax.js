const fs = require('fs');
const path = require('path');

console.log('检查员工控制器语法...');

try {
    // 尝试解析员工控制器文件
    const filePath = path.join(__dirname, 'src/controllers/employeesController.js');

    if (fs.existsSync(filePath)) {
        // 读取文件内容
        const content = fs.readFileSync(filePath, 'utf8');

        // 检查是否还有语法错误
        if (content.includes('? .total')) {
            console.log('❌ 发现语法错误: 可选链操作符格式错误');
        } else if (content.includes('!dailyWage')) {
            console.log('❌ 发现验证逻辑错误: 日工资仍然是必填项');
        } else {
            console.log('✅ 员工控制器语法检查通过');
        }

        // 尝试解析文件
        require(filePath);
        console.log('✅ 员工控制器文件可以正常解析');
    } else {
        console.log('❌ 员工控制器文件不存在');
    }
} catch (error) {
    console.log('❌ 员工控制器语法错误:', error.message);
}