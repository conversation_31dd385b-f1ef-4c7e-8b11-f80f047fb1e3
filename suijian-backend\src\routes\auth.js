const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { authMiddleware } = require('../middleware/auth');
const Validator = require('../utils/validator');

// 用户登录
router.post('/login', [
    Validator.loginValidationRules(),
    Validator.handleValidationResult
], authController.login);

// 用户注册
router.post('/register', [
    Validator.userValidationRules(),
    Validator.handleValidationResult
], authController.register);

// 用户登出
router.post('/logout', authMiddleware, authController.logout);

// 获取用户信息
router.get('/profile', authMiddleware, authController.getProfile);

// 修改用户信息
router.put('/profile', [
    authMiddleware,
    Validator.userValidationRules(),
    Validator.handleValidationResult
], authController.updateProfile);

// 修改密码
router.put('/change-password', [
    authMiddleware,
    Validator.handleValidationResult
], authController.changePassword);

module.exports = router;