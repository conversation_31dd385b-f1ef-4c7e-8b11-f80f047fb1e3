<template>
  <div class="system-log">
    <!-- 面包屑导航 -->
    <el-breadcrumb class="breadcrumb" separator=">">
      <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/system/user-management' }">系统设置</el-breadcrumb-item>
      <el-breadcrumb-item>系统日志</el-breadcrumb-item>
    </el-breadcrumb>

    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>📋 系统日志</span>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="danger" size="small" @click="clearLogs">
              <el-icon><Delete /></el-icon>
              清空日志
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索条件 -->
      <div class="search-section">
        <el-form :model="searchForm" inline>
          <el-form-item label="日志级别:">
            <el-select v-model="searchForm.logLevel" placeholder="请选择日志级别">
              <el-option label="全部" value="" />
              <el-option label="信息" value="info" />
              <el-option label="警告" value="warning" />
              <el-option label="错误" value="error" />
              <el-option label="调试" value="debug" />
            </el-select>
          </el-form-item>
          <el-form-item label="操作模块:">
            <el-select v-model="searchForm.module" placeholder="请选择操作模块">
              <el-option label="全部" value="" />
              <el-option label="用户管理" value="user" />
              <el-option label="仓库管理" value="warehouse" />
              <el-option label="工程订单" value="project" />
              <el-option label="散户订单" value="loose" />
              <el-option label="系统设置" value="system" />
            </el-select>
          </el-form-item>
          <el-form-item label="操作员:">
            <el-input v-model="searchForm.operator" placeholder="请输入操作员" />
          </el-form-item>
          <el-form-item label="日期范围:">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="logId" label="日志ID" width="100" />
        <el-table-column prop="logTime" label="操作时间" width="180" />
        <el-table-column prop="logLevel" label="日志级别" width="100">
          <template #default="{ row }">
            <el-tag :type="getLogLevelTag(row.logLevel)">
              {{ getLogLevelText(row.logLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="module" label="操作模块" width="120">
          <template #default="{ row }">
            {{ getModuleText(row.module) }}
          </template>
        </el-table-column>
        <el-table-column prop="operator" label="操作员" width="100" />
        <el-table-column prop="operation" label="操作内容" min-width="200" />
        <el-table-column prop="ipAddress" label="IP地址" width="120" />
        <el-table-column prop="userAgent" label="用户代理" min-width="150" show-overflow-tooltip />
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetails(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Delete } from '@element-plus/icons-vue'

// 搜索表单
const searchForm = reactive({
  logLevel: '',
  module: '',
  operator: '',
  dateRange: []
})

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 表格数据
const tableData = ref([
  {
    logId: 'L001',
    logTime: '2024-01-15 14:30:25',
    logLevel: 'info',
    module: 'user',
    operator: '张三',
    operation: '用户登录',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  },
  {
    logId: 'L002',
    logTime: '2024-01-15 14:25:10',
    logLevel: 'warning',
    module: 'warehouse',
    operator: '李四',
    operation: '物料库存不足预警',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  },
  {
    logId: 'L003',
    logTime: '2024-01-15 14:20:05',
    logLevel: 'error',
    module: 'project',
    operator: '王五',
    operation: '工程数据保存失败',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  }
])

// 获取日志级别标签
const getLogLevelTag = (level: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  switch (level) {
    case 'info':
      return 'primary'
    case 'warning':
      return 'warning'
    case 'error':
      return 'danger'
    case 'debug':
      return 'info'
    default:
      return 'info'
  }
}

// 获取日志级别文本
const getLogLevelText = (level: string): string => {
  switch (level) {
    case 'info':
      return '信息'
    case 'warning':
      return '警告'
    case 'error':
      return '错误'
    case 'debug':
      return '调试'
    default:
      return '未知'
  }
}

// 获取模块文本
const getModuleText = (module: string): string => {
  switch (module) {
    case 'user':
      return '用户管理'
    case 'warehouse':
      return '仓库管理'
    case 'project':
      return '工程订单'
    case 'loose':
      return '散户订单'
    case 'system':
      return '系统设置'
    default:
      return '未知模块'
  }
}

// 搜索
const handleSearch = () => {
  console.log('搜索条件:', searchForm)
  ElMessage.success('搜索完成')
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    (searchForm as any)[key] = key === 'dateRange' ? [] : ''
  })
  ElMessage.info('搜索条件已重置')
}

// 刷新数据
const refreshData = () => {
  ElMessage.success('数据已刷新')
}

// 清空日志
const clearLogs = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有日志吗？此操作不可恢复。', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    ElMessage.success('日志已清空')
  } catch {
    // 用户取消
  }
}

// 查看详情
const viewDetails = (row: any) => {
  ElMessage.info(`查看日志 ${row.logId} 的详情`)
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
}

// 初始化
onMounted(() => {
  pagination.total = tableData.value.length
})
</script>

<style scoped lang="scss">
.system-log {
  padding: 20px;
  
  .breadcrumb {
    margin-bottom: 20px;
  }
  
  .main-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
      color: #303133;
      
      .header-actions {
        display: flex;
        gap: 10px;
      }
    }
  }
  
  .search-section {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .pagination-section {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
