const { logger } = require('../utils/logger');
const { query, queryOne, execute } = require('../config/database');
const { generatePrefixedId } = require('../utils/uuid');
const ExcelUtil = require('../utils/excel');
const ExcelJS = require('exceljs');
const dayjs = require('dayjs');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
// 全局内存存储导入任务进度
const importTasks = {};

/**
 * 获取物料列表
 */
const getMaterials = async(req, res) => {
    try {
        const { page = 1, pageSize = 10, category, keyword, sortBy, sortOrder } = req.query;

        // 构建查询条件
        let whereClause = 'WHERE status = 1';
        let params = [];

        if (category) {
            whereClause += ' AND category = ?';
            params.push(category);
        }

        if (keyword) {
            whereClause += ' AND (name LIKE ? OR company_code LIKE ?)';
            params.push(`%${keyword}%`, `%${keyword}%`);
        }

        // 获取总数
        const countSql = `SELECT COUNT(*) as total FROM materials ${whereClause}`;
        const countResult = await queryOne(countSql, params);
        const total = countResult.total;

        // 构建排序条件
        let orderClause = 'ORDER BY id DESC';
        if (sortBy && sortOrder) {
            logger.info(`排序参数: sortBy=${sortBy}, sortOrder=${sortOrder}`);
            // 验证排序字段是否合法
            const allowedSortFields = ['company_code', 'client_code', 'name', 'specification', 'category', 'unit', 'stock_quantity', 'status'];
            if (allowedSortFields.includes(sortBy)) {
                const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

                // 特殊处理物料编码排序
                if (sortBy === 'company_code') {
                    // 使用正则表达式提取数字部分进行排序
                    orderClause = `ORDER BY CAST(SUBSTRING(${sortBy}, 3) AS UNSIGNED) ${order}`;
                } else {
                    orderClause = `ORDER BY ${sortBy} ${order}`;
                }
                logger.info(`应用排序: ${orderClause}`);
            } else {
                logger.warn(`无效的排序字段: ${sortBy}`);
            }
        }

        // 分页查询
        const offset = (page - 1) * pageSize;
        const listSql = `
            SELECT 
                id, company_code as companyCode, client_code as clientCodes, name, model, specification, category, unit, 
                price, stock_quantity as stockQuantity, warning_quantity as warningQuantity,
                status, created_at as createTime, updated_at as updateTime
            FROM materials 
            ${whereClause}
            ${orderClause}
            LIMIT ? OFFSET ?
        `;

        const listParams = [...params, parseInt(pageSize), offset];
        const materials = await query(listSql, listParams);

        // 计算统计信息
        const warningSql = `
            SELECT COUNT(*) as count 
            FROM materials 
            ${whereClause} AND stock_quantity <= warning_quantity
        `;
        const warningResult = await queryOne(warningSql, params);
        const warningCount = warningResult.count;

        const outOfStockSql = `
            SELECT COUNT(*) as count 
            FROM materials 
            ${whereClause} AND stock_quantity = 0
        `;
        const outOfStockResult = await queryOne(outOfStockSql, params);
        const outOfStockCount = outOfStockResult.count;

        res.json({
            success: true,
            message: '获取物料列表成功',
            data: {
                list: materials,
                pagination: {
                    current: parseInt(page),
                    pageSize: parseInt(pageSize),
                    total,
                    totalPages: Math.ceil(total / pageSize)
                },
                statistics: {
                    total,
                    warningCount,
                    outOfStockCount
                }
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error('获取物料列表失败', error);
        res.status(500).json({
            success: false,
            message: '获取物料列表失败',
            timestamp: new Date().toISOString()
        });
    }
};

/**
 * 获取物料详情
 */
const getMaterialById = async(req, res) => {
    try {
        const { id } = req.params;

        const sql = `
            SELECT 
                id, company_code as companyCode, client_code as clientCodes, name, model, specification, category, unit, 
                price, stock_quantity as stockQuantity, warning_quantity as warningQuantity,
                status, created_at as createTime, updated_at as updateTime
            FROM materials 
            WHERE id = ? AND status = 1
        `;

        const material = await queryOne(sql, [id]);
        if (!material) {
            return res.status(404).json({
                success: false,
                message: '物料不存在',
                timestamp: new Date().toISOString()
            });
        }

        res.json({
            success: true,
            message: '获取物料详情成功',
            data: material,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error('获取物料详情失败', error);
        res.status(500).json({
            success: false,
            message: '获取物料详情失败',
            timestamp: new Date().toISOString()
        });
    }
};

/**
 * 创建物料
 */
const createMaterial = async(req, res) => {
    try {
        const { companyCode, name, category, unit, price, stockQuantity, warningQuantity, clientCodes, model, specification } = req.body;

        // 如果没有提供公司物料编码，自动生成
        let finalCompanyCode = companyCode;
        if (!companyCode || companyCode.trim() === '') {
            // 生成物料编码：WL + 年月日 + 4位序号
            const date = new Date();
            const dateStr = date.getFullYear().toString() +
                (date.getMonth() + 1).toString().padStart(2, '0') +
                date.getDate().toString().padStart(2, '0');

            // 获取当天的最大序号
            const maxCodeSql = `
                SELECT company_code 
                FROM materials 
                WHERE company_code LIKE ? 
                ORDER BY company_code DESC 
                LIMIT 1
            `;
            const maxCode = await queryOne(maxCodeSql, [`WL${dateStr}%`]);

            let sequence = 1;
            if (maxCode) {
                const lastSequence = parseInt(maxCode.company_code.slice(-4));
                sequence = lastSequence + 1;
            }

            finalCompanyCode = `WL${dateStr}${sequence.toString().padStart(4, '0')}`;
        }

        // 检查编码是否已存在（只在创建新物料时检查）
        if (companyCode && companyCode.trim() !== '') {
            const existingSql = 'SELECT id FROM materials WHERE company_code = ? AND status = 1';
            const existing = await queryOne(existingSql, [finalCompanyCode]);
            if (existing) {
                return res.status(400).json({
                    success: false,
                    message: '物料编码已存在',
                    timestamp: new Date().toISOString()
                });
            }
        }

        // 生成物料ID
        const materialId = generatePrefixedId('material');

        // 创建新物料
        const insertSql = `
            INSERT INTO materials (
                id, company_code, client_code, name, model, specification, category, unit, price, 
                stock_quantity, warning_quantity, status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        `;

        const result = await execute(insertSql, [
            materialId,
            finalCompanyCode,
            clientCodes || '',
            name,
            model || '',
            specification || '',
            category,
            unit,
            parseFloat(price) || 0,
            parseInt(stockQuantity) || 0,
            parseInt(warningQuantity) || 0,
            1
        ]);

        // 获取新创建的物料
        const newMaterialSql = `
            SELECT 
                id, company_code as companyCode, client_code as clientCodes, name, model, specification, category, unit, 
                price, stock_quantity as stockQuantity, warning_quantity as warningQuantity,
                status, created_at as createTime, updated_at as updateTime
            FROM materials 
            WHERE id = ?
        `;

        const newMaterial = await queryOne(newMaterialSql, [materialId]);

        logger.info('创建物料', {
            materialId: materialId,
            companyCode: finalCompanyCode,
            name
        });

        res.json({
            success: true,
            message: '创建物料成功',
            data: newMaterial,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error('创建物料失败', error);
        res.status(500).json({
            success: false,
            message: '创建物料失败',
            timestamp: new Date().toISOString()
        });
    }
};

/**
 * 更新物料
 */
const updateMaterial = async(req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;

        // 检查物料是否存在
        const existingSql = 'SELECT id FROM materials WHERE id = ? AND status = 1';
        const existing = await queryOne(existingSql, [id]);
        if (!existing) {
            return res.status(404).json({
                success: false,
                message: '物料不存在',
                timestamp: new Date().toISOString()
            });
        }

        // 检查编码是否重复（排除自己）
        if (updateData.companyCode) {
            const duplicateSql = 'SELECT id FROM materials WHERE company_code = ? AND id != ? AND status = 1';
            const duplicate = await queryOne(duplicateSql, [updateData.companyCode, id]);
            if (duplicate) {
                return res.status(400).json({
                    success: false,
                    message: '物料编码已存在',
                    timestamp: new Date().toISOString()
                });
            }
        }

        // 构建更新SQL
        const updateFields = [];
        const updateParams = [];

        if (updateData.companyCode !== undefined) {
            updateFields.push('company_code = ?');
            updateParams.push(updateData.companyCode);
        }
        if (updateData.name !== undefined) {
            updateFields.push('name = ?');
            updateParams.push(updateData.name);
        }
        if (updateData.category !== undefined) {
            updateFields.push('category = ?');
            updateParams.push(updateData.category);
        }
        if (updateData.unit !== undefined) {
            updateFields.push('unit = ?');
            updateParams.push(updateData.unit);
        }
        if (updateData.price !== undefined) {
            updateFields.push('price = ?');
            updateParams.push(parseFloat(updateData.price) || 0);
        }
        if (updateData.stockQuantity !== undefined) {
            updateFields.push('stock_quantity = ?');
            updateParams.push(parseInt(updateData.stockQuantity) || 0);
        }
        if (updateData.warningQuantity !== undefined) {
            updateFields.push('warning_quantity = ?');
            updateParams.push(parseInt(updateData.warningQuantity) || 0);
        }
        if (updateData.clientCodes !== undefined) {
            updateFields.push('client_code = ?');
            updateParams.push(updateData.clientCodes);
        }
        if (updateData.model !== undefined) {
            updateFields.push('model = ?');
            updateParams.push(updateData.model);
        }
        if (updateData.specification !== undefined) {
            updateFields.push('specification = ?');
            updateParams.push(updateData.specification);
        }

        updateFields.push('updated_at = datetime(\'now\')');
        updateParams.push(id);

        const updateSql = `UPDATE materials SET ${updateFields.join(', ')} WHERE id = ?`;
        await execute(updateSql, updateParams);

        // 获取更新后的物料
        const updatedSql = `
            SELECT 
                id, company_code as companyCode, client_code as clientCodes, name, model, specification, category, unit, 
                price, stock_quantity as stockQuantity, warning_quantity as warningQuantity,
                status, created_at as createTime, updated_at as updateTime
            FROM materials 
            WHERE id = ?
        `;

        const updatedMaterial = await queryOne(updatedSql, [id]);

        logger.info('更新物料', {
            materialId: id,
            updateData
        });

        res.json({
            success: true,
            message: '更新物料成功',
            data: updatedMaterial,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error('更新物料失败', error);
        res.status(500).json({
            success: false,
            message: '更新物料失败',
            timestamp: new Date().toISOString()
        });
    }
};

/**
 * 删除物料
 */
const deleteMaterial = async(req, res) => {
    try {
        const { id } = req.params;

        // 检查物料是否存在
        const existingSql = 'SELECT id FROM materials WHERE id = ? AND status = 1';
        const existing = await queryOne(existingSql, [id]);
        if (!existing) {
            return res.status(404).json({
                success: false,
                message: '物料不存在',
                timestamp: new Date().toISOString()
            });
        }

        // 软删除（设置状态为0）
        const deleteSql = 'UPDATE materials SET status = 0, updated_at = datetime(\'now\') WHERE id = ?';
        await execute(deleteSql, [id]);

        logger.info('删除物料', {
            materialId: id
        });

        res.json({
            success: true,
            message: '删除物料成功',
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error('删除物料失败', error);
        res.status(500).json({
            success: false,
            message: '删除物料失败',
            timestamp: new Date().toISOString()
        });
    }
};

/**
 * 批量删除物料
 */
const batchDeleteMaterials = async(req, res) => {
    try {
        const { ids } = req.body;

        if (!Array.isArray(ids) || ids.length === 0) {
            return res.status(400).json({
                success: false,
                message: '请选择要删除的物料',
                timestamp: new Date().toISOString()
            });
        }

        // 批量软删除
        const placeholders = ids.map(() => '?').join(',');
        const deleteSql = `UPDATE materials SET status = 0, updated_at = datetime('now') WHERE id IN (${placeholders})`;
        const result = await execute(deleteSql, ids);

        logger.info('批量删除物料', {
            ids,
            deletedCount: result.changes
        });

        res.json({
            success: true,
            message: `成功删除${result.changes}个物料`,
            data: { deletedCount: result.changes },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error('批量删除物料失败', error);
        res.status(500).json({
            success: false,
            message: '批量删除物料失败',
            timestamp: new Date().toISOString()
        });
    }
};

/**
 * 新增甲方编码
 */
const createClientCode = async(req, res) => {
    try {
        const { companyCode, clientCodes, name, category, unit, model, specification } = req.body;

        // 验证必填字段
        if (!companyCode || !clientCodes || !name || !category || !unit) {
            return res.status(400).json({
                success: false,
                message: '请填写完整的物料信息',
                timestamp: new Date().toISOString()
            });
        }

        // 检查甲方编码是否已存在（同一公司物料编码下）
        const existingClientCodeSql = `
            SELECT id FROM materials 
            WHERE company_code = ? AND client_code = ? AND status = 1
        `;
        const existingClientCode = await queryOne(existingClientCodeSql, [companyCode, clientCodes]);
        if (existingClientCode) {
            return res.status(400).json({
                success: false,
                message: '该甲方编码已存在',
                timestamp: new Date().toISOString()
            });
        }

        // 生成物料ID
        const materialId = generatePrefixedId('material');

        // 创建新的甲方编码记录
        const insertSql = `
            INSERT INTO materials (
                id, company_code, client_code, name, model, specification, category, unit, price, 
                stock_quantity, warning_quantity, status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        `;

        const result = await execute(insertSql, [
            materialId,
            companyCode,
            clientCodes,
            name,
            model || '',
            specification || '',
            category,
            unit,
            0, // 默认价格
            0, // 默认库存
            0, // 默认预警数量
            1
        ]);

        // 获取新创建的物料
        const newMaterialSql = `
            SELECT 
                id, company_code as companyCode, client_code as clientCodes, name, model, specification, category, unit, 
                price, stock_quantity as stockQuantity, warning_quantity as warningQuantity,
                status, created_at as createTime, updated_at as updateTime
            FROM materials 
            WHERE id = ?
        `;

        const newMaterial = await queryOne(newMaterialSql, [materialId]);

        logger.info('新增甲方编码', {
            materialId: materialId,
            companyCode: companyCode,
            clientCodes: clientCodes,
            name
        });

        res.json({
            success: true,
            message: '新增甲方编码成功',
            data: newMaterial,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error('新增甲方编码失败', error);
        res.status(500).json({
            success: false,
            message: '新增甲方编码失败',
            timestamp: new Date().toISOString()
        });
    }
};

/**
 * 导出物料Excel（直接输出文件流，不保存本地）
 */
const exportMaterialsExcel = async(req, res) => {
    try {
        const { category, keyword, status } = req.query;
        let whereClause = 'WHERE status = 1';
        let params = [];
        if (category) {
            whereClause += ' AND category = ?';
            params.push(category);
        }
        if (keyword) {
            whereClause += ' AND (name LIKE ? OR company_code LIKE ?)';
            params.push(`%${keyword}%`, `%${keyword}%`);
        }
        if (status) {
            if (status === '正常') {
                whereClause += ' AND stock_quantity > warning_quantity';
            } else if (status === '需进仓') {
                whereClause += ' AND stock_quantity <= warning_quantity';
            }
        }
        const sql = `SELECT id, company_code as companyCode, client_code as partyCode, name, model, specification, category, unit, price, stock_quantity as stockQuantity, warning_quantity as warningQuantity, status FROM materials ${whereClause} ORDER BY id DESC`;
        const materials = await query(sql, params);
        if (!materials || materials.length === 0) {
            return res.status(404).json({ success: false, message: '无符合条件的物料可导出' });
        }
        // 构建Excel
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('物料列表');
        const headers = [
            '公司物料编码', '甲方编码', '物料名称', '型号', '规格', '单位', '物料分类', '库存数量', '预警数量', '状态'
        ];
        worksheet.addRow(headers);
        materials.forEach(material => {
            worksheet.addRow([
                material.companyCode,
                material.partyCode,
                material.name,
                material.model,
                material.specification,
                material.unit,
                material.category,
                material.stockQuantity,
                material.warningQuantity,
                material.status === 1 ? '启用' : '禁用'
            ]);
        });
        const now = dayjs().format('YYYYMMDD');
        const fileName = `${now}-库存.xlsx`;
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fileName)}"`);
        await workbook.xlsx.write(res);
        res.end();
    } catch (error) {
        logger.error('导出物料Excel失败', error);
        res.status(500).json({ success: false, message: '导出Excel失败' });
    }
};

/**
 * 导入物料Excel（异步任务，支持进度查询）
 */
const importMaterialsExcel = async(req, res) => {
    try {
        if (!req.files || !req.files.file) {
            return res.status(400).json({ success: false, message: '未上传文件' });
        }
        const file = req.files.file;
        const uploadPath = path.join(__dirname, '../uploads/excel', `${Date.now()}_${file.name}`);
        await file.mv(uploadPath);
        const taskId = uuidv4();
        importTasks[taskId] = {
            status: 'processing',
            total: 0,
            current: 0,
            successCount: 0, // 改名
            fail: 0,
            message: '',
            startTime: Date.now(),
            endTime: null
        };
        res.json({ success: true, taskId });
        (async() => {
            try {
                console.log(`[导入任务] ${taskId} 开始处理文件:`, uploadPath);
                const materials = await ExcelUtil.importMaterials(uploadPath);
                console.log(`[导入任务] ${taskId} 解析到数据条数:`, materials.length);
                importTasks[taskId].total = materials.length;
                let successCount = 0;
                let failCount = 0;
                const toInsertList = [];
                const companyCodeMap = {};
                // 获取当天数据库最大序号
                const date = new Date();
                const dateStr = date.getFullYear().toString() +
                    (date.getMonth() + 1).toString().padStart(2, '0') +
                    date.getDate().toString().padStart(2, '0');
                const maxCodeSql = `SELECT company_code FROM materials WHERE company_code LIKE ? ORDER BY company_code DESC LIMIT 1`;
                const maxCode = await queryOne(maxCodeSql, [`WL${dateStr}%`]);
                let dbSequence = 1;
                if (maxCode && maxCode.company_code) {
                    const lastSequence = parseInt(maxCode.company_code.slice(-4));
                    dbSequence = lastSequence + 1;
                }
                let localSequence = 0;
                for (let i = 0; i < materials.length; i++) {
                    const m = materials[i];
                    importTasks[taskId].current = i + 1;
                    try {
                        // 打印当前导入数据
                        console.log(`[导入任务] 检查第${i + 1}条:`, m);
                        // 查重SQL和参数（只用 name、specification、unit、category、client_code，全部用空字符串比对）
                        function safeStr(val) {
                            return (val === undefined || val === null) ? '' : String(val);
                        }
                        const duplicateSql = `SELECT id FROM materials WHERE IFNULL(name, '') = ? AND IFNULL(specification, '') = ? AND IFNULL(unit, '') = ? AND IFNULL(category, '') = ? AND IFNULL(client_code, '') = ?`;
                        const duplicateParams = [
                            safeStr(m.name),
                            safeStr(m.specification),
                            safeStr(m.unit),
                            safeStr(m.category),
                            safeStr(m.partyCode)
                        ];
                        const duplicate = await queryOne(duplicateSql, duplicateParams);
                        console.log(`[导入任务] 查重SQL:`, duplicateSql);
                        console.log(`[导入任务] 查重参数:`, duplicateParams);
                        if (duplicate) {
                            console.log(`[导入任务] 第${i + 1}条数据关键字段重复，跳过`);
                            failCount++;
                            continue;
                        }
                        // 生成companyCode和materialId
                        const key = `${safeStr(m.name)}|${safeStr(m.specification)}|${safeStr(m.unit)}|${safeStr(m.category)}`;
                        let companyCode = companyCodeMap[key];
                        if (!companyCode) {
                            // 先查数据库
                            const existCompany = await queryOne(
                                'SELECT company_code FROM materials WHERE name=? AND specification=? AND unit=? AND category=?', [m.name, m.specification, m.unit, m.category]
                            );
                            if (existCompany && existCompany.company_code) {
                                companyCode = existCompany.company_code;
                            } else {
                                companyCode = `WL${dateStr}${(dbSequence + localSequence).toString().padStart(4, '0')}`;
                                localSequence++;
                            }
                            companyCodeMap[key] = companyCode;
                        }
                        const materialId = generatePrefixedId('material');
                        toInsertList.push([
                            materialId, companyCode, m.partyCode, m.name, m.model, m.specification, m.unit, m.category, m.price, m.stockQuantity, m.warningQuantity, m.status
                        ]);
                        successCount++;
                    } catch (e) {
                        failCount++;
                        console.error('导入失败:', m, e);
                    }
                    importTasks[taskId].successCount = successCount;
                    importTasks[taskId].fail = failCount;
                    if ((i + 1) % 10 === 0 || i === materials.length - 1) {
                        console.log(`[导入任务] ${taskId} 进度: ${i + 1}/${materials.length}，成功${successCount}，失败${failCount}`);
                    }
                }
                // 批量插入
                if (toInsertList.length > 0) {
                    const placeholders = toInsertList.map(() => '(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)').join(',');
                    const flatValues = toInsertList.flat();
                    const sql = `INSERT INTO materials (id, company_code, client_code, name, model, specification, unit, category, price, stock_quantity, warning_quantity, status) VALUES ${placeholders}`;
                    await execute(sql, flatValues);
                }
                fs.unlinkSync(uploadPath);
                importTasks[taskId].status = 'done';
                importTasks[taskId].message = `导入完成，成功${successCount}条，失败${failCount}条`;
                importTasks[taskId].endTime = Date.now();
                setTimeout(() => { delete importTasks[taskId]; }, 20000); // 20秒后清理
            } catch (error) {
                importTasks[taskId].status = 'error';
                importTasks[taskId].message = '导入Excel失败';
                importTasks[taskId].endTime = Date.now();
                logger.error('导入物料Excel失败', error);
                setTimeout(() => { delete importTasks[taskId]; }, 20000); // 20秒后清理
            }
        })();
    } catch (error) {
        logger.error('导入物料Excel失败', error);
        res.status(500).json({ success: false, message: '导入Excel失败' });
    }
};

/**
 * 批量入库
 * POST /materials/inbound
 * body: { items: [{ id, quantity }], remark }
 */
const batchInbound = async(req, res) => {
    try {
        const { items, documentNumber, remark } = req.body;

        if (!documentNumber || !documentNumber.trim()) {
            return res.status(400).json({ success: false, message: '单号不能为空' });
        }
        if (!Array.isArray(items) || items.length === 0) {
            return res.status(400).json({ success: false, message: '入库物料不能为空' });
        }
        // 校验所有物料ID
        const ids = items.map(i => i.id);
        const placeholders = ids.map(() => '?').join(',');
        const sql = `SELECT id FROM materials WHERE id IN (${placeholders}) AND status = 1`;
        const rows = await query(sql, ids);
        const validIds = rows.map(r => r.id);
        const invalidIds = ids.filter(id => !validIds.includes(id));
        if (invalidIds.length > 0) {
            return res.status(400).json({ success: false, message: '部分物料ID无效', invalidIds });
        }
        // 批量更新库存和写入日志
        console.log('【批量入库】items:', items)
        console.log('【批量入库】operator_id:', req.user.id)

        // 开始事务
        await execute('BEGIN TRANSACTION');

        try {
            // 创建入库记录主表
            const recordId = generatePrefixedId('mr');
            await execute(
                `INSERT INTO material_records (id, type, operator_id, order_id, remarks, created_at) VALUES (?, 'in', ?, ?, ?, datetime('now'))`, [recordId, req.user.id, documentNumber, remark || '']
            );

            // 批量更新库存和写入明细
            for (const item of items) {
                // 获取当前库存数量
                const currentStockSql = 'SELECT stock_quantity FROM materials WHERE id = ?';
                const currentStockResult = await query(currentStockSql, [item.id]);
                const currentQuantity = currentStockResult[0] && currentStockResult[0].stock_quantity ? currentStockResult[0].stock_quantity : 0;

                // 更新库存
                const newQuantity = currentQuantity + (parseInt(item.quantity) || 0);
                await execute('UPDATE materials SET stock_quantity = ? WHERE id = ?', [newQuantity, item.id]);

                // 写入入库明细
                await execute(
                    `INSERT INTO material_record_items (id, record_id, material_id, quantity, current_quantity, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))`, [generatePrefixedId('mri'), recordId, item.id, parseInt(item.quantity) || 0, currentQuantity]
                );
            }

            // 提交事务
            await execute('COMMIT');
        } catch (error) {
            // 回滚事务
            await execute('ROLLBACK');
            throw error;
        }
        res.json({ success: true, message: '批量入库成功' });
    } catch (error) {
        logger.error('批量入库失败', error);
        res.status(500).json({ success: false, message: '批量入库失败' });
    }
};

/**
 * 物料退仓
 * POST /materials/return
 * body: { 
 *   returnDate: '退仓日期',
 *   returnerId: '退仓人ID', 
 *   returnType: '退仓类型',
 *   relatedProject: '关联工程',
 *   remarks: '备注',
 *   items: [{ id: '物料ID', returnQuantity: '退仓数量' }] 
 * }
 */
const materialReturn = async(req, res) => {
        try {
            const {
                returnDate,
                returnerId,
                returnType,
                relatedProject,
                remarks,
                items
            } = req.body;

            // 验证必填字段
            if (!returnDate || !returnerId || !returnType) {
                return res.status(400).json({
                    success: false,
                    message: '请填写完整的退仓信息'
                });
            }

            if (!Array.isArray(items) || items.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: '退仓物料不能为空'
                });
            }

            // 校验所有物料ID
            const ids = items.map(i => i.id);
            const placeholders = ids.map(() => '?').join(',');
            const sql = `SELECT id, name FROM materials WHERE id IN (${placeholders}) AND status = 1`;
            const rows = await query(sql, ids);
            const validIds = rows.map(r => r.id);
            const invalidIds = ids.filter(id => !validIds.includes(id));

            if (invalidIds.length > 0) {
                return res.status(400).json({
                    success: false,
                    message: '部分物料ID无效',
                    invalidIds
                });
            }

            console.log('【物料退仓】items:', items);
            console.log('【物料退仓】operator_id:', req.user.id);

            // 开始事务
            await execute('BEGIN TRANSACTION');

            try {
                // 创建退仓记录主表
                const recordId = generatePrefixedId('mr');
                const purpose = `退仓类型:${returnType}${relatedProject ? `,关联工程:${relatedProject}` : ''}`;
            
            await execute(
                `INSERT INTO material_records (id, type, operator_id, order_id, recipient, purpose, remarks, created_at) VALUES (?, 'return', ?, ?, ?, ?, ?, datetime('now'))`,
                [recordId, req.user.id, `TC${Date.now()}`, returnerId, purpose, remarks || '']
            );

            // 批量更新库存和写入明细
            for (const item of items) {
                // 获取当前库存数量
                const currentStockSql = 'SELECT stock_quantity FROM materials WHERE id = ?';
                const currentStockResult = await query(currentStockSql, [item.id]);
                const currentQuantity = currentStockResult[0] && currentStockResult[0].stock_quantity ? currentStockResult[0].stock_quantity : 0;

                // 更新库存（退仓增加库存）
                const newQuantity = currentQuantity + (parseInt(item.returnQuantity) || 0);
                await execute('UPDATE materials SET stock_quantity = ? WHERE id = ?', [newQuantity, item.id]);

                // 写入退仓明细
                await execute(
                    `INSERT INTO material_record_items (id, record_id, material_id, quantity, current_quantity, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))`,
                    [generatePrefixedId('mri'), recordId, item.id, parseInt(item.returnQuantity) || 0, currentQuantity]
                );
            }

            // 提交事务
            await execute('COMMIT');
            
            res.json({ 
                success: true, 
                message: '物料退仓成功',
                data: { recordId }
            });
        } catch (error) {
            // 回滚事务
            await execute('ROLLBACK');
            throw error;
        }
    } catch (error) {
        logger.error('物料退仓失败', error);
        res.status(500).json({ 
            success: false, 
            message: '物料退仓失败: ' + error.message 
        });
    }
};

// 进度查询接口
const getImportProgress = (req, res) => {
    const { taskId } = req.query;
    if (!taskId || !importTasks[taskId]) {
        return res.status(404).json({ success: false, message: '任务不存在' });
    }
    const resp = { success: true, ...importTasks[taskId] };
    res.json(resp);
};

/**
 * 获取物料进出记录
 * GET /api/materials/records
 */
const getMaterialRecords = async(req, res) => {
    try {
        const {
            page = 1,
            pageSize = 20,
            type = '',
            materialCode = '',
            materialName = '',
            orderId = '',
            startDate = '',
            endDate = '',
            sortBy = '',
            sortOrder = ''
        } = req.query;

        let where = 'WHERE 1=1';
        const params = [];

        // 按记录类型筛选
        if (type) {
            where += ' AND mr.type = ?';
            params.push(type);
        }

        // 按单号筛选
        if (orderId) {
            where += ' AND mr.order_id LIKE ?';
            params.push(`%${orderId}%`);
        }

        // 按日期范围筛选
        if (startDate) {
            where += ' AND mr.created_at >= ?';
            params.push(startDate);
        }

        if (endDate) {
            where += ' AND mr.created_at <= ?';
            params.push(endDate);
        }

        // 构建排序条件
        let orderClause = 'ORDER BY mr.created_at DESC';
        if (sortBy && sortOrder) {
            // 验证排序字段是否合法
            const allowedSortFields = ['created_at', 'order_id', 'type', 'operator_name', 'recipient_name', 'purpose'];
            if (allowedSortFields.includes(sortBy)) {
                const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
                if (sortBy === 'operator_name') {
                    orderClause = `ORDER BY u.real_name ${order}`;
                } else if (sortBy === 'recipient_name') {
                    orderClause = `ORDER BY e.name ${order}`;
                } else {
                    orderClause = `ORDER BY mr.${sortBy} ${order}`;
                }
            }
        }

        const offset = (parseInt(page) - 1) * parseInt(pageSize);

        // 获取总数
        const countSql = `
            SELECT COUNT(DISTINCT mr.id) as total 
            FROM material_records mr
            ${where}
        `;
        const countResult = await queryOne(countSql, params);
        const total = countResult.total || 0;

        // 获取记录列表
        const listSql = `
            SELECT 
                mr.id,
                mr.type,
                mr.operator_id,
                mr.order_id,
                mr.recipient,
                mr.purpose,
                mr.remarks,
                mr.created_at,
                u.real_name as operator_name,
                e.name as recipient_name
            FROM material_records mr
            LEFT JOIN users u ON mr.operator_id = u.id
            LEFT JOIN employees e ON mr.recipient = e.id
            ${where}
            ${orderClause}
            LIMIT ? OFFSET ?
        `;

        const listParams = [...params, parseInt(pageSize), offset];
        const records = await query(listSql, listParams);

        // 获取每个记录的物料明细
        for (const record of records) {
            let itemsWhere = 'WHERE mri.record_id = ?';
            const itemsParams = [record.id];

            // 按物料编码筛选
            if (materialCode) {
                itemsWhere += ' AND m.company_code LIKE ?';
                itemsParams.push(`%${materialCode}%`);
            }

            // 按物料名称筛选
            if (materialName) {
                itemsWhere += ' AND m.name LIKE ?';
                itemsParams.push(`%${materialName}%`);
            }

            const itemsSql = `
                SELECT 
                    mri.id,
                    mri.material_id,
                    mri.quantity,
                    mri.current_quantity,
                    m.name as material_name,
                    m.company_code as material_code,
                    m.specification,
                    m.unit
                FROM material_record_items mri
                LEFT JOIN materials m ON mri.material_id = m.id
                ${itemsWhere}
            `;
            const items = await query(itemsSql, itemsParams);
            record.materials = items;
        }

        // 如果有物料筛选条件，只返回包含匹配物料的记录
        let filteredRecords = records;
        if (materialCode || materialName) {
            filteredRecords = records.filter(record => record.materials && record.materials.length > 0);
        }

        res.json({
            success: true,
            message: '获取物料记录成功',
            data: {
                list: filteredRecords,
                pagination: {
                    current: parseInt(page),
                    pageSize: parseInt(pageSize),
                    total: filteredRecords.length,
                    totalPages: Math.ceil(filteredRecords.length / parseInt(pageSize))
                }
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error('获取物料记录失败', error);
        res.status(500).json({
            success: false,
            message: '获取物料记录失败',
            timestamp: new Date().toISOString()
        });
    }
};

/**
 * 获取记录明细
 * GET /api/materials/records/:id/items
 */
const getRecordItems = async(req, res) => {
    try {
        const { id } = req.params;
        
        if (!id) {
            return res.status(400).json({ success: false, message: '记录ID不能为空' });
        }

        // 查询记录明细
        const sql = `
            SELECT 
                mri.id,
                mri.material_id,
                mri.quantity,
                mri.current_quantity,
                m.name as material_name,
                m.company_code as material_code,
                m.specification,
                m.unit
            FROM material_record_items mri
            LEFT JOIN materials m ON mri.material_id = m.id
            WHERE mri.record_id = ?
            ORDER BY mri.created_at ASC
        `;
        
        const items = await query(sql, [id]);
        
        res.json({ 
            success: true, 
            data: items 
        });
    } catch (error) {
        logger.error('获取记录明细失败', error);
        res.status(500).json({ success: false, message: '获取记录明细失败' });
    }
};

/**
 * 获取物料库存记录
 * GET /api/materials/:id/stock-records
 */
const getMaterialStockRecords = async(req, res) => {
    try {
        const { id } = req.params;
        
        if (!id) {
            return res.status(400).json({ success: false, message: '物料ID不能为空' });
        }

        // 查询物料库存记录
        const sql = `
            SELECT 
                mr.id,
                mr.type,
                mr.order_id,
                mr.remarks,
                mr.created_at,
                mri.quantity,
                mri.current_quantity,
                (mri.current_quantity + mri.quantity) as new_quantity,
                e.name as recipient_name
            FROM material_records mr
            LEFT JOIN material_record_items mri ON mr.id = mri.record_id
            LEFT JOIN employees e ON mr.recipient = e.id
            WHERE mri.material_id = ?
            ORDER BY mr.created_at DESC
        `;
        
        const records = await query(sql, [id]);
        
        res.json({ 
            success: true, 
            data: records 
        });
    } catch (error) {
        logger.error('获取物料库存记录失败', error);
        res.status(500).json({ success: false, message: '获取物料库存记录失败' });
    }
};

module.exports = {
    getMaterials,
    getMaterialById,
    createMaterial,
    updateMaterial,
    deleteMaterial,
    batchDeleteMaterials,
    createClientCode,
    exportMaterialsExcel,
    importMaterialsExcel,
    getImportProgress,
    batchInbound,
    materialReturn,
    getMaterialRecords,
    getRecordItems,
    getMaterialStockRecords
};