# 工程管理系统前端

## 项目简介

这是一个基于 Vue 3 + TypeScript + Element Plus 的工程管理系统前端项目。

## 功能模块

### 散户订单管理
- **订单列表**: 查看和管理散户订单
- **订单分派**: 将订单分派给相应的师傅
- **订单执行**: 跟踪订单执行状态
- **月度平账**: 月度财务结算
- **平账记录**: 查看历史平账记录和详细统计信息

### 平账记录功能

平账记录模块提供了完整的财务统计和记录查看功能：

#### 主要功能
1. **搜索筛选**: 支持按平账时间、月份、状态进行筛选
2. **记录列表**: 显示平账时间、月份、师傅人数、完成订单数、订单总金额、物料成本、人工成本、利润、利润率等关键指标
3. **详情查看**: 点击查看按钮可查看详细的平账信息
4. **数据导出**: 支持导出Excel和打印报表
5. **统计图表**: 提供数据可视化分析

#### 详情页面包含
- **统计信息**: 师傅人数、完成订单、订单总金额、物料成本、人工成本、利润、利润率
- **人员工资明细**: 显示每位师傅的工种、工价、工作天数、总工资、已发放金额
- **订单分类统计**: 按订单类型统计完成数、金额、物料成本、人工成本、利润

#### 数据字段说明
- **平账时间**: 平账操作的具体时间
- **平账月份**: 对应的业务月份
- **师傅人数**: 参与该月工作的师傅总数
- **完成订单数**: 该月完成的订单数量
- **订单总金额**: 所有完成订单的总金额
- **物料成本**: 该月使用的物料总成本
- **人工成本**: 该月的人工工资总成本
- **利润**: 订单总金额减去物料成本和人工成本
- **利润率**: 利润占订单总金额的百分比

## 技术栈

- **框架**: Vue 3
- **语言**: TypeScript
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **构建工具**: Vite
- **样式**: SCSS

## 开发环境

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 代码检查
```bash
npm run lint
```

### 代码格式化
```bash
npm run format
```

## 项目结构

```
src/
├── assets/          # 静态资源
├── components/      # 公共组件
├── layout/          # 布局组件
├── router/          # 路由配置
├── stores/          # 状态管理
├── utils/           # 工具函数
├── views/           # 页面组件
│   ├── dashboard/   # 首页
│   ├── employees/   # 员工管理
│   ├── looseOrders/ # 散户订单
│   ├── projects/    # 工程订单
│   ├── system/      # 系统设置
│   └── warehouse/   # 仓库管理
└── main.ts          # 入口文件
```

## Mock数据

项目使用Mock数据进行开发，Mock文件位于 `public/mock/` 目录下：

- `balance_records.json`: 平账记录数据
- `loose_orders.json`: 散户订单数据
- `materials.json`: 物料数据
- `employees.json`: 员工数据
- `projects.json`: 工程订单数据
- `users.json`: 用户数据

## 路由配置

平账记录页面的路由配置：
- 路径: `/loose-orders/balance-records`
- 名称: `BalanceRecords`
- 标题: `平账记录`
- 需要认证: 是

## 开发规范

1. **组件命名**: 使用PascalCase命名组件
2. **文件命名**: 使用kebab-case命名文件
3. **变量命名**: 使用camelCase命名变量
4. **常量命名**: 使用UPPER_SNAKE_CASE命名常量
5. **类型定义**: 为所有数据定义TypeScript类型
6. **注释规范**: 为复杂逻辑添加注释

## 部署说明

1. 执行构建命令生成生产文件
2. 将 `dist` 目录下的文件部署到Web服务器
3. 配置服务器支持Vue Router的history模式
4. 确保API接口地址配置正确

## 更新日志

### v1.0.0
- 初始版本发布
- 实现基础功能模块
- 添加平账记录功能
- 完善Mock数据支持 