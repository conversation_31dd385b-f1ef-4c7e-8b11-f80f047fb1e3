工程管理系统 - 技术架构设计文档
==========================================

## 1. 项目概述

### 1.1 项目简介
工程管理系统是一个用于管理工程物料、订单、员工和财务的综合管理平台。系统主要服务于工程公司，帮助管理仓库物料、散户订单、工程订单、员工信息和系统设置等功能。

### 1.2 技术选型
- **前端框架**: Vue 3 + TypeScript
- **状态管理**: Pinia
- **UI组件库**: Element Plus
- **路由管理**: Vue Router
- **网络请求**: Axios
- **构建工具**: Vite
- **代码规范**: ESLint + Prettier

## 2. 系统架构设计

### 2.1 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        前端层                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │   Vue 3     │ │  Element    │ │   Axios     │        │
│  │ TypeScript  │ │    Plus     │ │  请求库     │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                        网关层                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │   Nginx     │ │    CORS     │ │   限流      │        │
│  │  反向代理   │ │   跨域处理   │ │   防护      │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                        应用层                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │   Spring    │ │   权限      │ │   日志      │        │
│  │    Boot     │ │   验证      │ │   记录      │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                        数据层                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │   Sqlite    │ │   Redis     │ │   文件      │        │
│  │   主数据库   │ │   缓存      │ │   存储      │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 模块划分
```
工程管理系统
├── 首页模块 (Dashboard)
│   ├── 工程物料统计
│   ├── 工程进度统计
│   ├── 散单物料统计

├── 仓库管理模块 (Warehouse)
│   ├── 物料列表管理
│   ├── 领料申请管理
│   ├── 入库管理
│   ├── 价格管理
│   └── 进出记录
├── 散户订单模块 (Loose Orders)
│   ├── 订单列表
│   ├── 订单分派
│   └── 月度平账
├── 工程订单模块 (Project Orders)
│   ├── 工程列表
│   ├── 工程推进
│   └── 工种管理
├── 员工管理模块 (Employee)
│   ├── 员工列表
│   ├── 工种设置
│   └── 绩效管理
└── 系统设置模块 (System)
    ├── 用户管理
    ├── 权限管理
    ├── 系统日志
    └── 数据导入
```

## 3. 安全设计

### 3.1 身份认证
- **JWT Token认证**: 用户登录后获取JWT令牌，用于后续请求的身份验证
- **Token过期机制**: 设置合理的Token过期时间，定期刷新
- **密码加密**: 使用BCrypt算法对用户密码进行加密存储

### 3.2 权限控制
- **RBAC权限模型**: 基于角色的访问控制
- **菜单权限**: 根据用户角色动态显示菜单
- **按钮权限**: 细粒度的操作权限控制
- **数据权限**: 根据用户角色限制数据访问范围

### 3.3 网络安全
- **HTTPS加密**: 使用SSL证书确保数据传输安全
- **CORS配置**: 严格控制跨域请求
- **SQL注入防护**: 使用参数化查询，避免SQL注入攻击
- **XSS防护**: 对用户输入进行过滤和转义
- **CSRF防护**: 使用CSRF Token防止跨站请求伪造

### 3.4 数据安全
- **数据备份**: 定期备份数据库，确保数据安全
- **敏感数据加密**: 对敏感信息进行加密存储
- **操作日志**: 记录所有重要操作，便于审计

## 4. 数据库设计

### 4.1 数据库选型
- **主数据库**: MySQL 8.0
- **缓存数据库**: Redis 6.0
- **文件存储**: 本地文件系统

### 4.2 核心数据表
```
用户表 (users)
├── id: 主键
├── username: 用户名
├── password: 密码(加密)
├── role_id: 角色ID
├── phone: 手机号
├── email: 邮箱
├── status: 状态
└── created_at: 创建时间

角色表 (roles)
├── id: 主键
├── name: 角色名称
├── description: 角色描述
└── status: 状态

权限表 (permissions)
├── id: 主键
├── name: 权限名称
├── code: 权限代码
├── module: 所属模块
└── status: 状态

物料表 (materials)
├── id: 主键
├── code: 公司物料编码
├── name: 物料名称
├── model: 型号
├── specification: 规格
├── unit: 单位
├── category: 分类(甲料/商品/辅料)
├── price: 单价
├── quantity: 库存数量
└── warning_quantity: 预警数量

订单表 (orders)
├── id: 主键
├── order_no: 订单号
├── customer_name: 客户姓名
├── address: 地址
├── phone: 电话
├── order_type: 订单类型
├── status: 订单状态
├── assigned_worker: 分派师傅
├── total_amount: 总金额
└── created_at: 创建时间

工程表 (projects)
├── id: 主键
├── project_no: 工程编号
├── project_name: 工程名称
├── address: 工程地址
├── status: 工程状态
├── start_date: 开始时间
├── end_date: 结束时间
├── progress: 进度百分比
└── total_cost: 总成本

员工表 (employees)
├── id: 主键
├── name: 员工姓名
├── work_type_id: 工种ID
├── daily_wage: 日工资
├── phone: 联系电话
├── status: 状态
└── hire_date: 入职时间
```

## 5. 接口设计

### 5.1 接口规范
- **RESTful API**: 遵循REST设计原则
- **统一响应格式**: 所有接口返回统一的数据格式
- **错误码规范**: 定义标准的错误码和错误信息
- **接口版本控制**: 使用URL版本控制

### 5.2 核心接口示例
```
# 用户认证
POST /api/v1/auth/login          # 用户登录
POST /api/v1/auth/logout         # 用户登出
POST /api/v1/auth/refresh        # 刷新Token

# 物料管理
GET    /api/v1/materials         # 获取物料列表
POST   /api/v1/materials         # 新增物料
PUT    /api/v1/materials/{id}    # 更新物料
DELETE /api/v1/materials/{id}    # 删除物料

# 订单管理
GET    /api/v1/orders            # 获取订单列表
POST   /api/v1/orders            # 新增订单
PUT    /api/v1/orders/{id}       # 更新订单
POST   /api/v1/orders/{id}/assign # 订单分派

# 工程管理
GET    /api/v1/projects          # 获取工程列表
POST   /api/v1/projects          # 新增工程
PUT    /api/v1/projects/{id}     # 更新工程
POST   /api/v1/projects/{id}/progress # 工程推进
```

## 6. 部署架构

### 6.1 部署环境
```
生产环境
├── 配置文件：.env.production config.production.js
├── API地址：api.domain.com
├── 数据库服务器: Sqlite
├── 缓存服务器: Redis
├── 调试模式：false
└── 文件服务器: 本地存储

开发环境
├── 配置文件：.env.development config.development.js
├── API接口：使用本地json文件模拟
├── 数据库: Sqlite
├── 调试模式：true
├── 文件服务器: 本地存储
└── 缓存: Redis
```
### 6.1.1 启动时配置文件切换方案

为实现不同环境（生产、开发、测试等）下的配置文件自动切换，建议采用如下方式：

#### 前端（Vite + Vue3）
- 使用`.env.development`、`.env.production`等环境变量文件。
- 启动命令区分环境，例如：
  - 开发环境：`npm run dev`（默认读取`.env.development`）
  - 生产环境：`npm run build`（默认读取`.env.production`）
- 可在`package.json`中配置不同的启动脚本：
  ```json
  "scripts": {
    "dev": "vite --mode development",
    "build": "vite build --mode production"
  }
  ```
- 代码中通过`import.meta.env`访问环境变量。


#### Docker 部署
- 在`docker-compose.yml`中通过`environment`字段注入环境变量，或挂载不同的配置文件。
- 例如：
  ```yaml
  environment:
    - SPRING_PROFILES_ACTIVE=prod
  ```

#### 总结
- 前后端均通过环境变量或启动参数自动切换配置文件，无需手动修改。
- 保证每个环境的敏感信息、数据库、API地址等配置独立，提升安全性和运维效率。


### 6.2 部署流程
1. **代码构建**: 使用Maven构建后端，Vite构建前端
2. **镜像打包**: 使用Docker打包应用镜像
3. **环境部署**: 使用Docker Compose部署到服务器
4. **服务启动**: 启动Nginx、应用服务、数据库等
5. **健康检查**: 检查各服务是否正常运行

## 7. 性能优化

### 7.1 前端优化
- **代码分割**: 按路由分割代码，减少首屏加载时间
- **图片优化**: 使用WebP格式，压缩图片大小
- **缓存策略**: 合理设置浏览器缓存
- **懒加载**: 对非首屏内容进行懒加载

### 7.2 后端优化
- **数据库索引**: 为常用查询字段建立索引
- **连接池**: 使用数据库连接池提高性能
- **缓存策略**: 使用Redis缓存热点数据
- **分页查询**: 大数据量查询使用分页

### 7.3 数据库优化
- **查询优化**: 优化SQL语句，避免全表扫描
- **索引优化**: 合理建立索引，避免索引失效
- **分表策略**: 大表按时间或业务进行分表
- **备份策略**: 定期备份，确保数据安全

## 8. 监控与维护

### 8.1 系统监控
- **应用监控**: 监控应用运行状态、响应时间
- **数据库监控**: 监控数据库连接数、查询性能
- **服务器监控**: 监控CPU、内存、磁盘使用情况
- **日志监控**: 监控错误日志，及时发现问题

### 8.2 运维管理
- **日志管理**: 统一收集和管理系统日志
- **备份管理**: 定期备份数据库和文件
- **版本管理**: 使用Git进行代码版本控制
- **发布管理**: 使用CI/CD自动化部署

## 9. 扩展性考虑

### 9.1 水平扩展
- **负载均衡**: 使用Nginx进行负载均衡
- **数据库读写分离**: 主库写，从库读
- **缓存集群**: Redis集群提高缓存性能
- **微服务架构**: 未来可考虑拆分为微服务

### 9.2 功能扩展
- **移动端适配**: 开发移动端应用
- **第三方集成**: 集成支付、短信等服务
- **数据分析**: 添加数据分析和报表功能
- **工作流**: 添加审批工作流功能

## 10. 总结

本技术架构设计采用前后端分离的架构模式，使用成熟稳定的技术栈，确保系统的安全性、可维护性和可扩展性。通过合理的模块划分、安全设计和性能优化，为工程管理系统提供了坚实的技术基础。

架构设计遵循简洁明了的原则，便于技术人员理解和实施，同时考虑了系统的安全性和稳定性，能够满足工程管理业务的需求。 