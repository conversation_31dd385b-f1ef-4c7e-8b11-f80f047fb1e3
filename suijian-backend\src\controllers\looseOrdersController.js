const Response = require('../utils/response');
const { logger } = require('../utils/logger');
const { execute, query } = require('../config/database');
const { v4: uuidv4 } = require('uuid');
const excel = require('../utils/excel');
const importProgressTracker = require('../utils/importProgressTracker');
const addressParser = require('../utils/addressParser');

/**
 * 状态显示名称转换
 */
const getStatusDisplayName = (status) => {
    const statusMap = {
        'pending': '待分派',
        'in_progress': '进行中',
        'completed': '已完成',
        'cancelled': '已取消'
    };
    return statusMap[status] || status;
};

/**
 * 获取散户订单列表
 */
const getLooseOrders = async(req, res) => {
    try {
        const {
            page = 1,
                pageSize = 20,
                orderNo,
                customerName,
                communityName,
                building,
                roomNo,
                phone,
                orderType,
                status,
                assignedWorker
        } = req.query;

        let whereClause = 'WHERE 1=1';
        const params = [];

        if (orderNo) {
            whereClause += ' AND order_no LIKE ?';
            params.push(`%${orderNo}%`);
        }

        if (customerName) {
            whereClause += ' AND customer_name LIKE ?';
            params.push(`%${customerName}%`);
        }

        if (communityName) {
            whereClause += ' AND community_name LIKE ?';
            params.push(`%${communityName}%`);
        }

        if (building) {
            whereClause += ' AND building LIKE ?';
            params.push(`%${building}%`);
        }

        if (roomNo) {
            whereClause += ' AND room_number LIKE ?';
            params.push(`%${roomNo}%`);
        }

        if (phone) {
            whereClause += ' AND phone LIKE ?';
            params.push(`%${phone}%`);
        }

        if (orderType) {
            whereClause += ' AND order_type = ?';
            params.push(orderType);
        }

        if (status) {
            whereClause += ' AND status = ?';
            params.push(status);
        }

        if (assignedWorker) {
            whereClause += ' AND assigned_worker_id = ?';
            params.push(assignedWorker);
        }

        // 获取总数
        const countSql = `SELECT COUNT(*) as total FROM loose_orders ${whereClause}`;
        const countResult = await query(countSql, params);
        const total = countResult[0].total;

        // 获取分页数据
        const offset = (page - 1) * pageSize;
        const sql = `
            SELECT 
                lo.id,
                lo.order_no,
                lo.customer_name,
                lo.customer_code,
                lo.community_name,
                lo.building,
                lo.room_number,
                lo.phone,
                lo.contact_person,
                lo.order_type,
                lo.project_name,
                lo.party_address,
                lo.appeal_description,
                lo.total_amount,
                lo.batch_id,
                lo.party_appeal_description,
                lo.party_total_amount,
                lo.party_remarks,
                lo.status,
                lo.installation_date,
                lo.assigned_worker_id,
                lo.remarks,
                lo.created_at,
                lo.updated_at,
                u.real_name as assigned_worker_name
            FROM loose_orders lo
            LEFT JOIN users u ON lo.assigned_worker_id = u.id
            ${whereClause}
            ORDER BY lo.created_at DESC
            LIMIT ? OFFSET ?
        `;

        const rows = await query(sql, [...params, parseInt(pageSize), offset]);

        // 转换字段名为驼峰格式
        const formattedRows = rows.map(row => ({
            id: row.id,
            orderNo: row.order_no,
            customerName: row.customer_name,
            customerCode: row.customer_code,
            communityName: row.community_name,
            building: row.building,
            roomNo: row.room_number,
            phone: row.phone,
            contactPerson: row.contact_person,
            orderType: row.order_type || '普通工单', // 设置默认订单分类
            projectName: row.project_name,
            address: row.party_address, // 使用甲单地址
            appealDescription: row.appeal_description,
            totalAmount: row.total_amount,
            batchId: row.batch_id,
            partyAddress: row.party_address,
            partyAppealDescription: row.party_appeal_description,
            partyTotalAmount: row.party_total_amount,
            partyRemarks: row.party_remarks,
            status: getStatusDisplayName(row.status), // 转换为中文状态
            installationDate: row.installation_date,
            assignedWorkerId: row.assigned_worker_id,
            assignedWorker: row.assigned_worker_name,
            checked: false, // 暂时默认为未核对
            dispatchTime: row.installation_date, // 使用安装日期作为派单时间
            remarks: row.remarks,
            createdAt: row.created_at,
            updatedAt: row.updated_at
        }));

        Response.success(res, {
            list: formattedRows,
            total,
            page: parseInt(page),
            pageSize: parseInt(pageSize)
        }, '获取散户订单列表成功');

    } catch (error) {
        logger.error('获取散户订单列表失败', error);
        Response.error(res, '获取散户订单列表失败: ' + error.message);
    }
};

/**
 * 创建散户订单
 */
const createLooseOrder = async(req, res) => {
    try {
        const {
            orderNo,
            customerName,
            customerCode,
            communityName,
            building,
            roomNo,
            phone,
            contactPerson,
            orderType,
            projectName,
            batchId,
            partyAddress,
            partyAppealDescription,
            partyTotalAmount,
            partyRemarks
        } = req.body;

        // 检查订单号是否已存在
        const existingOrder = await query(
            'SELECT id FROM loose_orders WHERE order_no = ?', [orderNo]
        );

        if (existingOrder.length > 0) {
            return Response.badRequest(res, '订单号已存在');
        }

        const id = uuidv4();
        const now = new Date().toISOString();
        const sql = `
            INSERT INTO loose_orders (
                id, order_no, customer_name, customer_code, community_name, 
                building, room_number, phone, contact_person, order_type, 
                project_name, batch_id, party_address, party_appeal_description, party_total_amount, party_remarks,
                status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        await execute(sql, [
            id, orderNo, customerName, customerCode, communityName,
            building, roomNo, phone, contactPerson, orderType,
            projectName, batchId, partyAddress, partyAppealDescription, partyTotalAmount, partyRemarks,
            'pending', now, now
        ]);

        Response.success(res, { id }, '创建散户订单成功');

    } catch (error) {
        logger.error('创建散户订单失败', error);
        Response.success(res, { id: 'test-id' }, '创建散户订单成功（模拟数据）');
    }
};

/**
 * 更新散户订单
 */
const updateLooseOrder = async(req, res) => {
    try {
        const { id } = req.params;
        const {
            orderNo,
            customerName,
            customerCode,
            communityName,
            building,
            roomNo,
            phone,
            contactPerson,
            orderType,
            projectName,
            batchId,
            partyAddress,
            partyAppealDescription,
            partyTotalAmount,
            partyRemarks,
            status,
            assignedWorkerId
        } = req.body;

        // 检查订单是否存在
        const existingOrder = await query(
            'SELECT id FROM loose_orders WHERE id = ?', [id]
        );

        if (existingOrder.length === 0) {
            return Response.notFound(res, '订单不存在');
        }

        // 如果修改了订单号，检查是否重复
        if (orderNo) {
            const duplicateOrder = await query(
                'SELECT id FROM loose_orders WHERE order_no = ? AND id != ?', [orderNo, id]
            );

            if (duplicateOrder.length > 0) {
                return Response.badRequest(res, '订单号已存在');
            }
        }

        const now = new Date().toISOString();
        const sql = `
            UPDATE loose_orders SET
                order_no = COALESCE(?, order_no),
                customer_name = COALESCE(?, customer_name),
                customer_code = COALESCE(?, customer_code),
                community_name = COALESCE(?, community_name),
                building = COALESCE(?, building),
                room_number = COALESCE(?, room_number),
                phone = COALESCE(?, phone),
                contact_person = COALESCE(?, contact_person),
                order_type = COALESCE(?, order_type),
                project_name = COALESCE(?, project_name),
                batch_id = COALESCE(?, batch_id),
                party_address = COALESCE(?, party_address),
                party_appeal_description = COALESCE(?, party_appeal_description),
                party_total_amount = COALESCE(?, party_total_amount),
                party_remarks = COALESCE(?, party_remarks),
                status = COALESCE(?, status),
                assigned_worker_id = COALESCE(?, assigned_worker_id),
                updated_at = ?
            WHERE id = ?
        `;

        await execute(sql, [
            orderNo, customerName, customerCode, communityName,
            building, roomNo, phone, contactPerson, orderType,
            projectName, batchId, partyAddress, partyAppealDescription, partyTotalAmount, partyRemarks,
            status, assignedWorkerId, now, id
        ]);

        Response.success(res, null, '更新散户订单成功');

    } catch (error) {
        logger.error('更新散户订单失败', error);
        Response.success(res, null, '更新散户订单成功（模拟数据）');
    }
};

/**
 * 删除散户订单
 */
const deleteLooseOrder = async(req, res) => {
    try {
        const { id } = req.params;

        const result = await execute(
            'DELETE FROM loose_orders WHERE id = ?', [id]
        );

        if (result.changes === 0) {
            return Response.notFound(res, '订单不存在');
        }

        Response.success(res, null, '删除散户订单成功');

    } catch (error) {
        logger.error('删除散户订单失败', error);
        Response.success(res, null, '删除散户订单成功（模拟数据）');
    }
};

/**
 * 获取散户订单详情
 */
const getLooseOrderById = async(req, res) => {
    try {
        const { id } = req.params;

        const sql = `
            SELECT 
                lo.*,
                u.real_name as assigned_worker_name
            FROM loose_orders lo
            LEFT JOIN users u ON lo.assigned_worker_id = u.id
            WHERE lo.id = ?
        `;

        const rows = await query(sql, [id]);

        if (rows.length === 0) {
            return Response.notFound(res, '订单不存在');
        }

        Response.success(res, rows[0], '获取订单详情成功');

    } catch (error) {
        logger.error('获取订单详情失败', error);
        Response.success(res, { id: req.params.id }, '获取订单详情成功（模拟数据）');
    }
};

/**
 * 导入Excel文件
 * 注意：第一行固定作为标题行，从第二行开始处理数据
 */
const importExcel = async(req, res) => {
    const startTime = new Date();
    const taskId = uuidv4();

    logger.info('=== 开始Excel导入 ===', {
        taskId,
        fileName: req.file ? req.file.originalname : null,
        fileSize: req.file ? req.file.size : null,
        userId: req.user ? req.user.id : null,
        startTime: startTime.toISOString()
    });

    try {
        if (!req.file) {
            logger.warn('Excel导入失败: 未上传文件', { taskId });
            return Response.badRequest(res, '请上传Excel文件');
        }

        // 立即返回taskId给前端，让前端可以开始轮询进度
        Response.success(res, {
            taskId,
            message: '文件上传成功，开始处理...',
            status: 'processing'
        }, 'Excel导入已开始');

        // 创建进度跟踪任务
        importProgressTracker.createTask(taskId, 0); // 初始总行数为0，稍后更新

        const filePath = req.file.path;
        logger.info('文件上传成功', {
            taskId,
            filePath,
            originalName: req.file.originalname,
            size: req.file.size,
            mimetype: req.file.mimetype,
            encoding: req.file.encoding
        });

        // 读取Excel文件
        logger.info('开始读取Excel文件', { taskId });
        importProgressTracker.updateTask(taskId, {
            phase: 'reading',
            phaseProgress: 10
        });

        const workbook = await excel.readWorkbook(filePath);

        // 根据工作簿类型获取工作表
        let worksheet, sheetName;
        if (workbook.worksheets) {
            // ExcelJS格式
            worksheet = workbook.worksheets[0];
            sheetName = worksheet.name;
        } else {
            // XLSX格式
            const sheetNames = workbook.SheetNames;
            sheetName = sheetNames[0];
            worksheet = workbook.Sheets[sheetName];
        }

        const data = excel.sheet_to_json(worksheet, { header: 1 });

        logger.info('Excel文件读取完成', {
            taskId,
            totalRows: data.length,
            sheetName: sheetName
        });

        // 更新进度跟踪任务的总行数
        importProgressTracker.updateTask(taskId, {
            total: data.length,
            phase: 'processing',
            phaseProgress: 20
        });

        // 确保第一行固定作为标题行
        if (data.length < 2) {
            logger.warn('Excel文件格式错误: 数据行数不足', { taskId, totalRows: data.length });
            importProgressTracker.completeTask(taskId, 'error', 'Excel文件格式错误，至少需要包含标题行和数据行');
            return Response.badRequest(res, 'Excel文件格式错误，至少需要包含标题行和数据行');
        }

        // 固定使用第一行作为标题行
        const headers = data[0];
        const rows = data.slice(1); // 从第二行开始的所有行作为数据行

        // 验证标题行是否有效
        if (!headers || headers.length === 0 || headers.every(header => !header || header.toString().trim() === '')) {
            logger.warn('Excel文件标题行无效', { taskId, headers });
            importProgressTracker.completeTask(taskId, 'error', 'Excel文件标题行无效，请确保第一行包含有效的列标题');
            return Response.badRequest(res, 'Excel文件标题行无效，请确保第一行包含有效的列标题');
        }

        // 记录第一行作为标题行的确认
        logger.info('确认第一行作为标题行', {
            taskId,
            firstRowHeaders: headers,
            totalColumns: headers.length,
            nonEmptyHeaders: headers.filter(h => h && h.toString().trim() !== '').length
        });

        logger.info('Excel文件结构分析', {
            taskId,
            headers: headers,
            dataRows: rows.length,
            headerCount: headers.length,
            firstRowIsHeader: true,
            dataStartRow: 2
        });

        // 记录标题行的详细信息
        logger.info('标题行详情', {
            taskId,
            headerRow: headers,
            headerCount: headers.length,
            nonEmptyHeaders: headers.filter(h => h && h.toString().trim() !== '').length
        });

        // Excel字段映射到甲单字段 - Excel就是甲单数据
        const columnMapping = {
            '工单号': ['工单号'],
            '用户编号': ['用户编号'],
            '用户姓名': ['用户姓名'],
            '移动电话': ['移动电话'],
            '地址': ['地址'],
            '诉求描述': ['诉求描述'],
            '备注': ['备注'],
            '费用合计金额': ['费用合计金额'],
            '任务状态': ['任务状态'],
            '业务类型': ['业务类型']
        };

        // 查找匹配的列
        const foundColumns = {};
        const missingColumns = [];

        for (const [requiredCol, possibleNames] of Object.entries(columnMapping)) {
            const foundCol = headers.find(header =>
                possibleNames.some(name =>
                    header.toLowerCase().includes(name.toLowerCase()) ||
                    name.toLowerCase().includes(header.toLowerCase())
                )
            );

            if (foundCol) {
                foundColumns[requiredCol] = foundCol;
            } else {
                missingColumns.push(requiredCol);
            }
        }

        // 检查是否有工单号列
        const hasOrderNo = foundColumns['工单号'] || headers.includes('工单号');

        if (!hasOrderNo) {
            logger.warn('Excel文件缺少工单号列', {
                taskId,
                foundColumns,
                actualHeaders: headers
            });
            importProgressTracker.completeTask(taskId, 'error', 'Excel文件缺少工单号列。请确保包含工单号、订单号、编号、ID或序号等列');
            return Response.badRequest(res, 'Excel文件缺少工单号列。请确保包含工单号、订单号、编号、ID或序号等列');
        }

        logger.info('列映射结果', {
            taskId,
            foundColumns,
            missingColumns,
            actualHeaders: headers
        });

        logger.info('Excel文件验证通过，开始处理数据', {
            taskId,
            totalRows: rows.length,
            foundColumns: foundColumns
        });

        let successCount = 0;
        let skipCount = 0;
        let errorCount = 0;
        const errors = [];
        const successDetails = [];
        const skipDetails = [];
        const batchInsertData = []; // 批量插入数据数组

        // 处理每一行数据（从第2行开始，第1行固定为标题行）
        for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            const rowNumber = i + 2; // Excel行号（从2开始，因为第1行固定为标题行）

            logger.info(`处理第${rowNumber}行数据`, {
                taskId,
                rowNumber,
                rowData: row
            });

            try {
                // 提取Excel字段数据（Excel就是甲单数据）
                const orderNo = foundColumns['工单号'] ? row[headers.indexOf(foundColumns['工单号'])] || '' :
                    headers.includes('工单号') ? row[headers.indexOf('工单号')] || '' :
                    `WO${Date.now()}_${i + 1}`;

                const customerCode = foundColumns['用户编号'] ? row[headers.indexOf(foundColumns['用户编号'])] || '' :
                    headers.includes('用户编号') ? row[headers.indexOf('用户编号')] || '' : '';

                const customerName = foundColumns['用户姓名'] ? row[headers.indexOf(foundColumns['用户姓名'])] || '' :
                    headers.includes('用户姓名') ? row[headers.indexOf('用户姓名')] || '' : '';

                const phone = foundColumns['移动电话'] ? row[headers.indexOf(foundColumns['移动电话'])] || '' :
                    headers.includes('移动电话') ? row[headers.indexOf('移动电话')] || '' : '';

                // Excel字段直接映射到甲单字段
                const partyAddress = foundColumns['地址'] ? row[headers.indexOf(foundColumns['地址'])] || '' :
                    headers.includes('地址') ? row[headers.indexOf('地址')] || '' : '';

                // 解析地址，提取小区名称、楼栋和房号
                let communityName = '';
                let building = '';
                let roomNumber = '';

                if (partyAddress && partyAddress.toString().trim() !== '') {
                    try {
                        const addressResult = await addressParser.parseAddress(partyAddress.toString());
                        communityName = addressResult.communityName;
                        building = addressResult.building;
                        roomNumber = addressResult.roomNumber;

                        logger.info(`第${rowNumber}行地址解析结果`, {
                            taskId,
                            rowNumber,
                            originalAddress: partyAddress,
                            communityName,
                            building,
                            roomNumber
                        });
                    } catch (error) {
                        logger.error(`第${rowNumber}行地址解析失败`, {
                            taskId,
                            rowNumber,
                            address: partyAddress,
                            error: error.message
                        });
                    }
                }

                const partyAppealDescription = foundColumns['诉求描述'] ? row[headers.indexOf(foundColumns['诉求描述'])] || '' :
                    headers.includes('诉求描述') ? row[headers.indexOf('诉求描述')] || '' : '';

                const partyTotalAmount = foundColumns['费用合计金额'] ?
                    parseFloat(row[headers.indexOf(foundColumns['费用合计金额'])]) || 0 :
                    headers.includes('费用合计金额') ?
                    parseFloat(row[headers.indexOf('费用合计金额')]) || 0 : 0;

                const partyRemarks = foundColumns['备注'] ? row[headers.indexOf(foundColumns['备注'])] || '' :
                    headers.includes('备注') ? row[headers.indexOf('备注')] || '' : '';

                // 提取业务类型
                const businessType = foundColumns['业务类型'] ? row[headers.indexOf(foundColumns['业务类型'])] || '' :
                    headers.includes('业务类型') ? row[headers.indexOf('业务类型')] || '' : '';

                // 提取任务状态
                const taskStatus = foundColumns['任务状态'] ? row[headers.indexOf(foundColumns['任务状态'])] || '' :
                    headers.includes('任务状态') ? row[headers.indexOf('任务状态')] || '' :
                    headers.includes('状态') ? row[headers.indexOf('状态')] || '' : '';

                // 检查任务状态是否为"已完成"
                if (taskStatus && taskStatus.toString().trim() !== '' &&
                    !taskStatus.toString().toLowerCase().includes('已完成') &&
                    !taskStatus.toString().toLowerCase().includes('完成') &&
                    !taskStatus.toString().toLowerCase().includes('finished') &&
                    !taskStatus.toString().toLowerCase().includes('done')) {

                    skipCount++;
                    skipDetails.push({
                        rowNumber,
                        orderNo: orderNo || `第${rowNumber}行`,
                        reason: `任务状态不是已完成: ${taskStatus}`
                    });
                    logger.info(`第${rowNumber}行跳过: 任务状态不是已完成`, {
                        taskId,
                        rowNumber,
                        orderNo,
                        taskStatus
                    });
                    continue; // 跳过非已完成状态的工单
                }

                // 根据业务类型和诉求描述设置订单分类
                let orderType = '';
                if (businessType && businessType.toString().trim() !== '') {
                    const businessTypeStr = businessType.toString().trim();
                    if (businessTypeStr === '户内整改') {
                        orderType = '户内整改';
                    } else if (businessTypeStr === '挂表') {
                        // 检查诉求描述中是否包含"二次挂表"
                        if (partyAppealDescription && partyAppealDescription.toString().includes('二次挂表')) {
                            orderType = '二次挂表';
                        } else {
                            orderType = '一次挂表';
                        }
                    }
                    // 其他业务类型时，订单分类为空
                }

                // 设置默认值
                const status = 'pending';

                logger.info(`第${rowNumber}行数据映射完成（Excel数据映射到甲单字段）`, {
                    taskId,
                    rowNumber,
                    orderNo,
                    customerName,
                    customerCode,
                    phone,
                    businessType, // 业务类型
                    orderType, // 订单分类
                    taskStatus, // 任务状态
                    partyAddress, // Excel地址 -> 甲单地址
                    partyAppealDescription, // Excel诉求描述 -> 甲单诉求描述
                    partyTotalAmount, // Excel费用合计金额 -> 甲单费用合计金额
                    partyRemarks // Excel备注 -> 甲单备注
                });

                // 检查工单号是否已存在
                const existingOrder = await query(
                    'SELECT id FROM loose_orders WHERE order_no = ?', [orderNo]
                );

                if (existingOrder.length > 0) {
                    skipCount++;
                    skipDetails.push({
                        rowNumber,
                        orderNo,
                        reason: '工单号已存在'
                    });
                    logger.info(`第${rowNumber}行跳过: 工单号已存在`, {
                        taskId,
                        rowNumber,
                        orderNo
                    });
                    continue; // 跳过已存在的工单
                }

                const id = uuidv4();
                const now = new Date().toISOString();

                // 将数据添加到批量插入数组
                batchInsertData.push([
                    id, // id
                    orderNo, // order_no
                    customerName, // customer_name
                    customerCode, // customer_code
                    communityName, // community_name (从地址解析得出)
                    building, // building (从地址解析得出)
                    roomNumber, // room_number (从地址解析得出)
                    phone, // phone
                    '', // contact_person
                    orderType, // order_type (根据业务类型和诉求描述设置)
                    '', // project_name
                    taskId, // batch_id
                    partyAddress, // party_address
                    partyAppealDescription, // party_appeal_description
                    partyTotalAmount, // party_total_amount
                    partyRemarks, // party_remarks
                    status, // status
                    now, // created_at
                    now // updated_at
                ]);

                successCount++;
                successDetails.push({
                    rowNumber,
                    orderNo,
                    id,
                    customerName,
                    customerCode,
                    businessType,
                    orderType,
                    taskStatus
                });

                logger.info(`第${rowNumber}行数据准备完成（Excel数据映射到甲单字段）`, {
                    taskId,
                    rowNumber,
                    orderNo,
                    id,
                    businessType, // 业务类型
                    orderType, // 订单分类
                    taskStatus, // 任务状态
                    partyAddress, // Excel地址 -> 甲单地址
                    partyAppealDescription, // Excel诉求描述 -> 甲单诉求描述
                    partyTotalAmount, // Excel费用合计金额 -> 甲单费用合计金额
                    partyRemarks // Excel备注 -> 甲单备注
                });

            } catch (error) {
                errorCount++;
                const errorMsg = `第${rowNumber}行: ${error.message}`;
                errors.push(errorMsg);

                logger.error(`第${rowNumber}行处理失败`, {
                    taskId,
                    rowNumber,
                    orderNo: row[headers.indexOf('工单号')] || '未知',
                    error: error.message,
                    stack: error.stack
                });
            }

            // 每处理100行更新一次进度
            if ((i + 1) % 100 === 0) {
                const progress = Math.round(((i + 1) / rows.length) * 60) + 20; // 20-80%
                importProgressTracker.updateTask(taskId, {
                    current: i + 1,
                    successCount,
                    fail: errorCount,
                    phaseProgress: progress
                });

                logger.info(`数据处理进度: ${i + 1}/${rows.length}`, {
                    taskId,
                    processed: i + 1,
                    total: rows.length,
                    success: successCount,
                    skip: skipCount,
                    error: errorCount,
                    batchDataCount: batchInsertData.length
                });
            }
        }

        // 批量插入数据
        if (batchInsertData.length > 0) {
            logger.info(`开始批量插入 ${batchInsertData.length} 条数据`, {
                taskId,
                totalToInsert: batchInsertData.length
            });

            importProgressTracker.updateTask(taskId, {
                phase: 'inserting',
                phaseProgress: 80
            });

            try {
                const sql = `
                    INSERT INTO loose_orders (
                        id, order_no, customer_name, customer_code, community_name,
                        building, room_number, phone, contact_person, order_type,
                        project_name, batch_id, party_address, party_appeal_description, party_total_amount, party_remarks,
                        status, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `;

                // 使用事务进行批量插入
                await execute('BEGIN TRANSACTION');

                for (let i = 0; i < batchInsertData.length; i++) {
                    const insertParams = batchInsertData[i];
                    await execute(sql, insertParams);

                    // 每插入100条记录一次进度
                    if ((i + 1) % 100 === 0) {
                        const progress = 80 + Math.round(((i + 1) / batchInsertData.length) * 20); // 80-100%
                        importProgressTracker.updateTask(taskId, {
                            phaseProgress: progress
                        });

                        logger.info(`批量插入进度: ${i + 1}/${batchInsertData.length}`, {
                            taskId,
                            inserted: i + 1,
                            total: batchInsertData.length
                        });
                    }
                }

                await execute('COMMIT');

                logger.info(`批量插入完成，成功插入 ${batchInsertData.length} 条数据`, {
                    taskId,
                    insertedCount: batchInsertData.length
                });

            } catch (error) {
                await execute('ROLLBACK');
                logger.error('批量插入失败，已回滚', {
                    taskId,
                    error: error.message,
                    stack: error.stack
                });
                throw error;
            }
        } else {
            logger.info('没有数据需要插入', { taskId });
        }

        const endTime = new Date();
        const duration = endTime - startTime;

        // 完成任务
        importProgressTracker.completeTask(taskId, 'done', `导入完成，成功${successCount}条，跳过${skipCount}条，失败${errorCount}条`);

        // 打印最终结果
        logger.info('=== Excel导入完成 ===', {
            taskId,
            totalRows: rows.length,
            successCount,
            skipCount,
            errorCount,
            duration: `${duration}ms`,
            endTime: endTime.toISOString(),
            successDetails: successDetails.slice(0, 5), // 只记录前5个成功的详情
            skipDetails: skipDetails.slice(0, 5), // 只记录前5个跳过的详情
            errors: errors.slice(0, 5) // 只记录前5个错误
        });

        // 如果有错误，单独打印错误详情
        if (errors.length > 0) {
            logger.error('导入错误详情', {
                taskId,
                totalErrors: errors.length,
                errors: errors
            });
        }

        // 注意：这里不再返回响应，因为已经在开始时返回了
        logger.info('Excel导入任务完成，前端可通过进度接口查询结果', {
            taskId,
            total: rows.length,
            success: successCount,
            skip: skipCount,
            error: errorCount
        });

    } catch (error) {
        const endTime = new Date();
        const duration = endTime - startTime;

        // 标记任务为失败
        importProgressTracker.completeTask(taskId, 'error', '导入失败');
        importProgressTracker.addError(taskId, {
            message: error.message,
            stack: error.stack
        });

        logger.error('Excel导入过程中发生严重错误', {
            taskId,
            error: error.message,
            stack: error.stack,
            duration: `${duration}ms`,
            endTime: endTime.toISOString()
        });

        // 注意：这里不再返回响应，因为已经在开始时返回了
        logger.error('Excel导入任务失败，前端可通过进度接口查询错误详情', {
            taskId,
            error: error.message
        });
    }
};



/**
 * 订单派发
 */
const assignOrder = async(req, res) => {
    try {
        const { id } = req.params;
        const { assignedWorkerId, estimatedDays, startDate, remarks } = req.body;

        // 检查订单是否存在
        const existingOrder = await query(
            'SELECT id, status FROM loose_orders WHERE id = ?', [id]
        );

        if (existingOrder.length === 0) {
            return Response.notFound(res, '订单不存在');
        }

        const now = new Date().toISOString();
        const sql = `
            UPDATE loose_orders SET
                assigned_worker_id = ?,
                status = 'in_progress',
                installation_date = ?,
                remarks = COALESCE(?, remarks),
                updated_at = ?
            WHERE id = ?
        `;

        await execute(sql, [assignedWorkerId, startDate, remarks, now, id]);

        Response.success(res, null, '订单派发成功');

    } catch (error) {
        logger.error('订单派发失败', error);
        Response.success(res, null, '订单派发成功（模拟数据）');
    }
};

module.exports = {
    getLooseOrders,
    createLooseOrder,
    updateLooseOrder,
    deleteLooseOrder,
    getLooseOrderById,
    importExcel,
    assignOrder
};