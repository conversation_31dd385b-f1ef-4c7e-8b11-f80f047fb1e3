// 导入进度跟踪工具
class ImportProgressTracker {
    constructor() {
        this.progressMap = new Map();
    }

    // 创建新的导入任务
    createTask(batchId, totalRows) {
        const task = {
            batchId,
            totalRows,
            processedRows: 0,
            successCount: 0,
            skipCount: 0,
            errorCount: 0,
            status: 'processing', // processing, completed, failed
            startTime: new Date().toISOString(),
            endTime: null,
            currentPhase: 'reading', // reading, processing, inserting
            phaseProgress: 0,
            errors: [],
            successDetails: [],
            skipDetails: []
        };

        this.progressMap.set(batchId, task);
        return task;
    }

    // 更新进度
    updateProgress(batchId, updates) {
        const task = this.progressMap.get(batchId);
        if (task) {
            Object.assign(task, updates);
            if (updates.processedRows !== undefined) {
                task.phaseProgress = Math.round((task.processedRows / task.totalRows) * 100);
            }
        }
        return task;
    }

    // 获取进度
    getProgress(batchId) {
        return this.progressMap.get(batchId);
    }

    // 完成任务
    completeTask(batchId, status = 'completed') {
        const task = this.progressMap.get(batchId);
        if (task) {
            task.status = status;
            task.endTime = new Date().toISOString();
            task.currentPhase = 'completed';
            task.phaseProgress = 100;
        }
        return task;
    }

    // 添加错误
    addError(batchId, error) {
        const task = this.progressMap.get(batchId);
        if (task) {
            task.errors.push(error);
            if (task.errors.length > 50) { // 限制错误数量
                task.errors = task.errors.slice(-50);
            }
        }
    }

    // 添加成功记录
    addSuccess(batchId, detail) {
        const task = this.progressMap.get(batchId);
        if (task) {
            task.successDetails.push(detail);
            if (task.successDetails.length > 20) { // 限制成功记录数量
                task.successDetails = task.successDetails.slice(-20);
            }
        }
    }

    // 添加跳过记录
    addSkip(batchId, detail) {
        const task = this.progressMap.get(batchId);
        if (task) {
            task.skipDetails.push(detail);
            if (task.skipDetails.length > 20) { // 限制跳过记录数量
                task.skipDetails = task.skipDetails.slice(-20);
            }
        }
    }

    // 清理旧任务（超过1小时的任务）
    cleanupOldTasks() {
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
        for (const [batchId, task] of this.progressMap.entries()) {
            if (task.endTime && new Date(task.endTime) < oneHourAgo) {
                this.progressMap.delete(batchId);
            }
        }
    }

    // 获取所有任务
    getAllTasks() {
        return Array.from(this.progressMap.values());
    }

    // 删除任务
    deleteTask(batchId) {
        return this.progressMap.delete(batchId);
    }
}

// 创建全局实例
const progressTracker = new ImportProgressTracker();

// 定期清理旧任务
setInterval(() => {
    progressTracker.cleanupOldTasks();
}, 30 * 60 * 1000); // 每30分钟清理一次

module.exports = progressTracker;