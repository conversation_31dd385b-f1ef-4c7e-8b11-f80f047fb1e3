#!/usr/bin/env node

/**
 * Excel导入日志测试脚本
 * 用于演示和测试日志功能
 */

const fs = require('fs');
const path = require('path');

// 创建logs目录
const LOG_DIR = path.join(__dirname, '../logs');
if (!fs.existsSync(LOG_DIR)) {
    fs.mkdirSync(LOG_DIR, { recursive: true });
}

const LOG_FILE = path.join(LOG_DIR, 'app.log');

/**
 * 生成测试日志
 */
function generateTestLogs() {
    const now = new Date();
    const timestamp = now.toISOString().replace('T', ' ').substring(0, 19);
    const batchId = 'test-batch-' + Date.now();

    const logs = [
        `${timestamp} info: === 开始Excel导入 === {"batchId":"${batchId}","fileName":"测试工单数据.xlsx","fileSize":2097152,"userId":"admin123","startTime":"${now.toISOString()}"}`,
        `${timestamp} info: 文件上传成功 {"batchId":"${batchId}","filePath":"./uploads/excel/1234567890_测试工单数据.xlsx","originalName":"测试工单数据.xlsx","size":2097152}`,
        `${timestamp} info: 开始读取Excel文件 {"batchId":"${batchId}"}`,
        `${timestamp} info: Excel文件读取完成 {"batchId":"${batchId}","totalRows":1001,"sheetName":"Sheet1"}`,
        `${timestamp} info: Excel文件结构分析 {"batchId":"${batchId}","headers":["工单号","工单类型","工单状态","工单来源","工单标题","工单内容","创建人","创建时间"],"dataRows":1000,"headerCount":8}`,
        `${timestamp} info: Excel文件验证通过，开始处理数据 {"batchId":"${batchId}","totalRows":1000,"requiredColumns":["工单号","工单类型","工单状态","工单来源","工单标题","工单内容","创建人","创建时间"]}`,
        `${timestamp} info: 处理第2行数据 {"batchId":"${batchId}","rowNumber":2,"rowData":["WO001","维修","待处理","系统","设备故障","设备无法启动","张三","2025-08-08"]}`,
        `${timestamp} info: 第2行数据映射完成 {"batchId":"${batchId}","rowNumber":2,"orderNo":"WO001","orderType":"维修","status":"待处理","creator":"张三","project":"系统"}`,
        `${timestamp} info: 第2行准备插入数据库 {"batchId":"${batchId}","rowNumber":2,"orderNo":"WO001","id":"uuid-123","address":"系统 - 设备","appealDescription":"设备无法启动"}`,
        `${timestamp} info: 第2行插入成功 {"batchId":"${batchId}","rowNumber":2,"orderNo":"WO001","id":"uuid-123"}`,
        `${timestamp} info: 处理第3行数据 {"batchId":"${batchId}","rowNumber":3,"rowData":["WO002","安装","进行中","项目","新设备安装","安装新设备","李四","2025-08-08"]}`,
        `${timestamp} info: 第3行数据映射完成 {"batchId":"${batchId}","rowNumber":3,"orderNo":"WO002","orderType":"安装","status":"进行中","creator":"李四","project":"项目"}`,
        `${timestamp} info: 第3行跳过: 工单号已存在 {"batchId":"${batchId}","rowNumber":3,"orderNo":"WO002"}`,
        `${timestamp} info: 处理第4行数据 {"batchId":"${batchId}","rowNumber":4,"rowData":["WO003","","待处理","系统","","","王五","2025-08-08"]}`,
        `${timestamp} error: 第4行导入失败 {"batchId":"${batchId}","rowNumber":4,"orderNo":"WO003","error":"工单类型不能为空","stack":"Error: 工单类型不能为空\\n    at validateOrderType"}`,
        `${timestamp} info: 导入进度: 100/1000 {"batchId":"${batchId}","processed":100,"total":1000,"success":95,"skip":3,"error":2}`,
        `${timestamp} info: 导入进度: 200/1000 {"batchId":"${batchId}","processed":200,"total":1000,"success":190,"skip":6,"error":4}`,
        `${timestamp} info: 导入进度: 300/1000 {"batchId":"${batchId}","processed":300,"total":1000,"success":285,"skip":9,"error":6}`,
        `${timestamp} info: 导入进度: 400/1000 {"batchId":"${batchId}","processed":400,"total":1000,"success":380,"skip":12,"error":8}`,
        `${timestamp} info: 导入进度: 500/1000 {"batchId":"${batchId}","processed":500,"total":1000,"success":475,"skip":15,"error":10}`,
        `${timestamp} info: 导入进度: 600/1000 {"batchId":"${batchId}","processed":600,"total":1000,"success":570,"skip":18,"error":12}`,
        `${timestamp} info: 导入进度: 700/1000 {"batchId":"${batchId}","processed":700,"total":1000,"success":665,"skip":21,"error":14}`,
        `${timestamp} info: 导入进度: 800/1000 {"batchId":"${batchId}","processed":800,"total":1000,"success":760,"skip":24,"error":16}`,
        `${timestamp} info: 导入进度: 900/1000 {"batchId":"${batchId}","processed":900,"total":1000,"success":855,"skip":27,"error":18}`,
        `${timestamp} info: 导入进度: 1000/1000 {"batchId":"${batchId}","processed":1000,"total":1000,"success":950,"skip":30,"error":20}`,
        `${timestamp} info: === Excel导入完成 === {"batchId":"${batchId}","totalRows":1000,"successCount":950,"skipCount":30,"errorCount":20,"duration":"63000ms","endTime":"${new Date(now.getTime() + 63000).toISOString()}","successDetails":[{"rowNumber":2,"orderNo":"WO001","id":"uuid-123","orderType":"维修","creator":"张三"},{"rowNumber":5,"orderNo":"WO004","id":"uuid-124","orderType":"维护","creator":"赵六"}],"skipDetails":[{"rowNumber":3,"orderNo":"WO002","reason":"工单号已存在"},{"rowNumber":8,"orderNo":"WO007","reason":"工单号已存在"}],"errors":["第4行: 工单类型不能为空","第7行: 创建人不能为空"]}`,
        `${timestamp} error: 导入错误详情 {"batchId":"${batchId}","totalErrors":20,"errors":["第4行: 工单类型不能为空","第7行: 创建人不能为空","第12行: 工单号格式错误","第15行: 日期格式错误","第18行: 必填字段缺失"]}`
    ];

    return logs;
}

/**
 * 写入测试日志到文件
 */
function writeTestLogs() {
    const logs = generateTestLogs();
    const logContent = logs.join('\n') + '\n';

    try {
        fs.writeFileSync(LOG_FILE, logContent, 'utf8');
        console.log('✅ 测试日志已生成到:', LOG_FILE);
        console.log(`📝 生成了 ${logs.length} 条测试日志`);
        return true;
    } catch (error) {
        console.error('❌ 写入测试日志失败:', error.message);
        return false;
    }
}

/**
 * 显示测试说明
 */
function showTestInstructions() {
    console.log('\n📖 测试说明:');
    console.log('='.repeat(50));
    console.log('1. 已生成包含Excel导入过程的测试日志');
    console.log('2. 现在可以使用日志查看工具进行测试');
    console.log('\n💡 测试命令:');
    console.log('  node scripts/view_import_logs.js stats          # 查看统计信息');
    console.log('  node scripts/view_import_logs.js list           # 查看批次列表');
    console.log('  node scripts/view_import_logs.js detail test-batch-<timestamp> # 查看详细日志');
    console.log('  node scripts/view_import_logs.js errors         # 查看错误日志');
    console.log('\n🔍 测试数据包含:');
    console.log('  - 1个完整的导入批次');
    console.log('  - 1000行数据处理');
    console.log('  - 950条成功记录');
    console.log('  - 30条跳过记录');
    console.log('  - 20条错误记录');
    console.log('  - 63000ms处理耗时');
}

/**
 * 主函数
 */
function main() {
    console.log('🧪 Excel导入日志测试工具');
    console.log('='.repeat(50));

    // 生成测试日志
    if (writeTestLogs()) {
        showTestInstructions();
    }
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = {
    generateTestLogs,
    writeTestLogs
};