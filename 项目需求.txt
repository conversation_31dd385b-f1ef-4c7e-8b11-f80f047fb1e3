这是一个工程管理系统项目，项目需求如下：
1. 工程管理系统前端


技术栈设计如下：
   - 框架：Vue3 + TypeScript
   - 状态管理：Pinia
   - UI 组件库：Element Plus
   - 路由管理：Router
   - 网络请求：Axios
   - 构建工具：Vite
   - 代码规范：ESLint + Prettier

功能如下：（一个公司物料编码可以对应多个甲方编码）
1 首页仪表板
    1-1 工程物料统计 (MaterialStatistics.vue)
        1 显示在工程中还未使用的物料 有多少项，总价多少
        2 按总价由多到少显示每一项未使用物料的名称，数量，总价
        3 显示在工程中已经使用的物料 有多少项，总价多少
        4 按总价由多到少显示每一项已经使用物料的名称，数量，总价
        5 显示总共领出物料多少项 总价多少
    1-2 工程进度统计 (ProjectProgressStatistics.vue)
        1 显示 未开始工程多少个，在建工程多少个，暂停工程多少个，完成工程多少个
        2 按最新进度排序显示每一项工程的名称，状态
    1-3 散单物料统计 (LooseMaterialStatistics.vue)
        1 显示上一次平帐时间，离上一次平账时间有多少天
        2 显示有多少还未使用的分类为甲料的物料（多少项，数量，总价），已经使用的分类为甲料的物料有多少（多少项，数量，总价）
        3 显示甲料的物料还有多少在仓库，有多少在工人师傅上
    1-4 散单情况统计 (LooseOrderStatistics.vue)
        1 显示散单订单统计信息
        2 显示订单状态分布
        3 显示订单分类统计

2 仓库管理
    2-1 物料列表 (MaterialList.vue)
        1 显示公司物料编码，甲方编码（显示1个编号，多出来的以...显示， 点击显示列表），标签，物料分类（甲料，商品，辅料），名称，型号，规格，单位，数量，状态，操作
        2 有搜索栏，可以按关键字搜索
        3 操作行有 编辑，增加不同型号规格，两个按钮
    2-2 领料申请 (MaterialApply.vue)
        1 填写领取物料列表（包括数量），申领用途，关联订单，领料人
        2 有保存和打印按钮
    2-3 甲料入库 (MaterialInbound.vue)
        1 填写公司物料编码，数量
        2 有保存和打印按钮
    2-4 物料退仓 (MaterialReturn.vue)
        1 填写公司物料编码，数量
        2 有保存和打印按钮
    2-5 辅料采购 (AuxiliaryPurchase.vue)
        1 填写公司物料编码，名称，型号，规格，单价，数量
        2 有保存和打印按钮
    2-6 商品入库 (ProductInbound.vue)
        1 填写公司物料编码（如果没有可以为空），甲料编码，名称，型号，规格，单位，数量
        2 有保存和打印按钮
    2-7 进出记录 (MaterialRecords.vue)
        1 显示记录时间，订单分类，订单，类型（领料，甲料入库，商品入库），变更前数量，变动后数量，领料人
    2-8 物料价格 (MaterialPrice.vue)
        1 显示公司 公司物料编码，甲方编码，名称，型号，规格，单位，单价，操作（修改价格按钮）
        2 修改价格按钮点击弹窗修改价格
    2-9 库存预警 (StockWarning.vue)
        1 显示 公司物料编码，甲方编码（显示按钮， 点击显示列表），物料分类（甲料，商品，辅料），名称，型号，规格，单位，当前数量，预警数量，操作（修改预警数量）
        2 点击 修改预警数量 弹窗修改预警数量
3 散户订单
    3-1 订单列表 (OrderList.vue)
        1 显示 甲方订单号，姓名，用户编号，小区名称，楼橦，房号，电话，联系人，订单分类（一次挂表，二次挂表，一次安装，二次安装，售后，单项工程），单项工程名称，订单状态，安装时间，备注， 跟进师傅
        2 有搜索栏，可以按甲方订单号，户名，地址，电话，联系人，订单分类，订单状态，跟进师傅 搜索
    3-2 工单录入 (OrderAssign.vue)
        1 填写 姓名，用户编号，小区名称，楼橦，房号，电话，联系人，订单分类（一次挂表，二次挂表，一次安装，二次安装，售后，单项工程），单项工程名称，备注
    3-3 工单结算 (OrderSettlement.vue)
        1 显示工单结算信息，包括客户信息、结算内容、物料使用等
        2 有保存和打印按钮
    3-4 维修结算 (RepairSettlement.vue)
        1 显示维修结算列表，包括维修单号、客户信息、维修内容、状态等
        2 支持搜索和筛选
    3-5 核对平料 (MaterialVerification.vue)
        1 核对物料使用情况，包括物料清单、使用数量等
        2 有保存和打印按钮
    3-6 安检录入 (SafetyInspectionEntry.vue)
        1 填写安检信息，包括安检内容、结果等
        2 有保存和打印按钮
    3-7 安检列表 (SafetyInspectionList.vue)
        1 显示安检列表，包括安检信息、结果等
        2 支持搜索和筛选
    3-8 项目成本汇总表 (ProjectCostSummaryTable.vue)
        1 显示项目成本汇总信息
        2 支持导出和打印
4 工程订单
    4-1 工程列表 (ProjectList.vue)
        1 显示 甲方订单号，工程名称，工程地址，工程状态，预估开始时间，预估结束时间，操作（详情按钮）
        2 有搜索栏，可以按甲方订单号，工程名称，工程地址，工程状态，预估开始时间，预估结束时间，物料编码搜索
    4-2 甲方派单 (PartyDispatch.vue)
        1 填写 甲方订单号，工程名称，预估开始时间，预估结束时间
    4-3 工种设置 (WorkTypeSetting.vue)
        1 显示 工种名称，工种描述
    4-4 工程开始 (ProjectStart.vue)
        1 选择 工程
    4-5 工程推进 (ProjectProgress.vue)
        1 选择 工程，填写 完成进度情况，人员列表（工种，工价，天数），物料使用列表（公司物料编码，数量）
    4-6 工程暂停 (ProjectPause.vue)
        1 选择 工程，填写 暂停原因
    4-7 工程完成 (ProjectFinish.vue)
        1 选择 工程，填写 人员列表（工种，工价，天数）
5 员工管理
    5-1 员工列表 (EmployeeList.vue)
        1 显示 员工姓名，工种，工价，操作（修改按钮）
        2 点击 修改按钮 弹窗修改工种，工价
    5-2 工种设置 (WorkTypeSetting.vue)
        1 显示 工种名称，工种描述
        2 有增加，编辑，导入excel 按钮
    5-3 绩效设置 (PerformanceSetting.vue)
        1 显示 商品售卖 绩效参数 
        2 显示 散单+订单分类 绩效参数 
6 系统设置
    6-1 用户管理 (UserManagement.vue)
        1 显示 用户名，角色，手机号，微信号，操作（修改按钮）
    6-2 新增用户 (AddUser.vue)
        1 填写 用户名，角色，手机号
    6-3 权限管理 (PermissionManagement.vue)
        1 显示 权限列表，权限名称，权限描述，操作（新增，修改按钮）
        2 显示 角色列表，角色名称，角色描述，操作（新增，修改按钮）
        3 在角色列表中可以给角色分配权限
    6-4 系统日志 (SystemLog.vue)
        1 显示 操作时间，操作人，操作内容，操作结果，操作IP
        2 有搜索栏，可以按操作时间，操作人，操作内容，操作结果，操作IP 搜索
    6-5 基础数据导入 (DataImport.vue)
        1 选择 导入类型（物料） ，excel 文件, 导入按钮

注意：详细的API接口规划设计请参考《API规划设计.txt》文档。

